import pandas as pd

# 读取Excel文件
file_path = '新的执法记录仪日志.xlsx'
df = pd.read_excel(file_path)

# 将“日志时间”列转换为日期时间格式
df['日志时间'] = pd.to_datetime(df['日志时间'])

# 按“操作用户名”和“操作对象”排序，然后按“日志时间”排序
df = df.sort_values(by=['操作用户名', '操作对象', '日志时间'])

# 删除在1分钟内重复的记录
df['时间差'] = df.groupby(['操作用户名', '操作对象'])['日志时间'].diff().dt.total_seconds()
df_filtered = df[(df['时间差'].isna()) | (df['时间差'] > 60)]

# 删除辅助列“时间差”
df_filtered = df_filtered.drop(columns=['时间差'])

# 保存为新的Excel文件
output_file_path = '新的执法记录仪日志每分钟1条.xlsx'
df_filtered.to_excel(output_file_path, index=False)

print(f"处理完成，新文件已保存为 {output_file_path}")