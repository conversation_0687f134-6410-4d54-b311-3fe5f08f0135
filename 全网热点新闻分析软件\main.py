#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全网热点新闻分析系统 - 主程序入口
支持爬取、分析多个平台的热点新闻数据
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication
from ui import HotNewsMainWindow

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("Main")

def main():
    """程序主入口"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 设置应用属性
        app.setApplicationName("全网热点新闻分析系统")
        app.setApplicationVersion("1.0.0")
        
        # 创建并显示主窗口
        window = HotNewsMainWindow()
        window.show()
        
        # 运行应用
        sys.exit(app.exec_())
    except Exception as e:
        logger.error(f"程序出错: {str(e)}")

if __name__ == "__main__":
    main() 