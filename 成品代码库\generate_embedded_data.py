import os
import base64
import zlib

def convert_txt_to_py():
    """将tzjkm.txt转换为嵌入式Python模块"""
    if not os.path.exists("tzjkm.txt"):
        print("错误：找不到tzjkm.txt文件")
        return
    
    # 读取文件内容
    with open("tzjkm.txt", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 压缩数据
    compressed_data = zlib.compress(content.encode('utf-8'), level=9)
    
    # 转换为base64
    base64_data = base64.b64encode(compressed_data).decode('utf-8')
    
    # 生成Python模块
    module_content = f'''# 这是自动生成的数据模块，请勿手动修改
import base64
import zlib

def get_embedded_data():
    """获取嵌入的数据内容"""
    compressed_data = base64.b64decode("{base64_data}")
    return zlib.decompress(compressed_data).decode('utf-8')
'''
    
    # 写入Python文件
    with open("embedded_data.py", 'w', encoding='utf-8') as f:
        f.write(module_content)
    
    print("数据模块生成完成！")

if __name__ == "__main__":
    convert_txt_to_py() 