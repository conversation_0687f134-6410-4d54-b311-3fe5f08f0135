{"name": "回复高冷女神", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "c2d39201-390a-4df0-8252-4eea53eb896c", "name": "When chat message received", "webhookId": "d542bc47-61f9-4d21-8aae-9ff765dfffbe"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17-thinking", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [220, 200], "id": "9bae8f9d-65f3-4aa9-8c5e-a7677be12d66", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "OgN8N8yxscAZwEGQ", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"options": {"systemMessage": "=你是一个情场高手、恋爱专家，你面对的是一个高冷的女神，她经常已读不回，你要根据对方发过来的话“{{ $json.chatInput }}”，生成一个回复，不能让对方感觉出来是AI回复的，要自然且能够拿捏住对方。你只需要直接告诉我应该和对方说什么，不需要任何解释。 "}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [220, 0], "id": "2ea69152-046c-45a8-952a-f2a44eee13ab", "name": "AI Agent"}], "pinData": {}, "connections": {"Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1fe9a10f-bd13-4237-838e-ada7f4432db7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "da293d8bc9d55da5a991575f3ab795aa37c2d9ef15407feeb5edfc2322d6467d"}, "id": "1STdsprgbSaUIjcl", "tags": []}