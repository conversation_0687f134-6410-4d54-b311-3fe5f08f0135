{"name": "Google search", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "=# Understand the question of the user\n# Use google Search tool to search the web to get up to date data to ground findings\n- Must pass in current time: {{ $now.format('yyyy-MM-dd') }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1040, 120], "id": "57eafde7-b262-45bc-8f9c-39fc433365a5", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [940, 300], "id": "a2c1b28f-c031-4e75-82d5-45df00c481c3", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "CHnSiUD969MD2uG1", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"toolDescription": "=Use this tool to search the web to get up to date informations. Return all response. current time : {{$now.format('yyyy-MM-dd')}}", "method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"{{ $fromAI(\"searchQuery\", \"search query + current time to ground use google search\")}}\"\n        }\n      ]\n    }\n  ],\n  \"tools\": [\n    {\n      \"google_search\": {}\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [2080, 260], "id": "568ef68c-bd2a-41d2-a4d7-a658f0d8d0b7", "name": "Google Search Tool1"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1400, 300], "id": "6110c8f1-6a6b-4eed-9738-a157b6739f20", "name": "Simple Memory1"}, {"parameters": {"content": "# Search Grounding tool, response in summary\n\nEnglish:\nThis workflow uses an n8n AI Agent with LangChain components to provide search-grounded responses. Unlike the raw HTTP request method, the agent handles the tool invocation and summarizes the information for the user.\n\n*   **AI Agent (`AI Agent`)**:\n    *   **Input**: Takes user input via `{{ $json.chatInput }}` (presumably from a chat trigger, though not directly connected in this view). Mock data example: `{\"chatInput\": \"Latest news on renewable energy?\"}`.\n    *   **System Message**: Guides the agent to understand the user's question, use the Google Search tool for up-to-date data, and ground its findings. It explicitly requires passing the current time (`{{ $now.format('yyyy-MM-dd') }}`) to the search tool.\n    *   **Connected Components**:\n        *   **Language Model**: `Google Gemini Chat Model`.\n        *   **Tool**: `Google Search Tool1`.\n        *   **Memory**: `Simple Memory1`.\n    *   **Purpose**: Orchestrates the process, decides when to use tools, and synthesizes the final response.\n\n*   **Google Gemini Chat Model (`Google Gemini Chat Model`)**:\n    *   **Configuration**: Uses `models/gemini-2.5-flash-preview-04-17`.\n    *   **Purpose**: Provides the core language understanding, reasoning, and generation capabilities for the AI Agent.\n\n*   **Google Search Tool (`Google Search Tool1`)**:\n    *   **Description**: Tells the agent when to use this tool (for up-to-date info, current time is passed).\n    *   **Mechanism**: It's an n8n HTTP Request Tool configured to call the Gemini API (`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent`).\n    *   **Body**: The crucial part is `\"text\": \"{{ $fromAI(\"searchQuery\", \"search query + current time to ground use google search\")}}\"` which takes the search query formulated by the AI Agent. It also includes `\"tools\": [{\"google_search\": {}}]` in its own call to Gemini, effectively making Gemini perform the search via its native tool integration using the AI Agent's query.\n    *   **Purpose**: Enables the AI Agent to access and incorporate real-time web information into its response.\n    *   **Credentials**: Requires a `httpQueryAuth` credential (e.g., named \"outGemini\") for authentication with the Gemini API.\n\n*   **Simple Memory (`Simple Memory1`)**:\n    *   **Purpose**: Stores conversation history (`BufferWindow`), allowing the agent to maintain context across multiple turns of interaction within a single workflow execution (if triggered sequentially for a conversation).\n\n**Overall Flow**:\nUser asks a question (via input) -> Agent decides if search is needed based on system prompt -> If yes, Agent formulates a search query and calls `Google Search Tool1` -> `Google Search Tool1` calls Gemini API with the formulated query and search enabled -> Gemini performs the search and returns results to the Agent -> Agent synthesizes information from search results and memory, and provides a summarized, grounded answer.\n\n---\n\n繁體中文:\n# 搜尋佐證工具，摘要式回應\n\n此工作流程使用 n8n AI 代理與 LangChain 組件來提供具有搜尋佐證的回應。與原始 HTTP 請求方法不同，代理會處理工具的調用並為使用者摘要資訊。\n\n*   **AI 代理 (`AI Agent`)**:\n    *   **輸入**: 透過 `{{ $json.chatInput }}` 接收使用者輸入（推測來自聊天觸發器，儘管在此視圖中未直接連接）。模擬資料範例：`{\"chatInput\": \"再生能源的最新消息是什麼？\"}`。\n    *   **系統訊息**: 指導代理理解使用者的問題，使用 Google 搜尋工具獲取最新數據，並佐證其發現。它明確要求將目前時間 (`{{ $now.format('yyyy-MM-dd') }}`) 傳遞給搜尋工具。\n    *   **連接組件**:\n        *   **語言模型**: `Google Gemini Chat Model`。\n        *   **工具**: `Google Search Tool1`。\n        *   **記憶體**: `Simple Memory1`。\n    *   **目的**: 協調整個過程，決定何時使用工具，並合成最終回應。\n\n*   **Google Gemini 聊天模型 (`Google Gemini Chat Model`)**:\n    *   **設定**: 使用 `models/gemini-2.5-flash-preview-04-17`。\n    *   **目的**: 為 AI 代理提供核心語言理解、推理和生成能力。\n\n*   **Google 搜尋工具 (`Google Search Tool1`)**:\n    *   **描述**: 告知代理何時使用此工具（用於獲取最新資訊，並傳遞目前時間）。\n    *   **機制**: 這是一個 n8n HTTP 請求工具，配置為呼叫 Gemini API (`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent`)。\n    *   **主體**: 關鍵部分是 `\"text\": \"{{ $fromAI(\"searchQuery\", \"search query + current time to ground use google search\")}}\"`，它接收由 AI 代理制定的搜尋查詢。在其對 Gemini 的呼叫中，它還包含 `\"tools\": [{\"google_search\": {}}]`，有效地使 Gemini 透過其原生工具整合，利用 AI 代理的查詢執行搜尋。\n    *   **目的**: 使 AI 代理能夠存取並將即時網路資訊納入其回應中。\n    *   **憑證**: 需要 `httpQueryAuth` 憑證（例如，名為「outGemini」）才能向 Gemini API 進行身份驗證。\n\n*   **簡單記憶體 (`Simple Memory1`)**:\n    *   **目的**: 儲存對話歷史（`BufferWindow`），使代理能夠在單一工作流程執行中（如果針對同一對話依序觸發）跨多次互動保持上下文。\n\n**整體流程**:\n使用者提問（透過輸入）-> 代理根據系統提示決定是否需要搜尋 -> 如果需要，代理制定搜尋查詢並呼叫 `Google Search Tool1` -> `Google Search Tool1` 呼叫啟用搜尋功能的 Gemini API 並傳遞制定的查詢 -> Gemini 執行搜尋並將結果返回給代理 -> 代理綜合搜尋結果和記憶體中的資訊，並提供摘要且有佐證的答案。", "height": 1640, "width": 2540, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 420], "id": "8e79fc35-9966-4be6-8c40-625b04bf1b1d", "name": "Sticky Note25"}, {"parameters": {"content": "## Workflow Section: AI Agent & Search Grounding\n\nEnglish:\nThis part of the workflow demonstrates using an n8n AI Agent powered by Google Gemini, integrated with memory and a search tool (leveraging Gemini's native search) to provide grounded responses.\n\nThis pattern is useful for building intelligent assistants that can access up-to-date external information.\n\n---\n\n繁體中文:\n## 工作流程部分：AI 代理與搜尋佐證\n\n此工作流程部分展示如何使用由 Google Gemini 提供支援的 n8n AI 代理，並整合記憶體和搜尋工具（利用 Gemini 的原生搜尋功能），以提供有佐證的回應。\n\n此模式對於建構能夠存取最新外部資訊的智慧助理非常有用。", "height": 2060, "width": 600}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "7fa9d987-ee57-4385-8138-351fe25cbfa2", "name": "<PERSON><PERSON> (Section Header)"}, {"parameters": {"content": "# Grounding with Google Search (With Tool USE)", "height": 80, "width": 2540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 0], "id": "a90382a6-ac49-42d2-bc9f-5a57676361ee", "name": "Sticky Note (Form Trigger)12"}, {"parameters": {"content": "# Workflow", "height": 300, "width": 2540, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 100], "id": "524e3490-beea-4a02-8952-35aca5db5639", "name": "Sticky Note (<PERSON> Trigger)13"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [760, 120], "id": "3657a135-f5f6-477b-be1e-885b02fd9896", "name": "When chat message received", "webhookId": "122e58e2-715e-4420-a706-9d6e1f2b00e5"}], "pinData": {}, "connections": {"Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Google Search Tool1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory1": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d6993a56-66fe-4030-aea8-a18c7433e970", "meta": {"templateCredsSetupCompleted": true, "instanceId": "04695fa805d662a7ca811a5d9568e6cc080c07e6ec9f81360f8146eccb701cc6"}, "id": "oAZRZ5To6wKhf4Va", "tags": []}