app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 微情报整合
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.16@77274df8fe2632cac66bfd153fcc75aa5e96abbe92b5c611b8984ad9f4cd4457
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: document-extractor
      id: 1748680340780-source-1748680365836-target
      selected: true
      source: '1748680340780'
      sourceHandle: source
      target: '1748680365836'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: document-extractor
        targetType: llm
      id: 1748680365836-source-1748680444163-target
      selected: true
      source: '1748680365836'
      sourceHandle: source
      target: '1748680444163'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1748680444163-source-1748793932972-target
      selected: true
      source: '1748680444163'
      sourceHandle: source
      target: '1748793932972'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions: []
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          label: files
          max_length: 48
          options: []
          required: true
          type: file
          variable: files
      height: 90
      id: '1748680340780'
      position:
        x: 80
        y: 280.484283497614
      positionAbsolute:
        x: 80
        y: 280.484283497614
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_array_file: false
        selected: false
        title: 文档提取器
        type: document-extractor
        variable_selector:
        - '1748680340780'
        - files
      height: 92
      id: '1748680365836'
      position:
        x: 384
        y: 280.484283497614
      positionAbsolute:
        x: 384
        y: 280.484283497614
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1748680365836'
          - text
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: doubao-1-5-pro-256k-250115
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 381092d8-bf79-4646-b780-cc50dbcdb98c
          role: system
          text: "# 角色\n你是一位专业的风险分析与情报撰写专家。\n\n# 任务\n你的任务是根据我提供的{{#1748680365836.text#}}中的内容，自动生成一份结构化、精炼的“微情报”报告。你需要在没有外部范例文件的情况下，严格遵循下述的结构、内容和风格要求。特别注意，你需要从原始素材各段落的初步标题中提炼总结出最终的微情报标题。\n\
            \n# 输入\n1.  **{{#1748680365836.text#}}**: 这是一个包含关于特定主题（事件）的多种风险信息、背景、数据和建议的文本文件。**重要提示：此文件中，每段话的第一句话通常是该段内容的初步拟定标题或核心概括。**\
            \ 你需要从中提取核心内容并进行整合。\n\n# 输出要求\n生成的“微情报”报告必须严格遵循以下结构和要求：\n\n1.  **标题**:\n\
            \    *   **生成逻辑**：\n        *   首先，识别并理解{{#1748680365836.text#}}中各段落首句的“初步拟定标题”。\n\
            \        *   然后，对这些初步标题进行综合分析、提炼和总结，抓住它们共同指向的核心主题和关键事件。\n        *   最终形成的标题应高度概括整个微情报的核心内容，并能反映出这些初步标题的共同指向。\n\
            \    *   **格式**：通常采用**“XXX风险提示”**或**“XXX风险预警”**的格式。XXX部分应基于上述提炼总结得出。\n\
            \    *   **内容**：应能准确概括{{#1748680365836.text#}}中内容的核心主题。如果主题包含多个关键词。\n\
            \    *   **示例风格**（基于提炼总结后）：“‘XX风险提示”。\n\n2.  **风险基本情况/背景**:\n    *   位置：紧随标题之后。\n\
            \    *   内容：\n        *   开篇应点明主题事件（这通常可以从最终确定的标题或原始素材的初步标题中得到印证），并可简要提及该事件的特殊性或吸引力。\n\
            \        *   如果{{#1748680365836.text#}}内容中提及重要的、与当前主题直接相关的政策变化、时间节点或新情况，应在此处简明扼要地介绍，如没有则不用提及。\n\
            \        *   此部分应为后续的风险分析提供必要的上下文。\n    *   风格：语言简洁，概括性强，信息密度适中，为整个报告奠定基调。\n\
            \n3.  **风险分析**:\n    *   固定开头：必须以**“分析认为，”**作为此部分的起始。\n    *   内容：\n \
            \       *   基于{{#1748680365836.text#}}中提及的各类风险点（这些风险点可能在初步标题下方的段落正文中有详细描述），进行高度的提炼、整合和归纳。\n\
            \        *   将众多具体的风险描述总结为**3至5个主要的风险类别/方面**。\n        *   每个风险类别应有一个概括性的名称或描述（例如：情感矛盾纠纷风险、公共场所秩序风险、网络诈骗风险、个人极端行为风险等），以“风险的具体表现+风险概括名称”的形式展现，每点字数控制在20个字左右（如：存在利用节日通过“杀猪盘”、裸聊敲诈、婚恋交友等方式实施网络诈骗的风险。）不要以“XX风险：XXX”的形式呈现。\n\
            \        *   避免简单罗列原始素材中的风险点，而是要进行抽象和分类。\n\n4.  **对策建议**:\n    *   固定开头：必须以**“因此，建议各地做好以下X方面工作：”**作为此部分的起始，其中X是建议的条数（通常是三至五条，与风险分析的维度可以有所呼应，但不必一一对应）。\n\
            \    *   建议条目格式：\n        *   每条建议以中文数字序号开头，如“一是”、“二是”、“三是”等。\n      \
            \  *   每条建议应以动词开头的短语作为小标题，内容具体、具有操作指导性。\n    *   内容来源：从{{#1748680365836.text#}}中提取并整合各项对策建议。如果原始素材中建议较多或分散，需要筛选、合并，使其精炼且有代表性。\n\
            \    *   常见建议方向（供参考，具体需依据素材）：强化重点区域巡逻防控、加强矛盾纠纷排查化解、严查严打特定违法行为（可联合多部门）、加强普法与安全宣传教育、优化应急预案或管理措施等。\n\
            \n5.  **语言风格**:\n    *   **正式与客观**: 使用规范的书面语言，避免口语化、情绪化表达。\n    *   **精炼与准确**:\
            \ 用最少的文字表达清晰的含义，避免冗余和不必要的修饰。\n    *   **信息密度高**: 在有限的篇幅内传递核心信息。\n\n6.\
            \  **字数控制**:\n    *   严格控制总字数在**400-600字**之间，不能超过600字。\n\n7.  **信息处理核心原则**:\n\
            \    *   **核心信息提取**: 准确识别并提取{{#1748680365836.text#}}中的关键背景、核心风险和主要对策。\n\
            \    *   **高度整合与归纳**: 对于{{#1748680365836.text#}}中散落的、相似的或相关的风险点/建议，必须进行有效的整合与归纳，形成高度概括的条目，而不是简单复制粘贴或罗列。\n\
            \    *   **避免冗余**: 主动去除{{#1748680365836.text#}}中的重复信息和与核心主旨关联度不高的细节。\n\
            \n# 工作流程指导\n1.  仔细阅读并深刻理解{{#1748680365836.text#}}中的全部内容，**特别关注每段话首句的初步标题信息**。\n\
            2.  **首先处理标题**：根据各段初步标题，提炼总结形成最终的微情报标题。\n3.  然后，严格按照上述【输出要求】中明确的各部分结构、内容要点、格式和风格进行撰写报告的其余部分。\n\
            4.  在撰写“风险分析”和“对策建议”部分时，特别注意信息的提炼、整合和分类能力，力求概括性和条理性。\n\n现在，我将提供{{#1748680365836.text#}}。请你严格按照上述指示进行处理，并生成“微情报”。最后生成的微情报以“标题+风险基本情况/背景+分析内容+对策建议”组成，除标题需要有，其他如风险基本情况/背景只要有内容即可，不需要有标题。"
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1748680444163'
      position:
        x: 703.1571650238598
        y: 280.484283497614
      positionAbsolute:
        x: 703.1571650238598
        y: 280.484283497614
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1748680444163'
          - text
          variable: output
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1748793932972'
      position:
        x: 1032.2653285848414
        y: 280.484283497614
      positionAbsolute:
        x: 1032.2653285848414
        y: 280.484283497614
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 392.4688516831138
      y: 34.65218361600847
      zoom: 0.6597539832982272
