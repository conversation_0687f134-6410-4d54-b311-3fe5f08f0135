import pandas as pd

# 读取Excel文件
file_path = '新的执法记录仪日志每分钟1条.xlsx'
df = pd.read_excel(file_path)

# 定义县市区和派出所的映射关系
mapping = {
    '椒江': ['海门', '府前', '白云', '葭沚', '洪家', '下陈', '前所', '章安', '车站', '港区', '大陈'],
    '黄岩': ['院桥', '城南', '城西', '澄江', '新前', '城东', '宁溪', '北洋', '城北', '江口', '头陀'],
    '路桥': ['路桥', '路南', '路北', '螺洋', '桐屿', '峰江', '新桥', '横街', '蓬街', '金清'],
    '临海': ['古城', '大田', '括苍', '尤溪', '大洋', '涌泉', '白水洋', '汛桥', '邵家渡', '桃渚', '河头', '东塍', '小芝', '永丰', '杜桥', '江南', '沿江', '上盘'],
    '温岭': ['太平', '城东', '城西', '城北', '横峰', '泽国', '大溪', '松门', '箬横', '新河', '石塘', '滨海', '温峤', '城南', '石桥头', '坞根', '车站', '经济开发区'],
    '玉环': ['玉城', '坎门', '楚门', '大麦屿', '清港', '沙门', '新城', '干江', '龙溪'],
    '天台': ['城东', '城西', '城南', '平桥', '白鹤', '坦头', '三合', '街头', '石梁', '国清'],
    '仙居': ['福应', '安洲', '横溪', '白塔', '下各', '朱溪', '官路', '南峰', '田市'],
    '三门': ['沙柳', '健跳', '海游', '亭旁', '珠岙', '花桥', '浦坝港', '海润'],
    '台州湾新区': ['月湖', '三甲', '海虹']
}

# 定义特殊处理规则
special_cases = {
    '牧屿': '温岭牧屿警务区',
    '葭芷': '椒江葭沚派出所'
}

# 规范化派出所名称
def normalize_name(name):
    for county, stations in mapping.items():
        for station in stations:
            if station in name:
                return f"{county}{station}派出所"
    for special, replacement in special_cases.items():
        if special in name:
            return replacement
    return name

# 应用规范化函数
df['操作部门'] = df['操作部门'].apply(normalize_name)

# 保存结果到新的Excel文件
output_file_path = '派出所名称治理后的执法记录仪日志.xlsx'
df.to_excel(output_file_path, index=False)


print(f"规范化后的文件已保存为 {output_file_path}")