#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
开源情报搜索助手 - 主程序（优化版）
"""

import sys
import os
import time
import threading
import logging
import webbrowser
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QRadioButton, QLabel, QListWidget, QListWidgetItem,
    QTextBrowser, QProgressBar, QSplitter, QButtonGroup, QFrame,
    QMessageBox, QStatusBar, QMenu, QAction, QToolBar, QToolButton,
    QGroupBox, QScrollArea, QFileDialog, QDialog, QGridLayout, QLineEdit,
    QCheckBox, QDialogButtonBox, QSizePolicy
)
from PyQt5.QtCore import Qt, QSize, QUrl, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QFont, QDesktopServices, QColor, QPalette

# 导入自定义工具模块
from utils import get_domestic_hot_news, get_international_hot_news, HotNewsCluster, ContentFetcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='osint_assistant.log'
)
logger = logging.getLogger('OSINTAssistant')

# 优化后的样式常量
MAIN_STYLE = """
QMainWindow, QWidget {
    background-color: #f8f9fa;
    color: #2c3e50;
}
QListWidget {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 8px;
    font-size: 13px;
    min-width: 450px;
    selection-background-color: #e7f3ff;
}
QListWidget::item {
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 8px;
    margin: 4px 0px;
    border-radius: 6px;
}
QListWidget::item:hover {
    background-color: #f8f9fa;
}
QListWidget::item:selected {
    background-color: #e7f3ff;
    color: #1a73e8;
    border: 1px solid #1a73e8;
}
QTextBrowser {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-size: 14px;
    line-height: 1.6;
}
QPushButton {
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    font-size: 13px;
}
QPushButton:hover {
    background-color: #1557b0;
}
QPushButton:pressed {
    background-color: #0d47a1;
}
QRadioButton {
    font-size: 14px;
    font-weight: bold;
    padding: 6px;
    spacing: 8px;
}
QProgressBar {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background-color: #f8f9fa;
    text-align: center;
    height: 12px;
}
QProgressBar::chunk {
    background-color: #4285f4;
    border-radius: 3px;
}
QGroupBox {
    font-weight: bold;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-top: 12px;
    padding-top: 18px;
    font-size: 14px;
}
QToolButton {
    background-color: transparent;
    border: none;
    padding: 6px;
    border-radius: 6px;
}
QToolButton:hover {
    background-color: #e7f3ff;
}
""" 

class FetchWorker(QThread):
    """异步获取热点信息的工作线程"""
    
    updateProgress = pyqtSignal(int)
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    
    def __init__(self, fetch_type):
        """
        初始化获取线程
        
        Args:
            fetch_type: 获取类型，'domestic'或'international'
        """
        super().__init__()
        self.fetch_type = fetch_type
        
    def run(self):
        """执行获取任务"""
        try:
            # 模拟进度更新
            self.updateProgress.emit(10)
            
            # 获取热点数据
            if self.fetch_type == 'domestic':
                hot_items = get_domestic_hot_news()
            else:
                hot_items = get_international_hot_news()
                
            self.updateProgress.emit(50)
            
            # 使用HotNewsCluster对获取的热点进行聚类
            if hot_items:
                cluster_tool = HotNewsCluster(similarity_threshold=0.35)
                clustered_items = cluster_tool.cluster_hot_news(hot_items)
                self.updateProgress.emit(90)
                
                # 返回聚类后的热点列表
                self.finished.emit(clustered_items)
            else:
                self.error.emit("未获取到热点信息，请检查网络连接")
                
            self.updateProgress.emit(100)
            
        except Exception as e:
            logger.error(f"获取热点失败: {e}")
            self.error.emit(f"获取热点时出错: {str(e)}")

class ContentFetchWorker(QThread):
    """异步获取热点详情的工作线程"""
    
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, url):
        """
        初始化内容获取线程
        
        Args:
            url: 要获取内容的URL
        """
        super().__init__()
        self.url = url
        self.fetcher = ContentFetcher()
        
    def run(self):
        """执行获取任务"""
        try:
            # 获取详细内容
            content = self.fetcher.fetch_content(self.url)
            
            # 返回获取到的内容
            self.finished.emit(content)
            
        except Exception as e:
            logger.error(f"获取内容详情失败: {e}")
            self.error.emit(f"获取内容详情时出错: {str(e)}")

class HotItemWidget(QWidget):
    """热点项目控件（优化版）"""
    
    def __init__(self, item_data, parent=None):
        super().__init__(parent)
        self.item_data = item_data
        
        # 主布局 - 优化间距和布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(10)
        
        # 主信息布局
        info_layout = QVBoxLayout()
        info_layout.setSpacing(8)
        
        # 标题 - 优化字体和样式
        title_text = item_data['title']
        if 'link' in item_data:
            title_text = f'<a href="{item_data["link"]}" style="color: #2c3e50; text-decoration: none;">{title_text}</a>'
        
        self.title_label = QLabel(title_text)
        self.title_label.setWordWrap(True)
        self.title_label.setTextFormat(Qt.RichText)
        self.title_label.setOpenExternalLinks(True)  # 允许打开外部链接
        self.title_label.setStyleSheet("""
            QLabel {
                font-weight: 600; 
                font-size: 15px; 
                padding: 5px 0px;
                line-height: 24px;
                color: #2c3e50;
                letter-spacing: 0.3px;
                background: transparent;
            }
            QLabel:hover {
                color: #1a73e8;
            }
        """)
        self.title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.title_label.setMinimumHeight(24)  # 设置最小高度
        self.title_label.adjustSize()  # 自动调整大小
        
        # 添加标题到信息布局
        info_layout.addWidget(self.title_label)
        
        # 聚合热点图标和标记 - 优化样式
        is_aggregated = 'cluster_size' in item_data and item_data['cluster_size'] > 1
        
        if is_aggregated:
            # 为聚合热点添加特殊标记
            marker_layout = QHBoxLayout()
            marker_layout.setSpacing(6)
            
            # 聚合图标
            aggregate_marker = QLabel("🔥")
            aggregate_marker.setStyleSheet("color: #e74c3c; font-size: 18px;")
            marker_layout.addWidget(aggregate_marker)
            
            # 聚合标识 - 优化样式
            aggregate_label = QLabel(f"热点聚合 [{item_data['cluster_size']}条相似内容]")
            aggregate_label.setStyleSheet("""
                color: #e74c3c; 
                font-size: 13px; 
                font-weight: bold;
                background-color: #fde8e7;
                padding: 3px 8px;
                border-radius: 4px;
            """)
            marker_layout.addWidget(aggregate_label)
            
            marker_layout.addStretch()
            info_layout.addLayout(marker_layout)
            
            # 优化聚合热点的背景样式
            self.setStyleSheet("""
                background-color: #fff5f5; 
                border-radius: 8px;
                border: 1px solid #fad7d7;
            """)
        
        # 来源和热度信息 - 优化布局和样式
        source_hot_layout = QHBoxLayout()
        source_hot_layout.setSpacing(15)
        
        # 来源 - 优化样式
        source_label = QLabel(f"来源: {item_data['source']}")
        source_label.setStyleSheet("""
            color: #666666; 
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
        """)
        source_hot_layout.addWidget(source_label)
        
        # 热度 - 优化样式
        if 'hot_value' in item_data:
            hot_label = QLabel(f"热度: {item_data['hot_value']}")
            hot_label.setStyleSheet("""
                color: #e67e22; 
                font-size: 12px;
                font-weight: bold;
                background-color: #fef5e9;
                padding: 2px 6px;
                border-radius: 3px;
            """)
            source_hot_layout.addWidget(hot_label)
        
        source_hot_layout.addStretch()
        info_layout.addLayout(source_hot_layout)
        
        # 添加主信息布局到主布局
        layout.addLayout(info_layout)

class ProxySettingsDialog(QDialog):
    """代理设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('代理设置')
        self.setMinimumWidth(400)
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 国内代理设置组
        domestic_group = QGroupBox('国内网站代理')
        domestic_layout = QGridLayout()
        
        self.domestic_enabled = QCheckBox('启用国内网站代理')
        domestic_layout.addWidget(self.domestic_enabled, 0, 0, 1, 2)
        
        domestic_layout.addWidget(QLabel('HTTP代理:'), 1, 0)
        self.domestic_http = QLineEdit()
        self.domestic_http.setPlaceholderText('http://地址:端口')
        domestic_layout.addWidget(self.domestic_http, 1, 1)
        
        domestic_layout.addWidget(QLabel('HTTPS代理:'), 2, 0)
        self.domestic_https = QLineEdit()
        self.domestic_https.setPlaceholderText('http://地址:端口')
        domestic_layout.addWidget(self.domestic_https, 2, 1)
        
        domestic_group.setLayout(domestic_layout)
        layout.addWidget(domestic_group)
        
        # 国际代理设置组
        international_group = QGroupBox('国际网站代理')
        international_layout = QGridLayout()
        
        self.international_enabled = QCheckBox('启用国际网站代理')
        international_layout.addWidget(self.international_enabled, 0, 0, 1, 2)
        
        international_layout.addWidget(QLabel('HTTP代理:'), 1, 0)
        self.international_http = QLineEdit()
        self.international_http.setPlaceholderText('http://地址:端口')
        international_layout.addWidget(self.international_http, 1, 1)
        
        international_layout.addWidget(QLabel('HTTPS代理:'), 2, 0)
        self.international_https = QLineEdit()
        self.international_https.setPlaceholderText('http://地址:端口')
        international_layout.addWidget(self.international_https, 2, 1)
        
        international_group.setLayout(international_layout)
        layout.addWidget(international_group)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        # 加载当前设置
        self.load_current_settings()
    
    def load_current_settings(self):
        """加载当前代理设置"""
        from utils.spider import get_proxy_config
        config = get_proxy_config()
        
        # 国内代理
        domestic = config.get('domestic', {})
        self.domestic_enabled.setChecked(domestic.get('enabled', False))
        self.domestic_http.setText(domestic.get('http', ''))
        self.domestic_https.setText(domestic.get('https', ''))
        
        # 国际代理
        international = config.get('international', {})
        self.international_enabled.setChecked(international.get('enabled', False))
        self.international_http.setText(international.get('http', ''))
        self.international_https.setText(international.get('https', ''))
    
    def get_settings(self):
        """获取设置的代理配置"""
        return {
            'domestic': {
                'enabled': self.domestic_enabled.isChecked(),
                'http': self.domestic_http.text().strip(),
                'https': self.domestic_https.text().strip()
            },
            'international': {
                'enabled': self.international_enabled.isChecked(),
                'http': self.international_http.text().strip(),
                'https': self.international_https.text().strip()
            }
        }
    
    def accept(self):
        """确认设置"""
        from utils.spider import set_proxy
        settings = self.get_settings()
        
        # 设置国内代理
        domestic = settings['domestic']
        set_proxy('domestic', domestic['enabled'],
                 domestic['http'], domestic['https'])
        
        # 设置国际代理
        international = settings['international']
        set_proxy('international', international['enabled'],
                 international['http'], international['https'])
        
        super().accept()

class MainWindow(QMainWindow):
    """主窗口（优化版）"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.create_menus()
        self.display_welcome_message()
    
    def create_menus(self):
        """创建菜单栏和工具栏"""
        # 文件菜单
        file_menu = self.menuBar().addMenu('文件')
        
        refresh_action = QAction('刷新热点', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.fetch_hot_news)
        file_menu.addAction(refresh_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Alt+F4')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 设置菜单
        settings_menu = self.menuBar().addMenu('设置')
        
        proxy_action = QAction('代理设置', self)
        proxy_action.triggered.connect(self.open_proxy_settings)
        settings_menu.addAction(proxy_action)
        
        # 帮助菜单
        help_menu = self.menuBar().addMenu('帮助')
        
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
        # 工具栏
        toolbar = QToolBar()
        toolbar.setMovable(False)
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)
        
        toolbar.addAction(refresh_action)
        toolbar.addSeparator()
        toolbar.addAction(proxy_action)
    
    def display_welcome_message(self):
        """显示欢迎信息"""
        welcome_html = """
        <div style="color: #2c3e50; font-family: Arial, sans-serif;">
            <h2 style="color: #1a73e8;">欢迎使用开源情报搜索助手</h2>
            <p>本工具可以帮助您实时获取和整合来自多个平台的热点信息：</p>
            <ul>
                <li>支持国内主流平台：百度热榜、微博热搜、抖音热搜、今日头条等</li>
                <li>支持国际热点获取：X(Twitter)、Youtube、Reddit等</li>
                <li>智能聚合相似热点，提供全面的热点视图</li>
                <li>支持热点详情查看和自动翻译功能</li>
            </ul>
            <p>使用说明：</p>
            <ol>
                <li>选择"国内热点"或"国际热点"</li>
                <li>点击"刷新热点"获取最新信息</li>
                <li>点击任意热点查看详细内容</li>
                <li>🔥图标表示该热点在多个平台受到关注</li>
            </ol>
            <p style="color: #666666; font-size: 12px;">
                提示：如果访问国际网站遇到问题，请在"设置"中配置代理
            </p>
        </div>
        """
        self.detail_browser.setHtml(welcome_html)
    
    def open_proxy_settings(self):
        """打开代理设置对话框"""
        dialog = ProxySettingsDialog(self)
        dialog.exec_()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, '关于',
            '<h3>开源情报搜索助手</h3>'
            '<p>版本：1.0.0</p>'
            '<p>一款功能强大的热点信息聚合工具</p>'
            '<p>支持国内外多个平台的热点获取与整合</p>')
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口标题和大小
        self.setWindowTitle('开源情报搜索助手')
        self.setMinimumSize(1200, 800)
        
        # 应用全局样式
        self.setStyleSheet(MAIN_STYLE)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # 创建左侧热点列表区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)
        
        # 热点类型选择
        type_group = QButtonGroup(self)
        type_layout = QHBoxLayout()
        
        self.domestic_radio = QRadioButton('国内热点')
        self.domestic_radio.setChecked(True)
        type_group.addButton(self.domestic_radio)
        type_layout.addWidget(self.domestic_radio)
        
        self.international_radio = QRadioButton('国际热点')
        type_group.addButton(self.international_radio)
        type_layout.addWidget(self.international_radio)
        
        type_layout.addStretch()
        left_layout.addLayout(type_layout)
        
        # 热点列表
        self.hot_list = QListWidget()
        self.hot_list.setVerticalScrollMode(QListWidget.ScrollPerPixel)
        self.hot_list.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.hot_list.itemClicked.connect(self.on_item_clicked)
        left_layout.addWidget(self.hot_list)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.hide()
        left_layout.addWidget(self.progress_bar)
        
        # 刷新按钮
        refresh_btn = QPushButton('刷新热点')
        refresh_btn.clicked.connect(self.fetch_hot_news)
        left_layout.addWidget(refresh_btn)
        
        # 添加左侧区域到主布局
        main_layout.addWidget(left_widget)
        
        # 创建右侧详情区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # 详情浏览器
        self.detail_browser = QTextBrowser()
        self.detail_browser.setOpenExternalLinks(True)
        self.detail_browser.setStyleSheet("""
            QTextBrowser {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                font-family: Arial, sans-serif;
            }
            QTextBrowser:hover {
                border-color: #1a73e8;
            }
        """)
        right_layout.addWidget(self.detail_browser)
        
        # 操作按钮布局
        action_layout = QHBoxLayout()
        
        # 在浏览器中打开按钮
        open_browser_btn = QPushButton('在浏览器中打开')
        open_browser_btn.clicked.connect(self.open_in_browser)
        action_layout.addWidget(open_browser_btn)
        
        # 复制链接按钮
        copy_link_btn = QPushButton('复制链接')
        copy_link_btn.clicked.connect(self.copy_link)
        action_layout.addWidget(copy_link_btn)
        
        # 保存内容按钮
        save_btn = QPushButton('保存内容')
        save_btn.clicked.connect(self.save_content)
        action_layout.addWidget(save_btn)
        
        action_layout.addStretch()
        right_layout.addLayout(action_layout)
        
        # 添加右侧区域到主布局
        main_layout.addWidget(right_widget)
        
        # 设置左右区域的比例为2:3
        main_layout.setStretch(0, 2)
        main_layout.setStretch(1, 3)
        
        # 状态栏
        self.statusBar().showMessage('就绪')
        
        # 初始化数据
        self.fetch_hot_news()
        
        # 保存当前查看的热点数据
        self.current_item_data = None
        self.current_content = None
    
    def fetch_hot_news(self):
        """获取热点新闻"""
        # 清空列表和详情
        self.hot_list.clear()
        self.detail_browser.clear()
        
        # 显示进度条
        self.progress_bar.setValue(0)
        self.progress_bar.show()
        
        # 创建工作线程
        fetch_type = 'domestic' if self.domestic_radio.isChecked() else 'international'
        self.worker = FetchWorker(fetch_type)
        self.worker.updateProgress.connect(self.update_progress)
        self.worker.finished.connect(self.on_fetch_finished)
        self.worker.error.connect(self.on_fetch_error)
        
        # 开始获取
        self.worker.start()
        
        # 更新状态栏
        self.statusBar().showMessage('正在获取热点信息...')
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def on_fetch_finished(self, hot_items):
        """获取完成的处理"""
        # 隐藏进度条
        self.progress_bar.hide()
        
        # 添加热点项到列表
        for item in hot_items:
            list_item = QListWidgetItem()
            item_widget = HotItemWidget(item)
            list_item.setSizeHint(item_widget.sizeHint())
            self.hot_list.addItem(list_item)
            self.hot_list.setItemWidget(list_item, item_widget)
        
        # 更新状态栏
        self.statusBar().showMessage(f'获取完成，共{len(hot_items)}条热点信息')
    
    def on_fetch_error(self, error_msg):
        """获取错误的处理"""
        # 隐藏进度条
        self.progress_bar.hide()
        
        # 显示错误消息
        QMessageBox.warning(self, '错误', error_msg)
        
        # 更新状态栏
        self.statusBar().showMessage('获取失败')
    
    def on_item_clicked(self, item):
        """处理热点项点击事件"""
        # 获取点击项的数据
        item_widget = self.hot_list.itemWidget(item)
        if not item_widget:
            return
            
        self.current_item_data = item_widget.item_data
        
        # 如果没有链接，显示提示信息
        if not self.current_item_data.get('link'):
            self.display_no_link_message(self.current_item_data)
            return
            
        # 显示加载提示
        self.detail_browser.setHtml(
            '<div style="text-align: center; margin-top: 20px;">'
            '<p style="font-size: 14px; color: #666;">正在获取内容，请稍候...</p>'
            '<p style="color: #999; font-size: 12px;">'
            f'正在从 {self.current_item_data["source"]} 获取 "{self.current_item_data["title"]}" 的详细内容'
            '</p></div>'
        )
        
        # 创建内容获取线程
        self.content_worker = ContentFetchWorker(self.current_item_data['link'])
        self.content_worker.finished.connect(
            lambda content: self.display_content(self.current_item_data, content)
        )
        self.content_worker.error.connect(self.on_content_fetch_error)
        self.content_worker.start()
    
    def display_content(self, item_data, content):
        """显示热点详细内容"""
        self.current_content = content
        
        # 构建HTML内容
        html = f"""
        <div style="font-family: Arial, sans-serif;">
            <h2 style="color: #1a73e8; margin-bottom: 15px; line-height: 1.4;">
                <a href="{item_data['link']}" style="color: #1a73e8; text-decoration: none;">
                    {item_data['title']}
                </a>
            </h2>
            
            <div style="margin-bottom: 15px; line-height: 1.6;">
                <span style="color: #666; font-size: 13px; padding: 4px 8px; background: #f8f9fa; border-radius: 4px;">
                    来源：{item_data['source']}
                </span>
                {f'<span style="color: #e67e22; font-size: 13px; margin-left: 15px; padding: 4px 8px; background: #fef5e9; border-radius: 4px;">热度：{item_data.get("hot_value", "")}</span>' if 'hot_value' in item_data else ''}
            </div>
            
            <div style="color: #2c3e50; line-height: 1.8; font-size: 14px; margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                {content.get('content', '暂无详细内容')}
            </div>
        """
        
        # 如果有图片，添加图片区域
        if 'images' in content and content['images']:
            html += '<div style="margin-top: 20px;">'
            for img_url in content['images']:
                html += f'<img src="{img_url}" style="max-width: 100%; margin-bottom: 10px;"><br>'
            html += '</div>'
        
        # 添加原文链接
        html += f"""
            <div style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #eee;">
                <p style="font-size: 13px; color: #666;">
                    原文链接：<a href="{item_data['link']}" style="color: #1a73e8;">{item_data['link']}</a>
                </p>
            </div>
        </div>
        """
        
        self.detail_browser.setHtml(html)
    
    def display_no_link_message(self, item_data):
        """显示无链接提示信息"""
        html = f"""
        <div style="text-align: center; margin-top: 50px; font-family: Arial, sans-serif;">
            <h3 style="color: #666;">暂无详细内容</h3>
            <p style="color: #999; font-size: 14px; margin-top: 20px;">
                该热点 "{item_data['title']}" 暂时没有关联的详细内容链接
            </p>
            <p style="color: #666; font-size: 13px; margin-top: 10px;">
                来源：{item_data['source']}
            </p>
        </div>
        """
        self.detail_browser.setHtml(html)
        self.current_content = None
    
    def on_content_fetch_error(self, error_message):
        """处理内容获取错误"""
        if not self.current_item_data:
            return
            
        html = f"""
        <div style="text-align: center; margin-top: 50px; font-family: Arial, sans-serif;">
            <h3 style="color: #e74c3c;">获取内容失败</h3>
            <p style="color: #666; font-size: 14px; margin-top: 20px;">
                无法获取 "{self.current_item_data['title']}" 的详细内容
            </p>
            <p style="color: #999; font-size: 13px; margin-top: 10px;">
                错误信息：{error_message}
            </p>
            <p style="margin-top: 20px;">
                <a href="{self.current_item_data['link']}" style="color: #1a73e8;">
                    点击在浏览器中查看原文
                </a>
            </p>
        </div>
        """
        self.detail_browser.setHtml(html)
        self.current_content = None
    
    def open_in_browser(self):
        """在浏览器中打开当前热点"""
        if self.current_item_data and 'link' in self.current_item_data:
            webbrowser.open(self.current_item_data['link'])
    
    def copy_link(self):
        """复制当前热点链接"""
        if self.current_item_data and 'link' in self.current_item_data:
            QApplication.clipboard().setText(self.current_item_data['link'])
            self.statusBar().showMessage('链接已复制到剪贴板', 3000)
    
    def save_content(self):
        """保存当前热点内容"""
        if not self.current_item_data or not self.current_content:
            return
            
        # 构建默认文件名
        default_name = f"{self.current_item_data['source']}_{self.current_item_data['title'][:20]}.html"
        default_name = default_name.replace('/', '_').replace('\\', '_')
        
        # 选择保存位置
        file_path, _ = QFileDialog.getSaveFileName(
            self, '保存内容', default_name, 'HTML文件 (*.html);;所有文件 (*.*)'
        )
        
        if not file_path:
            return
            
        try:
            # 构建完整的HTML文档
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{self.current_item_data['title']}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #2c3e50; max-width: 800px; margin: 0 auto; padding: 20px; }}
                    img {{ max-width: 100%; height: auto; }}
                    .source {{ color: #666; font-size: 14px; }}
                    .hot-value {{ color: #e67e22; font-size: 14px; }}
                    .content {{ margin: 20px 0; }}
                    .images {{ margin: 20px 0; }}
                    .footer {{ margin-top: 20px; padding-top: 10px; border-top: 1px solid #eee; font-size: 13px; color: #666; }}
                </style>
            </head>
            <body>
                <h1>{self.current_item_data['title']}</h1>
                <div class="source">
                    来源：{self.current_item_data['source']}
                    {f'<span class="hot-value">热度：{self.current_item_data.get("hot_value", "")}</span>' if 'hot_value' in self.current_item_data else ''}
                </div>
                <div class="content">
                    {self.current_content.get('content', '暂无详细内容')}
                </div>
            """
            
            # 添加图片
            if 'images' in self.current_content and self.current_content['images']:
                html += '<div class="images">'
                for img_url in self.current_content['images']:
                    html += f'<img src="{img_url}"><br>'
                html += '</div>'
            
            # 添加页脚
            html += f"""
                <div class="footer">
                    原文链接：<a href="{self.current_item_data['link']}">{self.current_item_data['link']}</a>
                    <br>
                    保存时间：{time.strftime('%Y-%m-%d %H:%M:%S')}
                </div>
            </body>
            </html>
            """
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html)
            
            self.statusBar().showMessage('内容已保存', 3000)
            
        except Exception as e:
            QMessageBox.warning(self, '保存失败', f'保存内容时出错：{str(e)}')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_()) 