我想制作一个每日分析研判助手软件，软件支持win7/win10/win11系统，需要有一个美观的界面。该软件可以支持上传xls、xlsx、csv格式的表格文件，表格文件中有两张sheet表单，提供用户表单选择模式，供用户点击选择。
在C:\Users\<USER>\Downloads\AI写代码文件夹中有一个名为“指令核查信息.xls”的excel文件，你可以作为参考。
目前两张表单分别名为“指令核查信息”和“关联人员”。其中，“指令核查信息”表单第一行是字段名，分别为指令单号、指令标题、指令类型、指令来源、级别、线索指向地、指向开始时间、指向结束时间、简要概况、发送时间、接受单位、抄送单位、反馈时限、状态。“关联人员”表单第一行也是字段名，分别为指令单号、指令标题、姓名、证件号码、稳控状态、维权风险、稳控方式、维权角色、指向地、反馈时间、反馈内容、反馈人、反馈单位。这个软件要实现的功能是：通过对表格中相关内容和数据的提取，形成一个完整的每日分析研判报告，最终输出为doc或者docx的word文档。
1.该软件需要获取系统当前的日期，去掉年份，只保留月日（设为变量{date}）。
2.软件需要获取“指令核查信息”表单中除了表头（字段行）之外的行数（设为变量{row}）。
3.软件需要获取“关联人员”表单中的“指令标题”字段下的内容，逐行获取，然后对相同内容的标题进行合并，并对内容进行进一步处理，如某行提取出的内容为“对北京区指流转【线索涉及人员】进京在京轨迹监测通报-数据截至6月28日8时线索开展核查处置的指令”，你需要对该内容做如下处理：提取“对”字后的内容一直到“线索”，即最终得到的结果是“北京区指流转【线索涉及人员】进京在京轨迹监测通报-数据截至6月28日8时线索”（设为变量{clue}）。
4.软件需要获取“关联人员”表单中的“姓名”字段下的内容，逐行获取，得到每个人的姓名（设为变量{name}}）。
5.软件需要获取“关联人员”表单中的“稳控状态”下的内容，表格中的稳控状态有“已稳控”“工作中”“未反馈”“移交外省”四种状态，其中请软件将所有的“未反馈”都一律输出为“工作中”。即最终得到结果只有“已稳控”“工作中”“移交外省”三种状态（设为变量{status}）。
6.软件需要获取“关联人员”表单中的“反馈内容”字段下的内容，逐行获取，需要对内容进行处理，首先提取内容中的派出所名称（一般在“责任单位：”后），派出所一般有两种表现形式，一种只有乡镇街道的名称，如章安派出所、葭沚派出所、路北派出所等；另一种是包括了县市区公安机关的名称，如黄岩分局宁溪派出所、台州市公安局黄岩分局宁溪派出所，三门海游派出所。无论是哪种情况，你只需要输出县市区+乡镇街道名称即可（如椒江葭沚、黄岩宁溪、温岭城东、仙居南峰）如果提取不到派出所名称，则需要你通过正则表达式在内容中匹配地址，寻找县市区和乡镇街道的名称，并最终输出。最终输出的内容设为变量{town}。
7.将每行的{clue}与{name}、{status}、{town}进行关联，注意，同一个指令标题下可能会对应好多人，如{clue2}下可能会有{name2}、{name3}、{name4}、{name5}等人。
8、检查每行内容中有无空缺的内容，如果有空缺，则交由用户自行填写，所有匹配识别出的内容用户也可以进行手动修改。
9、对{clue}进行类别识别，将{clue}中带有“玖富、捷越、中植、信托、投资、集资、存款、银行、原油宝、e租宝”内容的，识别为“涉众型经济利益群体”，设为变量{stakeholder}。将{clue}中带有“房产、房地产、业主、楼盘、商铺、店铺、小区”内容的，识别为“楼盘、商铺业主群体”，设为变量{owner}。将剩余的无法识别为上述两项的识别为“其他涉稳线索”，设为变量{other}。上述内容在软件界面上方要专门有一列，提供下拉菜单，供用户修改选择。
10、最终用户确认后，形成完整的每日分析研判报告。报告的模板如下：
台州市公安局每日分析研判（标题，字体：方正小标宋，字号：二号，居中）
{date}（如6月25日），全市社会大局平稳，未发生重大敏感案事件，预警涉稳线索{row}（如11）条。具体如下：（第一段，字体：仿宋_GB2312，字号：三号）
一、涉众型经济利益群体（二级标题，字体：黑体，字号：三号，首行缩进2字符）
1.{stakeholder1}（如北京区指流转【涉众重点群体（玖富、捷越、中植等）】进京在京轨迹监测通报-数据截至6月27日12时线索），涉及{name2}、{name3}、{name4}、{name5}，{status1}（已稳控）；{name6}、{name7}，{status2}（工作中）。（具体内容，字体：仿宋_GB2312，字号：三号，首行缩进2字符，下同）  ##说明：同一条线索，有人的状态可能是已稳控，有人的状态是工作中，要分开写。中间用分号隔开，下同。
2.{stakeholder2}（如北京区指流转关于对 “海汇国际”投资人6月29日进京集访风险人员开展工作的通知线索），涉及{name8}、{name9}，{status1}（已稳控）；{name10}，{status3}（移交外省）。
二、楼盘、商铺业主群体（二级标题，字体：黑体，字号：三号，首行缩进2字符）
3.{owner1}（如湖州吴兴融信凤仪府业主群体煽动6月23日周一赴省巡视组驻地维权线索），涉及{name11}，{status1}（已稳控）。
4.{owner2}（如东阳“横店红木家具中心”业主群体因要求退铺退款问题策划6月28日前往北京维权线索），涉及{name12}，{status1}（已稳控）；{name13}，{status2}（工作中）。
三、其他涉稳线索（二级标题，字体：黑体，字号：三号，首行缩进2字符）
5.{other1}（如北京区指流转河南省厅关于商请协助稳控相关人员的函线索），涉及{name14}，{status1}（已稳控）。
备注：数据统计时间自昨日15时至今日15时。（最后一段话，加在最后保持不变）

请你根据以上的要求，帮我生成python代码，以便我可以上传文件后可以生成报告。

