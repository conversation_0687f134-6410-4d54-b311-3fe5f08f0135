{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "outDir": "build", "declaration": true, "sourceMap": true, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "rootDir": "src", "lib": ["ES2020", "DOM"], "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "build", "**/*.test.ts"]}