import sys
import json
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QTextEdit, QPushButton, 
                            QComboBox, QMessageBox, QStyleFactory, QLineEdit)
from PyQt5.QtCore import Qt
import openai
import requests
from urllib.parse import urlparse
import hmac
import base64
import datetime
import hashlib
from bs4 import BeautifulSoup
import time
import jieba
import re
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import os
from PyQt5.QtGui import QIcon

class ConfigDialog(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_config()

    def init_ui(self):
        layout = QVBoxLayout()
        
        # API类型选择
        self.api_type = QComboBox()
        self.api_type.addItems(["通义千问", "讯飞星火", "OpenAI兼容", "Google Gemini"])
        self.api_type.currentIndexChanged.connect(self.on_api_type_changed)
        layout.addWidget(QLabel("API类型："))
        layout.addWidget(self.api_type)
        
        # 通义千问配置
        self.qianwen_group = QWidget()
        qianwen_layout = QVBoxLayout(self.qianwen_group)
        
        self.qianwen_url = QLineEdit()
        self.qianwen_url.setPlaceholderText("输入通义千问接口地址")
        self.qianwen_model = QLineEdit()
        self.qianwen_model.setPlaceholderText("输入通义千问模型名称")
        self.qianwen_key = QLineEdit()
        self.qianwen_key.setPlaceholderText("输入通义千问 API Key")
        
        qianwen_layout.addWidget(QLabel("接口地址："))
        qianwen_layout.addWidget(self.qianwen_url)
        qianwen_layout.addWidget(QLabel("模型名称："))
        qianwen_layout.addWidget(self.qianwen_model)
        qianwen_layout.addWidget(QLabel("API Key："))
        qianwen_layout.addWidget(self.qianwen_key)
        
        # 讯飞星火配置
        self.spark_group = QWidget()
        spark_layout = QVBoxLayout(self.spark_group)
        
        self.spark_url = QLineEdit()
        self.spark_url.setPlaceholderText("输入讯飞星火接口地址")
        self.spark_url.setText("https://spark-api-open.xf-yun.com/v1/chat/completions")
        self.spark_model = QLineEdit()
        self.spark_model.setPlaceholderText("输入讯飞星火模型名称（如：spark-v3）")
        self.spark_key = QLineEdit()
        self.spark_key.setPlaceholderText("输入讯飞星火 API Key")
        self.spark_secret = QLineEdit()
        self.spark_secret.setPlaceholderText("输入讯飞星火 API Secret")
        
        spark_layout.addWidget(QLabel("接口地址："))
        spark_layout.addWidget(self.spark_url)
        spark_layout.addWidget(QLabel("模型名称："))
        spark_layout.addWidget(self.spark_model)
        spark_layout.addWidget(QLabel("API Key："))
        spark_layout.addWidget(self.spark_key)
        spark_layout.addWidget(QLabel("API Secret："))
        spark_layout.addWidget(self.spark_secret)
        
        # OpenAI兼容接口配置
        self.openai_group = QWidget()
        openai_layout = QVBoxLayout(self.openai_group)
        
        self.openai_url = QLineEdit()
        self.openai_url.setPlaceholderText("输入OpenAI兼容接口地址")
        self.openai_model = QLineEdit()
        self.openai_model.setPlaceholderText("输入模型名称")
        self.openai_key = QLineEdit()
        self.openai_key.setPlaceholderText("输入API Key")
        
        openai_layout.addWidget(QLabel("接口地址："))
        openai_layout.addWidget(self.openai_url)
        openai_layout.addWidget(QLabel("模型名称："))
        openai_layout.addWidget(self.openai_model)
        openai_layout.addWidget(QLabel("API Key："))
        openai_layout.addWidget(self.openai_key)

        # Google Gemini配置
        self.gemini_group = QWidget()
        gemini_layout = QVBoxLayout(self.gemini_group)
        
        self.gemini_url = QLineEdit()
        self.gemini_url.setPlaceholderText("输入Gemini接口地址")
        self.gemini_url.setText("https://generativelanguage.googleapis.com/v1beta/models")
        self.gemini_model = QLineEdit()
        self.gemini_model.setPlaceholderText("输入模型名称（如：gemini-pro）")
        self.gemini_model.setText("gemini-pro")
        self.gemini_key = QLineEdit()
        self.gemini_key.setPlaceholderText("输入API Key")
        
        gemini_layout.addWidget(QLabel("接口地址："))
        gemini_layout.addWidget(self.gemini_url)
        gemini_layout.addWidget(QLabel("模型名称："))
        gemini_layout.addWidget(self.gemini_model)
        gemini_layout.addWidget(QLabel("API Key："))
        gemini_layout.addWidget(self.gemini_key)
        
        # 添加代理设置组
        proxy_group = QWidget()
        proxy_layout = QVBoxLayout(proxy_group)
        
        # 代理开关
        self.proxy_enabled = QComboBox()
        self.proxy_enabled.addItems(["禁用代理", "启用代理"])
        proxy_layout.addWidget(QLabel("代理设置："))
        proxy_layout.addWidget(self.proxy_enabled)
        
        # 代理主机
        self.proxy_host = QLineEdit()
        self.proxy_host.setPlaceholderText("代理主机地址（如：127.0.0.1）")
        proxy_layout.addWidget(QLabel("代理主机："))
        proxy_layout.addWidget(self.proxy_host)
        
        # 代理端口
        self.proxy_port = QLineEdit()
        self.proxy_port.setPlaceholderText("代理端口（如：10808）")
        proxy_layout.addWidget(QLabel("代理端口："))
        proxy_layout.addWidget(self.proxy_port)
        
        # 测试代理按钮
        test_proxy_btn = QPushButton("测试代理连接")
        test_proxy_btn.clicked.connect(self.test_proxy)
        proxy_layout.addWidget(test_proxy_btn)
        
        # 将代理设置添加到主布局
        layout.addWidget(proxy_group)
        
        # 添加所有配置组
        layout.addWidget(self.qianwen_group)
        layout.addWidget(self.spark_group)
        layout.addWidget(self.openai_group)
        layout.addWidget(self.gemini_group)
        
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)
        
        self.setLayout(layout)
        self.on_api_type_changed(0)  # 初始化界面

    def on_api_type_changed(self, index):
        """根据选择的API类型显示对应的配置界面"""
        self.qianwen_group.setVisible(index == 0)  # 通义千问
        self.spark_group.setVisible(index == 1)    # 讯飞星火
        self.openai_group.setVisible(index == 2)   # OpenAI兼容
        self.gemini_group.setVisible(index == 3)   # Google Gemini

    def load_config(self):
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
                self.api_type.setCurrentIndex(config.get('api_type', 0))
                
                # 通义千问配置
                self.qianwen_url.setText(config.get('qianwen_url', ''))
                self.qianwen_model.setText(config.get('qianwen_model', ''))
                self.qianwen_key.setText(config.get('qianwen_key', ''))
                
                # 讯飞星火配置
                self.spark_url.setText(config.get('spark_url', 'https://spark-api-open.xf-yun.com/v1/chat/completions'))
                self.spark_model.setText(config.get('spark_model', ''))
                self.spark_key.setText(config.get('spark_key', ''))
                self.spark_secret.setText(config.get('spark_secret', ''))
                
                # OpenAI兼容配置
                self.openai_url.setText(config.get('openai_url', ''))
                self.openai_model.setText(config.get('openai_model', ''))
                self.openai_key.setText(config.get('openai_key', ''))

                # Google Gemini配置
                self.gemini_url.setText(config.get('gemini_url', 'https://generativelanguage.googleapis.com/v1beta/models'))
                self.gemini_model.setText(config.get('gemini_model', 'gemini-pro'))
                self.gemini_key.setText(config.get('gemini_key', ''))
                
                # 加载代理设置
                self.proxy_enabled.setCurrentIndex(config.get('proxy_enabled', 0))
                self.proxy_host.setText(config.get('proxy_host', '127.0.0.1'))
                self.proxy_port.setText(config.get('proxy_port', '10808'))
        except FileNotFoundError:
            pass

    def save_config(self):
        config = {
            'api_type': self.api_type.currentIndex(),
            
            # 通义千问配置
            'qianwen_url': self.qianwen_url.text(),
            'qianwen_model': self.qianwen_model.text(),
            'qianwen_key': self.qianwen_key.text(),
            
            # 讯飞星火配置
            'spark_url': self.spark_url.text(),
            'spark_model': self.spark_model.text(),
            'spark_key': self.spark_key.text(),
            'spark_secret': self.spark_secret.text(),
            
            # OpenAI兼容配置
            'openai_url': self.openai_url.text(),
            'openai_model': self.openai_model.text(),
            'openai_key': self.openai_key.text(),

            # Google Gemini配置
            'gemini_url': self.gemini_url.text(),
            'gemini_model': self.gemini_model.text(),
            'gemini_key': self.gemini_key.text(),
            
            # 保存代理设置
            'proxy_enabled': self.proxy_enabled.currentIndex(),
            'proxy_host': self.proxy_host.text(),
            'proxy_port': self.proxy_port.text(),
        }
        with open('config.json', 'w') as f:
            json.dump(config, f)
        
        # 更新代理环境变量
        self.update_proxy_settings()
        QMessageBox.information(self, "提示", "配置已保存")

    def test_proxy(self):
        """测试代理连接"""
        try:
            if self.proxy_enabled.currentIndex() == 0:
                QMessageBox.information(self, "提示", "代理当前已禁用")
                return
                
            host = self.proxy_host.text()
            port = self.proxy_port.text()
            
            if not host or not port:
                QMessageBox.warning(self, "警告", "请输入代理主机和端口")
                return
                
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)  # 设置超时时间为2秒
            
            result = sock.connect_ex((host, int(port)))
            sock.close()
            
            if result == 0:
                QMessageBox.information(self, "提示", "代理连接测试成功")
            else:
                QMessageBox.warning(self, "警告", "代理连接测试失败")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"测试代理时出错：{str(e)}")

    def update_proxy_settings(self):
        """更新代理环境变量设置"""
        if self.proxy_enabled.currentIndex() == 1:  # 启用代理
            host = self.proxy_host.text()
            port = self.proxy_port.text()
            if host and port:
                os.environ['HTTP_PROXY'] = f'http://{host}:{port}'
                os.environ['HTTPS_PROXY'] = f'http://{host}:{port}'
        else:  # 禁用代理
            os.environ.pop('HTTP_PROXY', None)
            os.environ.pop('HTTPS_PROXY', None)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置Windows 7兼容的样式
        self.setStyle(QStyleFactory.create('Fusion'))
        self.hot_events = []
        self.config = {}  # 初始化配置字典
        self.load_config()  # 先加载配置
        self.init_ui()     # 再初始化UI

    def init_ui(self):
        self.setWindowTitle('微情报搜情助手')
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置窗口图标
        icon_path = '1.ico'  # 假设图标文件名为1.ico，并与代码文件在同一目录下
        if hasattr(sys, '_MEIPASS'):  # 如果是打包后的exe
            icon_path = os.path.join(sys._MEIPASS, '1.ico')
        self.setWindowIcon(QIcon(icon_path))
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 顶部布局（只保留配置按钮）
        top_layout = QHBoxLayout()
        
        # 配置按钮
        config_btn = QPushButton("配置")
        config_btn.clicked.connect(self.show_config)
        top_layout.addStretch()  # 添加弹性空间，使配置按钮靠右
        top_layout.addWidget(config_btn)
        layout.addLayout(top_layout)
        
        # 添加热点事件搜集和选择功能
        event_top_layout = QHBoxLayout()
        self.collect_events_btn = QPushButton("搜集热点事件（约需1分钟，请耐心等待）")
        self.collect_events_btn.clicked.connect(self.collect_hot_events)
        event_top_layout.addWidget(self.collect_events_btn)
        layout.addLayout(event_top_layout)
        
        # 热点事件列表
        self.events_list = QComboBox()
        self.events_list.setMinimumWidth(600)
        self.events_list.currentIndexChanged.connect(self.on_event_selected)
        layout.addWidget(QLabel("选择热点事件："))
        layout.addWidget(self.events_list)
        
        # 事件详情显示
        self.event_edit = QTextEdit()
        layout.addWidget(QLabel("事件详情："))
        layout.addWidget(self.event_edit)
        
        # 添加内容输入框
        self.content_edit = QTextEdit()
        layout.addWidget(QLabel("内容编辑区域（用于编辑和润色）："))
        layout.addWidget(self.content_edit)
        
        # 添加字数设置
        summary_layout = QHBoxLayout()
        self.summary_length = QLineEdit()
        self.summary_length.setPlaceholderText("0-1000")
        self.summary_length.setText("200")  # 默认200字
        self.summary_length.setMaximumWidth(100)
        self.summary_length.textChanged.connect(self.validate_summary_length)
        
        summary_layout.addWidget(QLabel("精简字数设置："))
        summary_layout.addWidget(self.summary_length)
        summary_layout.addWidget(QLabel("字"))
        summary_layout.addStretch()
        layout.addLayout(summary_layout)
        
        # 添加复制按钮
        copy_btn = QPushButton("复制详情到编辑区")
        copy_btn.clicked.connect(self.copy_to_content)
        layout.addWidget(copy_btn)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.generate_btn = QPushButton("生成内容（约需20秒）")
        self.generate_btn.clicked.connect(self.generate_content)
        self.polish_btn = QPushButton("润色内容（约需20秒）")
        self.polish_btn.clicked.connect(self.polish_content)
        button_layout.addWidget(self.generate_btn)
        button_layout.addWidget(self.polish_btn)
        layout.addLayout(button_layout)

        # 在底部添加版权信息标签
        copyright_label = QLabel("版权所有：台州市公安局 解晟")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                font-family: "Microsoft YaHei";
                color: #000000;
                padding: 10px;
                border-top: 1px solid #cccccc;
                background-color: #f5f5f5;
                font-size: 16px;
                font-weight: normal;
            }
        """)
        layout.addWidget(copyright_label)

        # 设置整体窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 2px;
            }
            QPushButton {
                background-color: #e1e1e1;
                border: 1px solid #c0c0c0;
                padding: 4px 8px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #d4d4d4;
            }
            QComboBox {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 2px;
            }
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 2px;
            }
            QLabel {
                font-family: "Microsoft YaHei";
            }
            QTextEdit {
                font-family: "Microsoft YaHei";
            }
        """)

        # 修改字体设置，确保在Windows 7上正常显示
        font = self.font()
        font.setFamily('Microsoft YaHei')  # 使用微软雅黑字体
        self.setFont(font)

    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
                
            # 如果配置文件存在，立即更新代理设置
            proxy_enabled = self.config.get('proxy_enabled', 0)
            proxy_host = self.config.get('proxy_host', '')
            proxy_port = self.config.get('proxy_port', '')
            
            if proxy_enabled == 1 and proxy_host and proxy_port:
                os.environ['HTTP_PROXY'] = f'http://{proxy_host}:{proxy_port}'
                os.environ['HTTPS_PROXY'] = f'http://{proxy_host}:{proxy_port}'
            else:
                # 清除代理设置
                if 'HTTP_PROXY' in os.environ:
                    del os.environ['HTTP_PROXY']
                if 'HTTPS_PROXY' in os.environ:
                    del os.environ['HTTPS_PROXY']
                    
        except FileNotFoundError:
            # 如果配置文件不存在，创建默认配置
            self.config = {
                'api_type': 0,
                'qianwen_url': '',
                'qianwen_model': '',
                'qianwen_key': '',
                'spark_url': 'https://spark-api-open.xf-yun.com/v1/chat/completions',
                'spark_model': '',
                'spark_key': '',
                'spark_secret': '',
                'openai_url': '',
                'openai_model': '',
                'openai_key': '',
                'gemini_url': 'https://generativelanguage.googleapis.com/v1beta/models',
                'gemini_model': 'gemini-pro',
                'gemini_key': '',
                'proxy_enabled': 0,
                'proxy_host': '127.0.0.1',
                'proxy_port': '10808'
            }
            # 保存默认配置
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)

    def show_config(self):
        self.config_dialog = ConfigDialog()
        self.config_dialog.show()

    def call_ai_api(self, prompt):
        """调用AI API"""
        try:
            if not self.config:  # 如果配置为空，重新加载
                self.load_config()
                
            api_type = self.config.get('api_type', 0)
            
            # 检查API配置是否完整
            if api_type == 0:  # 通义千问
                if not all([self.config.get('qianwen_url'), 
                           self.config.get('qianwen_model'),
                           self.config.get('qianwen_key')]):
                    raise Exception("请先完成通义千问API配置并保存")
            elif api_type == 1:  # 讯飞星火
                if not all([self.config.get('spark_url'),
                           self.config.get('spark_model'),
                           self.config.get('spark_key'),
                           self.config.get('spark_secret')]):
                    raise Exception("请先完成讯飞星火API配置并保存")
            elif api_type == 2:  # OpenAI兼容
                if not all([self.config.get('openai_url'),
                           self.config.get('openai_model'),
                           self.config.get('openai_key')]):
                    raise Exception("请先完成OpenAI兼容API配置并保存")
            elif api_type == 3:  # Google Gemini
                if not all([self.config.get('gemini_url'),
                           self.config.get('gemini_model'),
                           self.config.get('gemini_key')]):
                    raise Exception("请先完成Google Gemini API配置并保存")
            
            if api_type == 0:  # 通义千问
                return self._call_qianwen_api(prompt)
            elif api_type == 1:  # 讯飞星火
                return self._call_spark_api(prompt)
            elif api_type == 2:  # OpenAI兼容
                return self._call_openai_api(prompt)
            elif api_type == 3:  # Google Gemini
                return self._call_gemini_api(prompt)
            else:
                raise Exception("未知的API类型")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"AI调用失败：{str(e)}")
            return None

    def _call_spark_api(self, prompt):
        try:
            api_base = self.config.get('spark_url', '').rstrip('/')
            api_key = self.config.get('spark_key', '')
            api_secret = self.config.get('spark_secret', '')
            model = self.config.get('spark_model', '')
            
            parsed_url = urlparse(api_base)
            host = parsed_url.netloc or 'spark-api-open.xf-yun.com'
            
            date = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            signature_origin = f"host: {host}\ndate: {date}\nPOST /v1/chat/completions HTTP/1.1"
            
            signature_sha = hmac.new(
                api_secret.encode('utf-8'),
                signature_origin.encode('utf-8'),
                digestmod=hashlib.sha256
            ).digest()
            
            signature_sha_base64 = base64.b64encode(signature_sha).decode('utf-8')
            authorization = f'hmac username="{api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha_base64}"'

            headers = {
                'Authorization': authorization,
                'Content-Type': 'application/json',
                'Host': host,
                'Date': date,
                'Accept': 'application/json'
            }
            
            payload = {
                'model': model,
                'messages': [{'role': 'user', 'content': prompt}],
                'stream': False,
                'temperature': 0.7,
                'max_tokens': 1024
            }
            
            url = f"{api_base}" if api_base.endswith('/v1/chat/completions') else f"{api_base}/v1/chat/completions"
            
            # 添加重试机制
            max_retries = 3
            retry_count = 0
            while retry_count < max_retries:
                try:
                    # 增加超时时间
                    response = requests.post(
                        url,
                        headers=headers,
                        json=payload,
                        verify=True,
                        timeout=60,  # 增加60秒
                        # 添加代理设置（如果需要）
                        # proxies={
                        #     'http': 'http://your-proxy:port',
                        #     'https': 'https://your-proxy:port'
                        # }
                    )
                    
                    if response.status_code == 200:
                        response_data = response.json()
                        try:
                            return response_data['choices'][0]['message']['content']
                        except KeyError:
                            if 'data' in response_data:
                                return response_data['data'].get('text', '')
                            raise Exception("响应格式解析失败，请检查响应数据结构")
                    else:
                        error_msg = f"讯飞星火API调用失败: HTTP {response.status_code}"
                        try:
                            error_detail = response.json()
                            error_msg += f" - {error_detail.get('message', response.text)}"
                        except:
                            error_msg += f" - {response.text}"
                        raise Exception(error_msg)
                        
                except requests.exceptions.Timeout:
                    retry_count += 1
                    if retry_count == max_retries:
                        raise Exception(f"连接超时，已重试{max_retries}次")
                    print(f"连接超时，正在进行第{retry_count}次重试...")
                    time.sleep(2)  # 重试前等待2秒
                    
                except requests.exceptions.RequestException as e:
                    retry_count += 1
                    if retry_count == max_retries:
                        raise Exception(f"网络请求错误：{str(e)}，已重试{max_retries}次")
                    print(f"网络请求错误，正在进行第{retry_count}次重试...")
                    time.sleep(2)  # 重试前等待2秒
                    
        except Exception as e:
            raise Exception(f"讯飞星火API调用失败：{str(e)}")

    def _call_qianwen_api(self, prompt):
        """调用通义千问接口"""
        try:
            api_base = self.config.get('qianwen_url', '').rstrip('/')
            api_key = self.config.get('qianwen_key', '')
            model = self.config.get('qianwen_model', '')
            
            if not api_base:
                raise Exception("请配置通义千问接口地址")
            if not api_key:
                raise Exception("请配置通义千问 API Key")
            if not model:
                raise Exception("请配置通义千问模型名称")

            if not api_base.endswith('/v1/chat/completions'):
                api_base = f"{api_base}/v1/chat/completions"

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {api_key}'
            }

            payload = {
                'model': model,
                'messages': [{'role': 'user', 'content': prompt}],
                'temperature': 0.7,
                'max_tokens': 2048
            }

            response = requests.post(
                api_base,
                headers=headers,
                json=payload,
                verify=True,
                timeout=60
            )

            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
            else:
                error_msg = f"通义千问 API调用失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', response.text)}"
                except:
                    error_msg += f" - {response.text}"
                raise Exception(error_msg)

        except Exception as e:
            raise Exception(f"通义千问 API调用失败：{str(e)}")

    def _call_openai_api(self, prompt):
        try:
            api_base = self.config.get('openai_url', '').rstrip('/')
            api_key = self.config.get('openai_key', '')
            model = self.config.get('openai_model', '')
            
            if not api_base:
                raise Exception("请配置OpenAI兼容接口地址")
            if not api_key:
                raise Exception("请配置OpenAI API Key")
            if not model:
                raise Exception("请配置OpenAI模型名称")

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {api_key}'
            }

            payload = {
                'model': model,
                'messages': [{'role': 'user', 'content': prompt}],
                'temperature': 0.7,
                'max_tokens': 2048
            }

            response = requests.post(
                api_base,
                headers=headers,
                json=payload,
                verify=True,
                timeout=180
            )

            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
            else:
                error_msg = f"OpenAI API调用失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', response.text)}"
                except:
                    error_msg += f" - {response.text}"
                raise Exception(error_msg)

        except Exception as e:
            raise Exception(f"OpenAI API调用失败：{str(e)}")

    def _call_gemini_api(self, prompt):
        try:
            api_base = self.config.get('gemini_url', '').rstrip('/')
            api_key = self.config.get('gemini_key', '')
            model = self.config.get('gemini_model', 'gemini-pro')
            
            if not api_base:
                raise Exception("请配置Gemini接口地址")
            if not api_key:
                raise Exception("请配置Gemini API Key")
            if not model:
                raise Exception("请配置Gemini模型名称")

            # 构建完整的API URL
            url = f"{api_base}/{model}:generateContent?key={api_key}"

            headers = {
                'Content-Type': 'application/json'
            }

            payload = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 2048,
                }
            }

            response = requests.post(
                url,
                headers=headers,
                json=payload,
                verify=True,
                timeout=60
            )

            if response.status_code == 200:
                response_data = response.json()
                try:
                    # 提取生成的文本内容
                    candidates = response_data.get('candidates', [])
                    if candidates and len(candidates) > 0:
                        content = candidates[0].get('content', {})
                        parts = content.get('parts', [])
                        if parts and len(parts) > 0:
                            return parts[0].get('text', '')
                    raise Exception("响应中未找到生成的文本内容")
                except Exception as e:
                    print(f"解析Gemini响应时出错：{str(e)}")
                    print(f"完整响应：{response_data}")
                    raise Exception("解析响应数据失败")
            else:
                error_msg = f"Gemini API调用失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', response.text)}"
                except:
                    error_msg += f" - {response.text}"
                raise Exception(error_msg)

        except Exception as e:
            raise Exception(f"Gemini API调用失败：{str(e)}")

    def _calculate_similarity(self, text1, text2):
        """计算两个文本的相似度"""
        try:
            # 对文本进行分词和清理
            def clean_text(text):
                # 移除URL、特殊字符等
                text = re.sub(r'http\S+|www.\S+', '', text)
                text = re.sub(r'[^\w\s]', '', text)
                # 分词
                words = jieba.lcut(text)
                return set(words)
            
            # 获取两个文本的词集合
            words1 = clean_text(text1)
            words2 = clean_text(text2)
            
            # 计算Jaccard相似度
            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))
            
            return intersection / union if union > 0 else 0
            
        except Exception as e:
            print(f"计算相似度时出错：{str(e)}")
            return 0

    def _normalize_hot_index(self, hot_index):
        """统一热度值的格式并转换为数值"""
        try:
            # 移除非数字字符
            hot_str = ''.join(filter(str.isdigit, str(hot_index)))
            return float(hot_str) if hot_str else 0
        except:
            return 0

    def collect_hot_events(self):
        """搜集热点事件"""
        try:
            self.collect_events_btn.setEnabled(False)
            self.collect_events_btn.setText("正在搜集热点事件...")
            
            # 清空现有事件
            self.hot_events = []
            self.events_list.clear()
            self.event_edit.clear()
            self.content_edit.clear()
            
            # 从各个平台搜集热点事件
            events = []
            
            # 微博热搜
            try:
                weibo_events = self._get_weibo_hot()
                events.extend(weibo_events)
            except Exception as e:
                print(f"获取微博热搜失败：{str(e)}")
            
            # 百度热搜
            try:
                baidu_events = self._get_baidu_hot()
                events.extend(baidu_events)
            except Exception as e:
                print(f"获取百度热搜失败：{str(e)}")
            
            # 抖音热榜
            try:
                douyin_events = self._get_douyin_hot()
                events.extend(douyin_events)
            except Exception as e:
                print(f"获取抖音热榜失败：{str(e)}")
            
            # 对事件进行分组和排序
            self.hot_events = self._group_similar_events(events)
            
            # 更新事件列表
            for event in self.hot_events:
                self.events_list.addItem(event['title'])
            
            if self.hot_events:
                self.events_list.setCurrentIndex(0)
                
            self.collect_events_btn.setText("搜集热点事件（约需1分钟，请耐心等待）")
            self.collect_events_btn.setEnabled(True)
            
            # 显示统计信息
            total_events = len(self.hot_events)
            multi_source_events = sum(1 for event in self.hot_events if '[' in event['title'] and '、' in event['title'])
            
            QMessageBox.information(
                self, 
                "提示", 
                f"已搜集到 {total_events} 个热点事件\n"
                f"其中 {multi_source_events} 个事件出现在多个平台"
            )
            
        except Exception as e:
            self.collect_events_btn.setText("搜集热点事件（约需1分钟，请耐心等待）")
            self.collect_events_btn.setEnabled(True)
            QMessageBox.critical(self, "错误", f"搜集热点事件失败：{str(e)}")

    def _create_session(self):
        """创建一个配置好的请求会话"""
        session = requests.Session()
        session.verify = False  # 禁用SSL验证
        
        try:
            # 从配置文件中读取代理设置
            with open('config.json', 'r') as f:
                config = json.load(f)
                proxy_enabled = config.get('proxy_enabled', 0)
                proxy_host = config.get('proxy_host', '')
                proxy_port = config.get('proxy_port', '')
                
                if proxy_enabled == 1 and proxy_host and proxy_port:
                    proxies = {
                        'http': f'http://{proxy_host}:{proxy_port}',
                        'https': f'http://{proxy_host}:{proxy_port}'
                    }
                    session.proxies = proxies
                    session.trust_env = False
                    
                    # 设置更长的超时时间
                    session.timeout = 30
                    
                    # 配置SSL
                    session.verify = False
                    requests.packages.urllib3.disable_warnings()
                    
                    # 配置连接池
                    adapter = requests.adapters.HTTPAdapter(
                        pool_connections=5,
                        pool_maxsize=10,
                        max_retries=3,
                        pool_block=False
                    )
                    session.mount('http://', adapter)
                    session.mount('https://', adapter)
                    
                    print(f"代理已启用: {proxies}")
                else:
                    # 清除代理设置
                    session.proxies = {}
                    if 'HTTP_PROXY' in os.environ:
                        del os.environ['HTTP_PROXY']
                    if 'HTTPS_PROXY' in os.environ:
                        del os.environ['HTTPS_PROXY']
                    print("代理已禁用")
                    
        except Exception as e:
            print(f"加载代理配置失败：{str(e)}")
            session.proxies = {}
        
        return session

    def _get_weibo_hot(self):
        """获取微博热搜"""
        events = []
        try:
            # 在Windows 7上使用更保守的SSL设置
            requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS = 'ALL:@SECLEVEL=1'
            
            # 创建配置好的会话
            session = self._create_session()
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Cookie': 'SUB=_2AkMW'
            }
            
            # 使用session进行请求
            try:
                response = session.get(
                    'https://weibo.com/ajax/side/hotSearch',
                    headers=headers,
                    timeout=30,
                    verify=False  # 禁用SSL验证
                )
                response.raise_for_status()  # 检查响应状态
                
                # 微博热搜榜API
                data = response.json()
                
                if 'data' in data and 'realtime' in data['data']:
                    hot_items = data['data']['realtime']
                    
                    for item in hot_items:
                        try:
                            title = item.get('note', '')
                            if not title:  # 如果note为空，尝试使用word字段
                                title = item.get('word', '')
                            hot_index = item.get('num', '0')  # 热度值
                            category = item.get('category', '')  # 分类
                            link = f"https://s.weibo.com/weibo?q={requests.utils.quote(title)}"
                            
                            # 获取话题详情（可选，如果获取失败也不影响主要功能）
                            try:
                                detail_response = session.get(
                                    link,
                                    headers=headers,
                                    timeout=10,
                                    verify=False
                                )
                                detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
                                content_elements = detail_soup.find_all('div', class_='card-feed')
                                content = []
                                for element in content_elements[:3]:
                                    text = element.get_text().strip()
                                    if text:
                                        content.append(text)
                            except Exception as e:
                                print(f"获取微博详情失败：{str(e)}")
                                content = []
                            
                            event = {
                                'title': title,
                                'content': '\n'.join(content) if content else f'微博热搜：{title}',
                                'hot_index': f'{hot_index}热度',
                                'category': category,
                                'source': '微博热搜',
                                'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'link': link
                            }
                            events.append(event)
                            print(f"成功获取微博热搜：{title}")
                            
                        except Exception as e:
                            print(f"处理微博热搜项目时出错：{str(e)}")
                            continue
                    
            except requests.exceptions.RequestException as e:
                print(f"请求微博API失败：{str(e)}")
                raise
                
        except Exception as e:
            print(f"获取微博热搜失败：{str(e)}")
            
        return events

    def _get_baidu_hot(self):
        """获取百度热搜"""
        events = []
        try:
            # 在Windows 7上使用更保守的SSL设置
            requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS = 'ALL:@SECLEVEL=1'
            
            # 创建配置好的会话
            session = self._create_session()
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 使用session进行请求
            try:
                response = session.get(
                    'https://top.baidu.com/board?tab=realtime',
                    headers=headers,
                    timeout=30,
                    verify=False
                )
                response.raise_for_status()
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 解析百度热搜榜
                hot_items = soup.find_all('div', class_='category-wrap_iQLoo')
                
                for item in hot_items:
                    try:
                        title = item.find('div', class_='c-single-text-ellipsis')
                        content = item.find('div', class_='hot-desc_1m_jR')
                        hot_index = item.find('div', class_='hot-index_1Bl1a')
                        
                        if title and content and hot_index:
                            title_text = title.text.strip()
                            content_text = content.text.strip()
                            hot_index_text = hot_index.text.strip()
                            
                            # 获取详细内容
                            try:
                                search_url = f"https://www.baidu.com/s?wd={requests.utils.quote(title_text)}"
                                detail_response = session.get(
                                    search_url,
                                    headers=headers,
                                    timeout=10,
                                    verify=False
                                )
                                detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
                                
                                # 获取搜索结果中的新闻内容
                                news_contents = []
                                news_items = detail_soup.find_all('div', class_='c-container')[:3]
                                
                                for news_item in news_items:
                                    text = news_item.get_text().strip()
                                    if text:
                                        news_contents.append(text)
                            except Exception as e:
                                print(f"获取百度详情失败：{str(e)}")
                                news_contents = []
                            
                            event = {
                                'title': title_text,
                                'content': content_text + '\n\n' + '\n'.join(news_contents) if news_contents else content_text,
                                'hot_index': hot_index_text,
                                'source': '百度热搜',
                                'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'link': search_url
                            }
                            events.append(event)
                            print(f"成功获取百度热搜：{title_text}")
                            
                    except Exception as e:
                        print(f"处理百度热搜项目时出错：{str(e)}")
                        continue
                        
            except requests.exceptions.RequestException as e:
                print(f"请求百度API失败：{str(e)}")
                raise
                    
        except Exception as e:
            print(f"获取百度热搜失败：{str(e)}")
            
        return events

    def _get_douyin_hot(self):
        """获取抖音热榜"""
        events = []
        try:
            # 创建配置好的会话
            session = self._create_session()
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Referer': 'https://www.douyin.com/',
                'Origin': 'https://www.douyin.com'
            }
            
            # 首先尝试使用抖音官方API
            try:
                response = session.get(
                    'https://www.douyin.com/aweme/v1/web/hot/search/list/',
                    headers=headers,
                    timeout=30,
                    verify=False
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        hot_list = data.get('data', {}).get('word_list', [])
                        
                        for item in hot_list:
                            try:
                                title = item.get('word', '')
                                hot_value = item.get('hot_value', 0)
                                
                                # 构建搜索链接
                                search_url = f"https://www.douyin.com/search/{requests.utils.quote(title)}"
                                
                                # 获取相关视频内容
                                video_api_url = f'https://www.douyin.com/aweme/v1/web/search/item/?keyword={requests.utils.quote(title)}&count=3'
                                video_response = session.get(
                                    video_api_url,
                                    headers=headers,
                                    timeout=10,
                                    verify=False
                                )
                                video_data = video_response.json()
                                
                                content = []
                                if 'data' in video_data:
                                    for video in video_data['data'][:3]:
                                        desc = video.get('desc', '')
                                        if desc:
                                            content.append(desc)
                                
                                event = {
                                    'title': title,
                                    'content': '\n'.join(content) if content else f'抖音热榜：{title}',
                                    'hot_index': f'{hot_value:,} 热度',
                                    'source': '抖音热榜',
                                    'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    'link': search_url
                                }
                                events.append(event)
                                print(f"成功获取抖音热榜：{title}")
                                
                                # 添加延时，避免请求过快
                                time.sleep(1)
                                
                            except Exception as e:
                                print(f"处理抖音热榜项目时出错：{str(e)}")
                                continue
                            
                    except Exception as e:
                        print(f"解析抖音官方API数据失败：{str(e)}")
                else:
                    print(f"抖音官方API请求失败，状态码：{response.status_code}")
                    
            except Exception as e:
                print(f"访问抖音官方API失败：{str(e)}")
            
            # 如果官方API获取失败，尝试使用备用数据源
            if not events:
                print("尝试使用备用数据源获取抖音热榜...")
                backup_apis = [
                    {
                        'url': "https://api.oioweb.cn/api/common/HotList",
                        'type': 'oioweb'
                    },
                    {
                        'url': "https://tenapi.cn/v2/douyinhot",
                        'type': 'tenapi'
                    }
                ]
                
                for api in backup_apis:
                    if events:  # 如果已经获取到数据，就跳过后续API
                        break
                        
                    try:
                        response = session.get(
                            api['url'],
                            headers=headers,
                            timeout=30,
                            verify=False
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            
                            if api['type'] == 'oioweb':
                                if data.get('code') == 200:
                                    douyin_list = data.get('result', {}).get('DouYinHotList', [])
                                    for item in douyin_list:
                                        try:
                                            title = item.get('title', '')
                                            hot_value = item.get('hot', '未知')
                                            link = item.get('url', '')
                                            if not link:
                                                link = f"https://www.douyin.com/search/{requests.utils.quote(title)}"
                                            
                                            event = {
                                                'title': title,
                                                'content': f'抖音热榜：{title}\n热度：{hot_value}',
                                                'hot_index': str(hot_value),
                                                'source': '抖音热榜',
                                                'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                                'link': link
                                            }
                                            events.append(event)
                                            print(f"成功从oioweb获取抖音热榜：{title}")
                                        except Exception as e:
                                            print(f"处理oioweb抖音热榜项目时出错：{str(e)}")
                                            continue
                            
                            elif api['type'] == 'tenapi':
                                if data.get('code') == 200:
                                    douyin_list = data.get('data', [])
                                    for item in douyin_list:
                                        try:
                                            title = item.get('name', '')
                                            hot_value = item.get('hot', '未知')
                                            link = item.get('url', '')
                                            if not link:
                                                link = f"https://www.douyin.com/search/{requests.utils.quote(title)}"
                                            
                                            event = {
                                                'title': title,
                                                'content': f'抖音热榜：{title}\n热度：{hot_value}',
                                                'hot_index': str(hot_value),
                                                'source': '抖音热榜',
                                                'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                                'link': link
                                            }
                                            events.append(event)
                                            print(f"成功从tenapi获取抖音热榜：{title}")
                                        except Exception as e:
                                            print(f"处理tenapi抖音热榜项目时出错：{str(e)}")
                                            continue
                                        
                    except Exception as e:
                        print(f"请求备用API {api['url']} 失败：{str(e)}")
                        continue
            
            if not events:
                print("警告：所有抖音数据源都获取失败")
            
        except Exception as e:
            print(f"获取抖音热榜失败：{str(e)}")
            
        return events

    def _group_similar_events(self, events):
        """对事件进行分组和排序"""
        try:
            # 存储事件组
            event_groups = []
            processed_events = set()
            
            # 遍历所有事件
            for i, event1 in enumerate(events):
                if i in processed_events:
                    continue
                    
                # 创建新的事件组
                current_group = {
                    'events': [event1],
                    'sources': {event1['source']},
                    'total_hot_index': self._normalize_hot_index(event1['hot_index'])
                }
                processed_events.add(i)
                
                # 查找相似事件
                for j, event2 in enumerate(events[i+1:], i+1):
                    if j in processed_events:
                        continue
                        
                    # 计算标题相似度
                    title_similarity = self._calculate_similarity(event1['title'], event2['title'])
                    # 计算内容相似度
                    content_similarity = self._calculate_similarity(event1['content'], event2['content'])
                    
                    # 如果标题或内容相似度超过阈值，认为是相似事件
                    if title_similarity > 0.3 or content_similarity > 0.2:
                        current_group['events'].append(event2)
                        current_group['sources'].add(event2['source'])
                        current_group['total_hot_index'] += self._normalize_hot_index(event2['hot_index'])
                        processed_events.add(j)
                
                event_groups.append(current_group)
            
            # 对事件组进行排序
            # 1. 首先按照来源数量排序（多个来源的优先）
            # 2. 然后按照总热度排序
            sorted_groups = sorted(event_groups, 
                                 key=lambda x: (len(x['sources']), x['total_hot_index']), 
                                 reverse=True)
            
            # 展平排序后的事件组
            sorted_events = []
            for group in sorted_groups:
                # 对组内事件按照热度排序
                group_events = sorted(group['events'], 
                                    key=lambda x: self._normalize_hot_index(x['hot_index']), 
                                    reverse=True)
                
                # 处理事件标题
                if len(group['sources']) > 1:
                    # 多源事件，使用所有来源
                    sources_str = '、'.join(sorted(group['sources']))
                else:
                    # 单源事件，使用单个来源
                    sources_str = next(iter(group['sources']))
                
                # 为所有事件添加标记
                for event in group_events:
                    # 移除可能存在的旧标记
                    title = event['title']
                    if '[' in title and ']' in title:
                        title = title.split(']')[-1].strip()
                    event['title'] = f"[{sources_str}] {title}"
                
                sorted_events.extend(group_events)
            
            return sorted_events
            
        except Exception as e:
            print(f"分组事件时出错：{str(e)}")
            return events  # 如果出错，返回原始事件列表

    def on_event_selected(self, index):
        """当选择事件时更新事件详情"""
        if 0 <= index < len(self.hot_events):
            event = self.hot_events[index]
            detail = f"标题：{event['title']}\n\n来源：{event['source']}\n\n热度：{event['hot_index']}\n\n详情：{event['content']}"
            self.event_edit.setText(detail)

    def copy_to_content(self):
        """复制事件详情到内容编辑区"""
        self.content_edit.setText(self.event_edit.toPlainText())

    def generate_content(self):
        """生成内容"""
        try:
            content = self.content_edit.toPlainText()
            if not content:
                # 如果内容编辑区为空，检查事件详情区
                event_content = self.event_edit.toPlainText()
                if not event_content:
                    QMessageBox.warning(self, "警告", "请先选择事件或输入内容")
                    return
                # 如果事件详情区有内容，自动复制到内容编辑区
                self.content_edit.setText(event_content)
                content = event_content
                
            self.generate_btn.setEnabled(False)
            self.generate_btn.setText("正在生成内容...")
            
            # 获取设定的字数
            target_length = self.get_summary_length()
            length_requirement = f"{target_length}字" if target_length > 0 else "800-1000字"
            
            prompt = f"""请根据以下内容生成一篇新闻报道：

{content}

要求：
1. 使用新闻报道的写作风格
2. 内容要客观、准确
3. 篇幅要求：{length_requirement}
4. 分段清晰，结构完整
5. 标题要吸引人但不夸张
6. 如果内容超过要求字数，请适当精简，保留核心信息
7. 如果内容不足要求字数，请适当扩充，增加相关背景信息
"""
            
            response = self.call_ai_api(prompt)
            if response:
                self.content_edit.setText(response)
                
            self.generate_btn.setText("生成内容（约需20秒）")
            self.generate_btn.setEnabled(True)
            
        except Exception as e:
            self.generate_btn.setText("生成内容（约需20秒）")
            self.generate_btn.setEnabled(True)
            QMessageBox.critical(self, "错误", f"生成内容失败：{str(e)}")

    def polish_content(self):
        """润色内容"""
        try:
            content = self.content_edit.toPlainText()
            if not content:
                QMessageBox.warning(self, "警告", "请先生成或输入内容")
                return
                
            self.polish_btn.setEnabled(False)
            self.polish_btn.setText("正在润色内容...")
            
            # 获取设定的字数
            target_length = self.get_summary_length()
            length_requirement = f"{target_length}字" if target_length > 0 else "保持原文字数"
            
            prompt = f"""请对以下内容进行润色和优化：

{content}

要求：
1. 保持原文的主要信息和观点
2. 改善文字表达，使其更加流畅自然
3. 增加适当的修辞，提升文章的可读性
4. 调整段落结构，使文章更有层次感
5. 保持新闻报道的客观性和专业性
6. 字数要求：{length_requirement}
7. 如果内容超过要求字数，请适当精简，保留核心信息
8. 如果内容不足要求字数，请适当扩充，增加相关背景信息
"""
            
            response = self.call_ai_api(prompt)
            if response:
                self.content_edit.setText(response)
                
            self.polish_btn.setText("润色内容（约需20秒）")
            self.polish_btn.setEnabled(True)
            
        except Exception as e:
            self.polish_btn.setText("润色内容（约需20秒）")
            self.polish_btn.setEnabled(True)
            QMessageBox.critical(self, "错误", f"润色内容失败：{str(e)}")

    def validate_summary_length(self, text):
        """验证输入的字数是否有效"""
        try:
            if text:
                length = int(text)
                if length < 0 or length > 1000:
                    self.summary_length.setText("200")
                    QMessageBox.warning(self, "警告", "请输入0-1000之间的数字")
        except ValueError:
            self.summary_length.setText("200")
            QMessageBox.warning(self, "警告", "请输入有效的数字")

    def get_summary_length(self):
        """获取当前设置的精简字数"""
        try:
            length = int(self.summary_length.text() or "200")
            return min(max(0, length), 1000)  # 确保在0-1000范围内
        except ValueError:
            return 200  # 默认值

if __name__ == '__main__':
    # 设置环境变量，指定Qt插件路径
    import os
    os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = os.path.join(os.path.dirname(sys.executable), "Lib", "site-packages", "PyQt5", "Qt5", "plugins")
    
    app = QApplication(sys.argv)
    app.setStyle(QStyleFactory.create('Fusion'))  # 使用Fusion风格，在Windows 7上也能正常显示
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())