import sys
import os
import re
import pandas as pd
import datetime
import traceback
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QFileDialog, QTabWidget, QTableWidget,
                             QTableWidgetItem, QComboBox, QMessageBox, QLineEdit, QDialog,
                             QFormLayout, QGroupBox, QScrollArea, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn

class EditDialog(QDialog):
    """编辑数据对话框"""
    def __init__(self, parent=None, clue="", name="", status="", town="", clue_type=""):
        super().__init__(parent)
        self.setWindowTitle("编辑数据")
        self.setMinimumWidth(600)
        
        # 创建表单布局
        form_layout = QFormLayout()
        
        # 线索标题
        self.clue_edit = QLineEdit(clue)
        form_layout.addRow("线索标题:", self.clue_edit)
        
        # 姓名
        self.name_edit = QLineEdit(name)
        form_layout.addRow("姓名:", self.name_edit)
        
        # 稳控状态
        self.status_combo = QComboBox()
        self.status_combo.addItems(["已稳控", "工作中", "移交外省"])
        self.status_combo.setCurrentText(status)
        form_layout.addRow("稳控状态:", self.status_combo)
        
        # 地区
        self.town_edit = QLineEdit(town)
        form_layout.addRow("地区:", self.town_edit)
        
        # 类别
        self.type_combo = QComboBox()
        self.type_combo.addItems(["涉众型经济利益群体", "楼盘、商铺业主群体", "其他涉稳线索"])
        self.type_combo.setCurrentText(clue_type)
        form_layout.addRow("类别:", self.type_combo)
        
        # 保存按钮
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.accept)
        
        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(cancel_button)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addLayout(form_layout)
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
    
    def get_values(self):
        return {
            'clue': self.clue_edit.text(),
            'name': self.name_edit.text(),
            'status': self.status_combo.currentText(),
            'town': self.town_edit.text(),
            'type': self.type_combo.currentText()
        }

class DailyAnalysisAssistant(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("每日分析研判助手")
        self.setMinimumSize(1200, 800)
        
        # 设置应用图标
        self.set_app_icon()
        
        # 数据变量
        self.file_path = None
        self.excel_data = {}
        self.instruction_df = None
        self.related_people_df = None
        self.processed_data = []
        self.clue_types = {}  # 存储线索类型
        
        # 创建UI
        self.create_ui()
    
    def set_app_icon(self):
        """设置应用图标"""
        # 尝试从不同位置加载图标
        icon_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "ico", "9.ico"),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "9.ico"),
            "ico/9.ico",
            "9.ico"
        ]
        
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
                break

    def create_ui(self):
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 顶部区域 - 文件选择和操作区
        top_group = QGroupBox("文件操作")
        top_layout = QHBoxLayout()
        top_group.setLayout(top_layout)
        
        # 文件选择按钮
        self.select_file_btn = QPushButton("选择Excel文件")
        self.select_file_btn.clicked.connect(self.select_file)
        top_layout.addWidget(self.select_file_btn)
        
        # 文件名显示
        self.file_label = QLabel("未选择文件")
        top_layout.addWidget(self.file_label)
        top_layout.addStretch(1)
        
        # 添加到主布局
        main_layout.addWidget(top_group)
        
        # 中间区域 - 数据预览和编辑区
        middle_tab = QTabWidget()
        
        # 数据预览选项卡
        self.preview_tab = QTabWidget()
        
        # 指令核查信息预览
        self.instruction_table = QTableWidget()
        self.preview_tab.addTab(self.instruction_table, "指令核查信息")
        
        # 关联人员预览
        self.related_people_table = QTableWidget()
        self.preview_tab.addTab(self.related_people_table, "关联人员")
        
        middle_tab.addTab(self.preview_tab, "数据预览")
        
        # 数据编辑选项卡
        edit_widget = QWidget()
        edit_layout = QVBoxLayout()
        edit_widget.setLayout(edit_layout)
        
        # 创建编辑表格
        self.edit_table = QTableWidget()
        self.edit_table.setColumnCount(5)
        self.edit_table.setHorizontalHeaderLabels(["线索标题", "姓名", "稳控状态", "地区", "类别"])
        
        header = self.edit_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 线索标题列自适应宽度
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.edit_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.edit_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.edit_table.doubleClicked.connect(self.edit_item)
        
        edit_layout.addWidget(self.edit_table)
        
        # 编辑区按钮
        edit_buttons_layout = QHBoxLayout()
        
        process_data_btn = QPushButton("处理数据")
        process_data_btn.clicked.connect(self.process_data)
        edit_buttons_layout.addWidget(process_data_btn)
        
        refresh_view_btn = QPushButton("刷新视图")
        refresh_view_btn.clicked.connect(self.refresh_edit_view)
        edit_buttons_layout.addWidget(refresh_view_btn)
        edit_buttons_layout.addStretch(1)
        
        edit_layout.addLayout(edit_buttons_layout)
        middle_tab.addTab(edit_widget, "数据编辑")
        
        # 添加到主布局
        main_layout.addWidget(middle_tab, 1)
        
        # 底部区域 - 报告生成
        bottom_group = QGroupBox("报告生成")
        bottom_layout = QHBoxLayout()
        bottom_group.setLayout(bottom_layout)
        
        # 生成报告按钮
        generate_report_btn = QPushButton("生成分析报告")
        generate_report_btn.clicked.connect(self.generate_report)
        bottom_layout.addWidget(generate_report_btn)
        
        # 状态信息
        self.status_label = QLabel("")
        bottom_layout.addWidget(self.status_label)
        bottom_layout.addStretch(1)
        
        # 退出按钮
        exit_btn = QPushButton("退出")
        exit_btn.clicked.connect(self.close)
        bottom_layout.addWidget(exit_btn)
        
        # 添加到主布局
        main_layout.addWidget(bottom_group)
        
        # 添加版权信息
        copyright_label = QLabel("版权所有：台州市公安局  解晟")
        copyright_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        main_layout.addWidget(copyright_label)
    
    def select_file(self):
        """选择Excel文件并加载数据"""
        file_types = "Excel文件 (*.xls *.xlsx *.csv);;XLS文件 (*.xls);;XLSX文件 (*.xlsx);;CSV文件 (*.csv)"
        file_path, _ = QFileDialog.getOpenFileName(self, "选择Excel文件", "", file_types)
        
        if not file_path:
            return
        
        self.file_path = file_path
        self.file_label.setText(os.path.basename(file_path))
        
        try:
            # 清空现有数据
            self.excel_data = {}
            
            # 读取Excel文件
            if file_path.endswith('.csv'):
                # 处理CSV文件
                self.excel_data['指令核查信息'] = pd.read_csv(file_path, encoding='utf-8')
            else:
                # 处理Excel文件
                xls = pd.ExcelFile(file_path)
                sheet_names = xls.sheet_names
                
                # 检查是否包含需要的表单
                if '指令核查信息' not in sheet_names or '关联人员' not in sheet_names:
                    QMessageBox.critical(self, "错误", "文件必须包含'指令核查信息'和'关联人员'两个表单")
                    return
                
                self.excel_data['指令核查信息'] = pd.read_excel(file_path, sheet_name='指令核查信息')
                self.excel_data['关联人员'] = pd.read_excel(file_path, sheet_name='关联人员')
            
            # 更新数据引用
            self.instruction_df = self.excel_data['指令核查信息']
            self.related_people_df = self.excel_data['关联人员']
            
            # 显示数据预览
            self.display_preview()
            
            # 处理数据
            self.process_data()
            
            QMessageBox.information(self, "成功", "文件加载成功")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件时出错：{str(e)}")
            traceback.print_exc()
    
    def display_preview(self):
        """在预览选项卡中显示加载的数据"""
        # 显示指令核查信息表格
        if self.instruction_df is not None:
            self.instruction_table.clear()
            self.instruction_table.setRowCount(len(self.instruction_df))
            self.instruction_table.setColumnCount(len(self.instruction_df.columns))
            self.instruction_table.setHorizontalHeaderLabels(self.instruction_df.columns)
            
            # 添加数据
            for i, (_, row) in enumerate(self.instruction_df.iterrows()):
                for j, col in enumerate(self.instruction_df.columns):
                    value = str(row[col]) if not pd.isna(row[col]) else ""
                    item = QTableWidgetItem(value)
                    self.instruction_table.setItem(i, j, item)
            
            # 调整列宽
            self.instruction_table.resizeColumnsToContents()
        
        # 显示关联人员表格
        if self.related_people_df is not None:
            self.related_people_table.clear()
            self.related_people_table.setRowCount(len(self.related_people_df))
            self.related_people_table.setColumnCount(len(self.related_people_df.columns))
            self.related_people_table.setHorizontalHeaderLabels(self.related_people_df.columns)
            
            # 添加数据
            for i, (_, row) in enumerate(self.related_people_df.iterrows()):
                for j, col in enumerate(self.related_people_df.columns):
                    value = str(row[col]) if not pd.isna(row[col]) else ""
                    item = QTableWidgetItem(value)
                    self.related_people_table.setItem(i, j, item)
            
            # 调整列宽
            self.related_people_table.resizeColumnsToContents()
    
    def process_data(self):
        """处理Excel数据并提取关键信息"""
        if self.related_people_df is None or self.instruction_df is None:
            QMessageBox.critical(self, "错误", "请先加载Excel文件")
            return
        
        try:
            self.processed_data = []
            self.clue_types = {}
            
            # 获取指令核查信息表行数（不包括表头）
            row_count = len(self.instruction_df)
            
            # 处理关联人员数据
            for i, row in self.related_people_df.iterrows():
                # 提取指令标题
                instruction_title = str(row['指令标题']) if not pd.isna(row['指令标题']) else ""
                
                # 处理指令标题，提取线索内容
                clue = self.extract_clue_from_title(instruction_title)
                
                # 获取姓名
                name = str(row['姓名']) if not pd.isna(row['姓名']) else ""
                
                # 处理稳控状态（将"未反馈"转为"工作中"）
                status = str(row['稳控状态']) if not pd.isna(row['稳控状态']) else ""
                if status == "未反馈":
                    status = "工作中"
                
                # 提取地区信息
                feedback = str(row['反馈内容']) if not pd.isna(row['反馈内容']) else ""
                town = self.extract_town_from_feedback(feedback)
                
                # 确定线索类型
                clue_type = self.determine_clue_type(clue)
                self.clue_types[clue] = clue_type
                
                # 添加到处理后的数据中
                self.processed_data.append({
                    'clue': clue,
                    'name': name,
                    'status': status,
                    'town': town,
                    'type': clue_type
                })
            
            # 更新编辑视图
            self.refresh_edit_view()
            
            self.status_label.setText(f"数据处理完成, 共有{row_count}条指令，{len(self.processed_data)}条人员信息")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理数据时出错：{str(e)}")
            traceback.print_exc()
    
    def extract_clue_from_title(self, title):
        """从指令标题中提取线索内容"""
        try:
            # 使用正则表达式提取"对"字后到最后一个"线索"字(包括"线索"二字)
            match = re.search(r'对(.*线索)', title)
            if match:
                return match.group(1).strip()
            else:
                # 如果没有找到匹配，则返回原始标题
                return title
        except Exception:
            return title
    
    def extract_town_from_feedback(self, feedback):
        """从反馈内容中提取地区信息"""
        try:
            # 首先尝试提取责任单位后的派出所信息
            match = re.search(r'责任单位[：:]\s*(.*?派出所)', feedback)
            if match:
                police_station = match.group(1).strip()
                
                # 处理派出所名称
                return self.normalize_police_station(police_station, feedback)
            
            # 如果找不到责任单位，尝试直接匹配派出所
            match = re.search(r'([\u4e00-\u9fa5]+派出所)', feedback)
            if match:
                police_station = match.group(1).strip()
                return self.normalize_police_station(police_station, feedback)
            
            # 如果找不到派出所信息，尝试提取地址中的县市区和乡镇街道
            return self.extract_location_from_text(feedback)
            
        except Exception as e:
            print(f"提取地区信息出错: {str(e)}")
            return ""
    
    def normalize_police_station(self, station, full_text=""):
        """规范化派出所名称，提取县市区+乡镇街道名称"""
        try:
            # 县市区名称列表
            districts = ["椒江", "黄岩", "路桥", "临海", "温岭", "天台", "仙居", "三门", "玉环", "台州湾新区", "台州"]
            
            # 处理特殊情况："台州市公安局黄岩分局院桥派出所"类型
            complex_pattern = re.search(r'台州市公安局([\u4e00-\u9fa5]+)分局([\u4e00-\u9fa5]+)派出所', station)
            if complex_pattern:
                district = complex_pattern.group(1)  # 提取黄岩
                town = complex_pattern.group(2)      # 提取院桥
                return f"{district}{town}"           # 返回黄岩院桥
            
            # 如果是类似"XX市公安局YY派出所"格式
            city_bureau_match = re.search(r'([\u4e00-\u9fa5]+)[市县]公安局([\u4e00-\u9fa5]+)派出所', station)
            if city_bureau_match:
                city = city_bureau_match.group(1)
                town = city_bureau_match.group(2)
                # 检查city是否是县市区名称
                if city in districts:
                    return f"{city}{town}"
                else:
                    # 尝试在town中查找县市区名称
                    for district in districts:
                        if district in town:
                            return f"{district}{town.replace(district, '')}"
                    return f"{city}{town}"
                
            # 如果是类似"XX分局YY派出所"格式
            division_match = re.search(r'([\u4e00-\u9fa5]+)分局([\u4e00-\u9fa5]+)派出所', station)
            if division_match:
                division = division_match.group(1)
                town = division_match.group(2)
                # 检查division是否是县市区名称
                if division in districts:
                    return f"{division}{town}"
                else:
                    return town
            
            # 直接查找县市区名称
            districts_found = []
            for district in districts:
                if district in station:
                    districts_found.append(district)
            
            # 如果找到了县市区名称，需要确定使用哪一个
            if districts_found:
                # 排除"台州"如果同时存在其他县市区名称
                if "台州" in districts_found and len(districts_found) > 1:
                    districts_found.remove("台州")
                
                # 使用第一个找到的县市区名称
                district_found = districts_found[0]
                
                # 移除县市区名称和"派出所"，剩下的可能是乡镇街道
                town_name = station
                for d in districts_found:
                    town_name = town_name.replace(d, "")
                
                town_name = town_name.replace("派出所", "").replace("分局", "").replace("市公安局", "")
                
                # 如果乡镇街道非空，返回县市区+乡镇街道
                if town_name.strip():
                    return f"{district_found}{town_name.strip()}"
                else:
                    return district_found
            
            # 如果派出所名称中没有县市区，从全文中查找
            if full_text:
                # 提取派出所名称中的乡镇街道部分
                town_name = station.replace("派出所", "").replace("分局", "")
                
                # 在全文中查找县市区
                district_found = None
                for district in districts:
                    if district in full_text and district != "台州":  # 优先使用非"台州"的县市区
                        district_found = district
                        break
                
                if not district_found and "台州" in full_text:  # 如果只有"台州"则使用它
                    district_found = "台州"
                
                if district_found:
                    return f"{district_found}{town_name}"
            
            # 如果实在无法确定，返回原始名称（去掉派出所）
            return station.replace("派出所", "").replace("分局", "")
        except Exception as e:
            print(f"派出所名称规范化错误: {str(e)}")
            return station.replace("派出所", "").replace("分局", "")
            
    def extract_location_from_text(self, text):
        """从文本中提取地址信息"""
        try:
            # 县市区名称列表
            districts = ["椒江", "黄岩", "路桥", "临海", "温岭", "天台", "仙居", "三门", "玉环", "台州湾新区", "台州"]
            
            # 先尝试找县市区名称
            district_found = None
            for district in districts:
                if district in text:
                    district_found = district
                    break
            
            if not district_found:
                return ""
            
            # 查找接近县市区名称的乡镇街道
            # 尝试提取县市区后面紧跟的名称作为乡镇街道
            town_match = re.search(f"{district_found}[区市县]?([\u4e00-\u9fa5]{{1,4}})[镇乡街道区]*", text)
            if town_match:
                town = town_match.group(1)
                if town:
                    return f"{district_found}{town}"
            
            return district_found
        except Exception:
            return ""
    
    def determine_clue_type(self, clue):
        """确定线索类型"""
        # 涉众型经济利益群体关键词
        economic_keywords = ["玖富", "捷越", "中植", "信托", "投资", "集资", "存款", "银行", "原油宝", "e租宝"]
        
        # 楼盘、商铺业主群体关键词
        property_keywords = ["房产", "房地产", "业主", "楼盘", "商铺", "店铺", "小区"]
        
        # 检查关键词
        for keyword in economic_keywords:
            if keyword in clue:
                return "涉众型经济利益群体"
        
        for keyword in property_keywords:
            if keyword in clue:
                return "楼盘、商铺业主群体"
        
        # 默认为其他涉稳线索
        return "其他涉稳线索"
    
    def refresh_edit_view(self):
        """刷新编辑视图"""
        # 清空表格
        self.edit_table.setRowCount(0)
        
        # 添加处理后的数据
        if not self.processed_data:
            return
        
        self.edit_table.setRowCount(len(self.processed_data))
        
        for i, item in enumerate(self.processed_data):
            self.edit_table.setItem(i, 0, QTableWidgetItem(item['clue']))
            self.edit_table.setItem(i, 1, QTableWidgetItem(item['name']))
            self.edit_table.setItem(i, 2, QTableWidgetItem(item['status']))
            self.edit_table.setItem(i, 3, QTableWidgetItem(item['town']))
            self.edit_table.setItem(i, 4, QTableWidgetItem(item['type']))
    
    def edit_item(self):
        """双击编辑表格项"""
        # 获取选中行
        selected_rows = self.edit_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        
        # 获取当前数据
        clue = self.edit_table.item(row, 0).text()
        name = self.edit_table.item(row, 1).text()
        status = self.edit_table.item(row, 2).text()
        town = self.edit_table.item(row, 3).text()
        clue_type = self.edit_table.item(row, 4).text()
        
        # 创建并显示编辑对话框
        dialog = EditDialog(self, clue, name, status, town, clue_type)
        if dialog.exec_() == QDialog.Accepted:
            # 获取编辑后的值
            new_values = dialog.get_values()
            
            # 更新表格
            self.edit_table.setItem(row, 0, QTableWidgetItem(new_values['clue']))
            self.edit_table.setItem(row, 1, QTableWidgetItem(new_values['name']))
            self.edit_table.setItem(row, 2, QTableWidgetItem(new_values['status']))
            self.edit_table.setItem(row, 3, QTableWidgetItem(new_values['town']))
            self.edit_table.setItem(row, 4, QTableWidgetItem(new_values['type']))
            
            # 更新处理后的数据
            old_item = self.processed_data[row]
            if old_item['clue'] == clue and old_item['name'] == name:
                self.processed_data[row] = new_values
                
                # 更新线索类型字典
                self.clue_types[new_values['clue']] = new_values['type']
    
    def generate_report(self):
        """生成分析研判报告"""
        if not self.processed_data:
            QMessageBox.critical(self, "错误", "没有数据可用于生成报告")
            return
        
        try:
            # 创建保存文件对话框
            result = QFileDialog.getSaveFileName(
                self,
                "保存报告",
                "台州市公安局每日分析研判.docx",
                "Word文档 (*.docx)"
            )
            
            # 确保获取的路径是有效的
            file_path = result[0]
            if not file_path:
                return
            
            # 如果文件路径不是以.docx结尾，添加扩展名
            if not file_path.lower().endswith('.docx'):
                file_path += '.docx'
            
            # 获取当前日期，格式为"x月x日"
            today = datetime.datetime.now()
            date_str = f"{today.month}月{today.day}日"
            
            # 获取指令核查信息表行数（不包括表头）
            row_count = len(self.instruction_df) if self.instruction_df is not None else 0
            
            # 创建Word文档
            doc = Document()
            
            # 设置默认字体
            doc.styles['Normal'].font.name = '仿宋_GB2312'
            doc.styles['Normal'].font.size = Pt(16)  # 三号字体约为16pt
            doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
            
            # 添加标题
            title = doc.add_paragraph()
            title_run = title.add_run("台州市公安局每日分析研判")
            title_run.font.name = '方正小标简体'  # 修改为方正小标简体
            title_run.font.size = Pt(22)  # 二号字体约为22pt
            title_run.bold = True
            title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            title_run.font.color.rgb = RGBColor(0, 0, 0)
            
            # 添加第一段
            first_para = doc.add_paragraph()
            first_para_text = f"{date_str}，全市社会大局平稳，未发生重大敏感案事件，预警涉稳线索{row_count}条。具体如下："
            first_para_run = first_para.add_run(first_para_text)
            first_para_run.font.name = '仿宋_GB2312'
            first_para_run.font.size = Pt(16)  # 三号字体
            # 为数字和标点符号设置Times New Roman字体
            for i, char in enumerate(first_para_text):
                if char.isdigit() or char in "，。、；：''""（）《》？【】{}#@!,.;:'\"\\/()<>?[]{}#@!":
                    run = first_para_run
                    run.font.name = 'Times New Roman'
            first_para.paragraph_format.first_line_indent = Pt(32)  # 首行缩进2字符，约32磅
            
            # 按类型组织数据
            economic_items = []
            property_items = []
            other_items = []
            
            for item in self.processed_data:
                if item['type'] == "涉众型经济利益群体":
                    economic_items.append(item)
                elif item['type'] == "楼盘、商铺业主群体":
                    property_items.append(item)
                else:
                    other_items.append(item)
            
            # 按线索分组人员
            def group_by_clue(items):
                clue_groups = {}
                for item in items:
                    clue = item['clue']
                    if clue not in clue_groups:
                        clue_groups[clue] = []
                    clue_groups[clue].append(item)
                return clue_groups
            
            # 初始化分组变量
            eco_groups = group_by_clue(economic_items) if economic_items else {}
            prop_groups = group_by_clue(property_items) if property_items else {}
            other_groups = group_by_clue(other_items) if other_items else {}
            
            # 跟踪当前的标题序号
            title_index = 1
            
            # 添加涉众型经济利益群体内容
            if economic_items:
                # 添加二级标题
                eco_title = doc.add_paragraph()
                eco_title_run = eco_title.add_run(f"{self.get_chinese_num(title_index)}、涉众型经济利益群体")
                eco_title_run.font.name = '黑体'
                eco_title_run.font.size = Pt(16)  # 三号字体
                # 移除加粗
                # eco_title_run.bold = True
                eco_title.paragraph_format.first_line_indent = Pt(32)  # 首行缩进2字符，约32磅
                
                title_index += 1  # 递增标题序号
                
                # 添加具体内容
                for i, (clue, items) in enumerate(eco_groups.items(), 1):
                    para = doc.add_paragraph()
                    
                    # 按稳控状态分组
                    status_groups = {}
                    for item in items:
                        status = item['status']
                        if status not in status_groups:
                            status_groups[status] = []
                        status_groups[status].append(item)
                    
                    # 组织段落
                    text = f"{i}.{clue}，涉及"
                    
                    # 分稳控状态添加人员信息
                    first_status = True
                    for status, status_items in status_groups.items():
                        names_with_location = []
                        for item in status_items:
                            if item['name']:
                                # 添加地区+人名格式
                                location = item['town'] if item['town'] else ""
                                full_name = f"{location}{item['name']}" if location else item['name']
                                names_with_location.append(full_name)
                        
                        if names_with_location:
                            if not first_status:
                                text += "；"
                            text += "、".join(names_with_location) + f"，{status}"
                            first_status = False
                    
                    text += "。"
                    
                    para_run = para.add_run(text)
                    para_run.font.name = '仿宋_GB2312'
                    para_run.font.size = Pt(16)  # 三号字体
                    # 为数字和标点符号设置Times New Roman字体
                    for char in text:
                        if char.isdigit() or char in "，。、；：''""（）《》？【】{}#@!,.;:'\"\\/()<>?[]{}#@!":
                            run = para_run
                            run.font.name = 'Times New Roman'
                    para.paragraph_format.first_line_indent = Pt(32)  # 首行缩进2字符
            
            # 添加楼盘、商铺业主群体内容
            if property_items:
                # 添加二级标题
                prop_title = doc.add_paragraph()
                prop_title_run = prop_title.add_run(f"{self.get_chinese_num(title_index)}、楼盘、商铺业主群体")
                prop_title_run.font.name = '黑体'
                prop_title_run.font.size = Pt(16)  # 三号字体
                # 移除加粗
                # prop_title_run.bold = True
                prop_title.paragraph_format.first_line_indent = Pt(32)  # 首行缩进2字符
                
                title_index += 1  # 递增标题序号
                
                # 添加具体内容
                start_index = len(eco_groups) + 1 if economic_items else 1
                
                for i, (clue, items) in enumerate(prop_groups.items(), start_index):
                    para = doc.add_paragraph()
                    
                    # 按稳控状态分组
                    status_groups = {}
                    for item in items:
                        status = item['status']
                        if status not in status_groups:
                            status_groups[status] = []
                        status_groups[status].append(item)
                    
                    # 组织段落
                    text = f"{i}.{clue}，涉及"
                    
                    # 分稳控状态添加人员信息
                    first_status = True
                    for status, status_items in status_groups.items():
                        names_with_location = []
                        for item in status_items:
                            if item['name']:
                                # 添加地区+人名格式
                                location = item['town'] if item['town'] else ""
                                full_name = f"{location}{item['name']}" if location else item['name']
                                names_with_location.append(full_name)
                        
                        if names_with_location:
                            if not first_status:
                                text += "；"
                            text += "、".join(names_with_location) + f"，{status}"
                            first_status = False
                    
                    text += "。"
                    
                    para_run = para.add_run(text)
                    para_run.font.name = '仿宋_GB2312'
                    para_run.font.size = Pt(16)  # 三号字体
                    # 为数字和标点符号设置Times New Roman字体
                    for char in text:
                        if char.isdigit() or char in "，。、；：''""（）《》？【】{}#@!,.;:'\"\\/()<>?[]{}#@!":
                            run = para_run
                            run.font.name = 'Times New Roman'
                    para.paragraph_format.first_line_indent = Pt(32)  # 首行缩进2字符
            
            # 添加其他涉稳线索内容
            if other_items:
                # 添加二级标题
                other_title = doc.add_paragraph()
                other_title_run = other_title.add_run(f"{self.get_chinese_num(title_index)}、其他涉稳线索")
                other_title_run.font.name = '黑体'
                other_title_run.font.size = Pt(16)  # 三号字体
                # 移除加粗
                # other_title_run.bold = True
                other_title.paragraph_format.first_line_indent = Pt(32)  # 首行缩进2字符
                
                # 添加具体内容
                start_index = len(eco_groups) + len(prop_groups) + 1 if economic_items or property_items else 1
                
                for i, (clue, items) in enumerate(other_groups.items(), start_index):
                    para = doc.add_paragraph()
                    
                    # 按稳控状态分组
                    status_groups = {}
                    for item in items:
                        status = item['status']
                        if status not in status_groups:
                            status_groups[status] = []
                        status_groups[status].append(item)
                    
                    # 组织段落
                    text = f"{i}.{clue}，涉及"
                    
                    # 分稳控状态添加人员信息
                    first_status = True
                    for status, status_items in status_groups.items():
                        names_with_location = []
                        for item in status_items:
                            if item['name']:
                                # 添加地区+人名格式
                                location = item['town'] if item['town'] else ""
                                full_name = f"{location}{item['name']}" if location else item['name']
                                names_with_location.append(full_name)
                        
                        if names_with_location:
                            if not first_status:
                                text += "；"
                            text += "、".join(names_with_location) + f"，{status}"
                            first_status = False
                    
                    text += "。"
                    
                    para_run = para.add_run(text)
                    para_run.font.name = '仿宋_GB2312'
                    para_run.font.size = Pt(16)  # 三号字体
                    # 为数字和标点符号设置Times New Roman字体
                    for char in text:
                        if char.isdigit() or char in "，。、；：''""（）《》？【】{}#@!,.;:'\"\\/()<>?[]{}#@!":
                            run = para_run
                            run.font.name = 'Times New Roman'
                    para.paragraph_format.first_line_indent = Pt(32)  # 首行缩进2字符
            
            # 添加备注
            remark = doc.add_paragraph()
            remark_run = remark.add_run("备注：数据统计时间自昨日15时至今日15时。")
            remark_run.font.name = '仿宋_GB2312'
            remark_run.font.size = Pt(16)  # 三号字体
            # 为数字和标点符号设置Times New Roman字体
            remark_text = "备注：数据统计时间自昨日15时至今日15时。"
            for char in remark_text:
                if char.isdigit() or char in "，。、；：''""（）《》？【】{}#@!,.;:'\"\\/()<>?[]{}#@!":
                    run = remark_run
                    run.font.name = 'Times New Roman'
            # 添加首行缩进两字符
            remark.paragraph_format.first_line_indent = Pt(32)  # 首行缩进2字符，约32磅
            
            # 保存文档
            doc.save(file_path)
            
            QMessageBox.information(self, "成功", f"分析报告已成功生成：{file_path}")
            
            # 自动打开文档
            os.startfile(file_path)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成报告时出错：{str(e)}")
            traceback.print_exc()

    def get_chinese_num(self, num):
        """将阿拉伯数字转换为中文数字"""
        chinese_nums = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"]
        if 1 <= num <= 10:
            return chinese_nums[num-1]
        return str(num)

if __name__ == "__main__":
    # 创建应用
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 设置应用名称
    app.setApplicationName("每日分析研判助手")
    
    # 创建并显示主窗口
    window = DailyAnalysisAssistant()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())
