import sys
import os
import hashlib
import uuid
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QLabel, QLineEdit, 
                           QMessageBox, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPalette, QColor

class AuthTool(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('话单分析软件授权工具')
        self.setGeometry(300, 300, 600, 400)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                font-size: 12pt;
                color: #333333;
            }
            QLineEdit, QTextEdit {
                padding: 8px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
                font-size: 11pt;
            }
            QPushButton {
                padding: 8px 15px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)

        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title = QLabel('话单分析软件授权工具')
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            font-size: 18pt;
            color: #1565C0;
            margin-bottom: 20px;
        """)
        layout.addWidget(title)

        # 机器码输入区域
        machine_code_layout = QHBoxLayout()
        machine_code_label = QLabel('机器码:')
        self.machine_code_input = QLineEdit()
        self.machine_code_input.setPlaceholderText('请输入需要授权的机器码')
        machine_code_layout.addWidget(machine_code_label)
        machine_code_layout.addWidget(self.machine_code_input)
        layout.addLayout(machine_code_layout)

        # 生成授权码按钮
        generate_btn = QPushButton('生成授权码')
        generate_btn.clicked.connect(self.generate_auth_code)
        layout.addWidget(generate_btn)

        # 授权码显示区域
        auth_code_layout = QVBoxLayout()
        self.auth_code_display = QTextEdit()
        self.auth_code_display.setReadOnly(True)
        self.auth_code_display.setPlaceholderText('授权码将在这里显示')
        copy_btn = QPushButton('复制授权码')
        copy_btn.clicked.connect(self.copy_auth_code)
        auth_code_layout.addWidget(self.auth_code_display)
        auth_code_layout.addWidget(copy_btn)
        layout.addLayout(auth_code_layout)

        # 版权信息
        copyright_label = QLabel('版权所有：台州市公安局 解晟')
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            color: #666666;
            font-size: 10pt;
            margin-top: 20px;
        """)
        layout.addWidget(copyright_label)

    def generate_auth_code(self):
        machine_code = self.machine_code_input.text().strip()
        if not machine_code:
            QMessageBox.warning(self, '警告', '请输入机器码！')
            return

        try:
            # 使用自定义密钥对机器码进行加密
            key = "TaiZhouPolice2024"  # 加密密钥
            auth_code = self.encrypt_code(machine_code, key)
            
            # 显示授权码
            self.auth_code_display.setText(auth_code)
            QMessageBox.information(self, '成功', '授权码已生成！')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'生成授权码时出错：{str(e)}')

    def encrypt_code(self, machine_code, key):
        # 将机器码和密钥组合后进行MD5加密
        combined = f"{machine_code}:{key}"
        md5_hash = hashlib.md5(combined.encode()).hexdigest()
        # 返回大写的授权码
        return md5_hash.upper()

    def copy_auth_code(self):
        auth_code = self.auth_code_display.toPlainText()
        if auth_code:
            clipboard = QApplication.clipboard()
            clipboard.setText(auth_code)
            QMessageBox.information(self, '成功', '授权码已复制到剪贴板！')
        else:
            QMessageBox.warning(self, '提示', '请先生成授权码！')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AuthTool()
    window.show()
    sys.exit(app.exec_()) 