import base64
import os
import pandas as pd

def generate_base64_string(file_path):
    """生成文件的base64编码字符串"""
    with open(file_path, 'rb') as f:
        data = f.read()
        # 确保生成的base64字符串格式正确
        b64_str = base64.b64encode(data).decode('utf-8')
        # 每76个字符添加换行符，使代码更易读
        chunks = [b64_str[i:i+76] for i in range(0, len(b64_str), 76)]
        return '"""\n' + '\n'.join(chunks) + '\n"""'

def main():
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 创建一个新的Python文件来存储base64数据
    output_file = os.path.join(current_dir, 'base64_data.py')
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# 这个文件由生成base64数据.py自动生成\n\n")
        f.write("# CSV文件的编码格式\n")
        f.write("CSV_ENCODING = 'gbk'\n\n")
        
        # 写入手机归属地数据
        phone_file = os.path.join(current_dir, '手机归属地.csv')
        if os.path.exists(phone_file):
            phone_data = generate_base64_string(phone_file)
            f.write("PHONE_DATA_BASE64 = " + phone_data + "\n\n")
        else:
            print(f"找不到文件: {phone_file}")
        
        # 写入行政区域代码数据
        area_file = os.path.join(current_dir, '行政区域代码与省市县.xlsx')
        if os.path.exists(area_file):
            area_data = generate_base64_string(area_file)
            f.write("AREA_DATA_BASE64 = " + area_data + "\n")
        else:
            print(f"找不到文件: {area_file}")
    
    print(f"已生成文件: {output_file}")
    print("请将生成的base64_data.py中的变量复制到身份证与手机号码处理工具_打包版.py中")

if __name__ == '__main__':
    main() 