import requests
import base64
import time
import json
from datetime import datetime
import os
from urllib.parse import urlparse
import logging
from concurrent.futures import ThreadPoolExecutor
import hashlib
from typing import List, Dict
import concurrent.futures

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('node_collector.log'),
        logging.StreamHandler()
    ]
)

class NodeCollector:
    def __init__(self):
        self.nodes_dir = "nodes"
        # 使用更多的节点源，包括一些国内可以访问的源
        self.sources = [
            # GitHub源
            "https://raw.githubusercontent.com/freefq/free/master/v2",
            "https://raw.githubusercontent.com/Pawdroid/Free-servers/main/sub",
            "https://raw.githubusercontent.com/aiboboxx/v2rayfree/main/v2",
            # GitHub镜像源
            "https://raw.fastgit.org/freefq/free/master/v2",
            "https://raw.fastgit.org/Pawdroid/Free-servers/main/sub",
            "https://raw.fastgit.org/aiboboxx/v2rayfree/main/v2",
            # 备用源
            "https://ghproxy.com/https://raw.githubusercontent.com/freefq/free/master/v2",
            "https://ghproxy.com/https://raw.githubusercontent.com/Pawdroid/Free-servers/main/sub",
            "https://ghproxy.com/https://raw.githubusercontent.com/aiboboxx/v2rayfree/main/v2",
            # 添加一些直接的订阅源
            "https://sub.xeton.dev/sub?target=clash&url=https://raw.githubusercontent.com/freefq/free/master/v2",
            "https://api.v1.mk/sub?target=mixed&url=https://raw.githubusercontent.com/freefq/free/master/v2",
        ]
        
        # 代理配置
        self.proxy_options = [
            None,  # 无代理直连
            {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            },
            {
                'http': 'http://127.0.0.1:1080',
                'https': 'http://127.0.0.1:1080'
            },
            # 添加更多可能的代理端口
            {
                'http': 'http://127.0.0.1:8080',
                'https': 'http://127.0.0.1:8080'
            },
            {
                'http': 'http://127.0.0.1:10809',
                'https': 'http://127.0.0.1:10809'
            }
        ]
        self.create_dirs()
        self.session = requests.Session()
        # 设置重试次数
        self.max_retries = 3

    def create_dirs(self):
        """创建必要的目录"""
        if not os.path.exists(self.nodes_dir):
            os.makedirs(self.nodes_dir)

    def decode_base64(self, b64_str):
        """解码Base64字符串"""
        try:
            # 清理输入字符串
            b64_str = b64_str.strip()
            b64_str = b64_str.replace('-', '+').replace('_', '/')
            # 添加填充
            padding = 4 - (len(b64_str) % 4)
            if padding != 4:
                b64_str += '=' * padding
            return base64.b64decode(b64_str).decode('utf-8', errors='ignore')
        except Exception as e:
            logging.debug(f"Base64 decode failed: {str(e)}")
            return None

    def fetch_nodes(self, url: str) -> List[str]:
        """从源获取节点"""
        for retry in range(self.max_retries):
            for proxy in self.proxy_options:
                try:
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                    response = self.session.get(
                        url,
                        headers=headers,
                        timeout=15,
                        proxies=proxy,
                        verify=False
                    )
                    
                    if response.status_code == 200:
                        content = response.text
                        # 尝试Base64解码
                        decoded_content = self.decode_base64(content)
                        if decoded_content:
                            content = decoded_content
                        
                        # 提取有效节点
                        nodes = []
                        for line in content.splitlines():
                            line = line.strip()
                            if any(protocol in line for protocol in ['ss://', 'vmess://', 'trojan://', 'vless://']):
                                nodes.append(line)
                        
                        if nodes:
                            logging.info(f"Successfully found {len(nodes)} nodes from {url}")
                            return nodes
                except Exception as e:
                    logging.debug(f"Attempt {retry + 1} failed for {url} with proxy {proxy}: {str(e)}")
                    continue
            
            # 在重试之前等待一段时间
            time.sleep(2)
        
        logging.error(f"All attempts failed for {url}")
        return []

    def save_nodes(self, nodes: List[str]):
        """保存节点到文件"""
        if not nodes:
            return
            
        date_str = datetime.now().strftime('%Y%m%d')
        protocols = {
            'ss': [], 'vmess': [], 'trojan': [], 'vless': []
        }

        # 按协议分类节点
        for node in nodes:
            for protocol in protocols.keys():
                if node.startswith(f'{protocol}://'):
                    protocols[protocol].append(node)

        # 保存节点信息
        for protocol, protocol_nodes in protocols.items():
            if protocol_nodes:
                filename = os.path.join(self.nodes_dir, f'{protocol}_{date_str}.txt')
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(protocol_nodes))
                    logging.info(f"Saved {len(protocol_nodes)} {protocol} nodes to {filename}")
                except Exception as e:
                    logging.error(f"Failed to save {protocol} nodes: {str(e)}")

    def collect(self):
        """收集所有节点"""
        logging.info("Starting node collection...")
        all_nodes = set()  # 使用集合去重
        
        # 使用较小的线程池
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_url = {executor.submit(self.fetch_nodes, url): url for url in self.sources}
            
            for future in concurrent.futures.as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    nodes = future.result()
                    if nodes:  # 只在成功获取到节点时更新
                        all_nodes.update(nodes)
                        logging.info(f"Successfully processed {url}")
                except Exception as e:
                    logging.error(f"Error processing {url}: {str(e)}")

        if all_nodes:  # 只在有节点时保存
            self.save_nodes(list(all_nodes))
            logging.info(f"Collection completed. Total unique nodes: {len(all_nodes)}")
        else:
            logging.warning("No nodes were collected!")

def main():
    """主函数"""
    # 禁用SSL警告
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    import argparse
    parser = argparse.ArgumentParser(description='节点收集器')
    parser.add_argument('--daemon', action='store_true', help='是否以守护进程模式运行（每6小时更新一次）')
    parser.add_argument('--interval', type=int, default=6, help='更新间隔小时数（仅在守护进程模式下生效）')
    args = parser.parse_args()

    try:
        collector = NodeCollector()
        if args.daemon:
            logging.info(f"启动守护进程模式，每{args.interval}小时更新一次")
            while True:
                try:
                    collector.collect()
                    logging.info(f"等待{args.interval}小时后进行下一次更新...")
                    time.sleep(args.interval * 60 * 60)
                except Exception as e:
                    logging.error(f"运行出错: {str(e)}")
                    time.sleep(300)  # 发生错误时等待5分钟后重试
        else:
            collector.collect()
            logging.info("节点收集完成，程序退出")
    except KeyboardInterrupt:
        logging.info("收到退出信号，程序结束")
    except Exception as e:
        logging.error(f"程序异常退出: {str(e)}")

if __name__ == "__main__":
    main()