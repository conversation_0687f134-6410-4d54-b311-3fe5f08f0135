
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named org - imported by copy (optional)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), http.server (delayed, optional), webbrowser (delayed), pathlib (delayed, conditional, optional), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), netrc (delayed, conditional), getpass (delayed)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by url<PERSON>b (delayed, optional), lxml.html (delayed, optional)
missing module named termios - imported by tty (top-level), getpass (optional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named java - imported by platform (delayed)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named _winreg - imported by platform (delayed, optional), pkg_resources._vendor.appdirs (delayed, conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional)
missing module named 'org.python' - imported by pickle (optional), setuptools.sandbox (conditional), xml.sax (delayed, conditional)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed), distutils.archive_util (optional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional), C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_win32comgenpy.py (top-level)
missing module named pyimod03_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (top-level), C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (top-level)
missing module named _manylinux - imported by pkg_resources._vendor.packaging._manylinux (delayed, optional), packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.markers (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named _uuid - imported by uuid (optional)
missing module named __builtin__ - imported by pkg_resources._vendor.pyparsing (conditional), setuptools._vendor.pyparsing (conditional)
missing module named ordereddict - imported by pkg_resources._vendor.pyparsing (optional), setuptools._vendor.pyparsing (optional)
missing module named 'com.sun' - imported by pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named com - imported by pkg_resources._vendor.appdirs (delayed)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named sip - imported by matplotlib.backends.qt_compat (delayed, conditional), PyQt5 (top-level)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named bs4.builder.HTMLParserTreeBuilder - imported by bs4.builder (top-level), bs4 (top-level)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (optional), lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level), lxml.html.html5parser (top-level)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named numpy.power - imported by numpy (top-level), scipy.stats.kde (top-level)
missing module named numpy.hypot - imported by numpy (top-level), scipy.stats.morestats (top-level)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.NINF - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats.morestats (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy.typing._array_like (top-level)
missing module named numpy.double - imported by numpy (top-level), scipy.optimize._nnls (top-level)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize.minpack (top-level)
missing module named numpy.float64 - imported by numpy (top-level), scipy.optimize.lbfgsb (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg.matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg.matfuncs (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg.matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg.decomp_schur (top-level), scipy.linalg.matfuncs (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg.decomp_svd (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg.decomp (top-level)
missing module named numpy.arccos - imported by numpy (top-level), scipy.special.orthogonal (top-level), scipy.linalg.decomp_svd (top-level)
missing module named numpy.inexact - imported by numpy (top-level), scipy.special._basic (top-level), scipy.linalg.decomp (top-level), scipy.optimize.minpack (top-level)
missing module named 'nose.plugins' - imported by numpy.testing._private.noseclasses (top-level), numpy.testing._private.nosetester (delayed)
missing module named 'nose.util' - imported by numpy.testing._private.noseclasses (top-level)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), scipy._lib._testutils (delayed, optional)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isinf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.random.randn - imported by numpy.random (top-level), scipy (top-level)
missing module named numpy.random.rand - imported by numpy.random (top-level), scipy (top-level)
missing module named numpy.core.roll - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.take - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.fastCopyAndTranspose - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.linalg.decomp (top-level), scipy.interpolate._pade (top-level), scipy.optimize.optimize (top-level), scipy.optimize.minpack (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), scipy.optimize.minpack (top-level), numpy.typing._array_like (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), scipy.interpolate.interpolate (top-level), scipy.linalg.decomp (top-level), scipy.linalg.decomp_schur (top-level), scipy.sparse.linalg.isolve.utils (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.interpolate.fitpack2 (top-level), scipy.stats.stats (top-level), scipy.optimize.lbfgsb (top-level), scipy.optimize.tnc (top-level), scipy.optimize.slsqp (top-level), scipy.integrate._ode (top-level), scipy.misc.common (top-level), scipy.stats.morestats (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), numpy.typing._array_like (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg.decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats.morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg.matfuncs (top-level), scipy.stats.morestats (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), pandas.compat.numpy.function (top-level), numpy.typing._array_like (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats.mstats_basic (top-level), scipy.stats.mstats_extras (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named pytest - imported by scipy._lib._testutils (delayed), matplotlib (delayed, optional), pandas._testing._io (delayed), pandas._testing (delayed), pandas.util._tester (delayed, optional)
missing module named PyQt6 - imported by matplotlib.backends.qt_compat (delayed, conditional), matplotlib.backends.backend_qtagg (delayed, conditional, optional)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
missing module named StringIO - imported by six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named shiboken6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named PySide6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named 'tornado.template' - imported by matplotlib.backends.backend_webagg (delayed)
missing module named 'tornado.websocket' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.ioloop' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.web' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named tornado - imported by matplotlib.backends.backend_webagg (optional), matplotlib.backends.backend_webagg_core (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named 'PySide6.QtGui' - imported by PIL.ImageQt (conditional, optional)
missing module named 'PyQt6.QtGui' - imported by PIL.ImageQt (conditional, optional)
missing module named cffi - imported by scipy._lib._ccallback (delayed, optional), PIL.Image (optional), PIL.PyAccess (optional)
missing module named defusedxml - imported by PIL.Image (optional), openpyxl.xml (delayed, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named IPython - imported by matplotlib.backend_bases (delayed), matplotlib.pyplot (delayed, conditional), pandas.io.formats.printing (delayed)
missing module named 'IPython.core' - imported by matplotlib.backend_bases (delayed), matplotlib.pyplot (delayed), pandas.io.formats.printing (delayed, conditional)
missing module named matplotlib.tri.Triangulation - imported by matplotlib.tri (top-level), matplotlib.tri.trifinder (top-level), matplotlib.tri.tritools (top-level), matplotlib.tri.triinterpolate (top-level)
missing module named matplotlib.axes.Axes - imported by matplotlib.axes (delayed), matplotlib.legend (delayed), matplotlib.projections.geo (top-level), matplotlib.projections.polar (top-level), mpl_toolkits.mplot3d.axes3d (top-level), matplotlib.figure (top-level), matplotlib.pyplot (top-level), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.core (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional), pandas.plotting._matplotlib.misc (conditional)
missing module named gi - imported by matplotlib.cbook (delayed, conditional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional)
missing module named _dummy_threading - imported by dummy_threading (optional)
missing module named pyarrow - imported by pandas.core.arrays.masked (delayed), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays._arrow_utils (top-level), pandas.core.arrays.interval (delayed), pandas.core.arrays.period (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.io.feather_format (delayed), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed, conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional, optional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named xlwt - imported by pandas.io.excel._xlwt (delayed, conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named tests - imported by openpyxl.reader.excel (optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named cStringIO - imported by xlrd.timemachine (conditional)
missing module named copy_reg - imported by cStringIO (top-level)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (delayed)
missing module named scipy.special.ndtr - imported by scipy.special (top-level), scipy.stats._bootstrap (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named scipy.special.entr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.chndtr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named scipy.special.rel_entr - imported by scipy.special (top-level), scipy.spatial.distance (top-level)
missing module named scipy.special.xlogy - imported by scipy.special (top-level), scipy.interpolate.rbf (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (delayed), scipy.sparse.data (delayed), scipy.optimize._numdiff (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.common (top-level), scipy.stats._crosstab (top-level), pandas.core.arrays.sparse.accessor (delayed)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg.matfuncs (delayed, conditional)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scikits - imported by scipy.sparse.linalg.dsolve.linsolve (optional)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg.isolve._gcrotmk (top-level)
missing module named scipy.special.gammaln - imported by scipy.special (top-level), scipy.special.spfun_stats (top-level), scipy.optimize._dual_annealing (top-level), scipy.integrate._quadrature (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._hypotests (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special.orthogonal (top-level)
missing module named scipy.special.comb - imported by scipy.special (delayed), scipy.linalg.special_matrices (delayed), scipy.interpolate.interpolate (top-level), scipy.interpolate._rbfinterp (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named scipy.special.poch - imported by scipy.special (top-level), scipy.fft._fftlog (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named _ufunc - imported by numpy.typing (conditional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.string_arrow (conditional)
missing module named hypothesis - imported by pandas.util._tester (delayed, optional)
missing module named pandas.ExtensionArray - imported by pandas (conditional), pandas.core.construction (conditional)
missing module named posix - imported by os (conditional, optional)
missing module named resource - imported by posix (top-level)
