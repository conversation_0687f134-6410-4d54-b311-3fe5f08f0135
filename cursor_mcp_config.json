{"mcpServers": {"Sequential Thinking": {"command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking", "--config", "\"{}\""]}, "files": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "C:/Users/<USER>/Downloads/AI写代码"]}, "excel": {"command": "npx", "args": ["@negokaz/excel-mcp-server"], "env": {"EXCEL_MCP_PAGING_CELLS_LIMIT": "4000"}}, "quickchart-server": {"command": "npx", "args": ["@gongrzhe/quickchart-mcp-server"]}, "hotnews": {"command": "npx", "args": ["@smithery/cli", "run", "@wopal/mcp-server-hotnews", "--config", "\"{}\""]}}}