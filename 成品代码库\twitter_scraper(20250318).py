import json
import requests
import datetime
import time
import socks
import socket
import re
import urllib3
import ssl
from urllib.parse import urlparse

# 禁用不安全连接警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class TwitterScraper:
    def __init__(self, proxy_settings=None):
        self.proxy_settings = proxy_settings
        self.authenticated = False
        
        # 授权相关的信息
        self.authorization = ""
        self.csrf_token = ""
        self.cookies = ""
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36"
        
        # 请求设置
        self.timeout = 30  # 超时设置（秒）
        self.verify_ssl = False  # SSL验证设置，默认关闭
        self.max_retries = 3  # 最大重试次数
        
        # API URL - 更新为最新的GraphQL端点哈希值
        self.api_base_url = "https://twitter.com/i/api/graphql"
        # 旧的端点: self.user_by_screen_name_url = f"{self.api_base_url}/dZ6ceqYkvsY8xJIkRqHK5Q/UserByScreenName"
        # 最新的端点哈希值 (2024年3月更新)
        self.user_by_screen_name_url = f"{self.api_base_url}/qRednkZG-rn1P6b48NINmQ/UserByScreenName"
        # 旧的推文获取端点: self.user_tweets_url = f"{self.api_base_url}/VcZPNqP7UKkwQs_n9QZtJw/UserTweets"
        # 更新的推文获取端点: self.user_tweets_url = f"{self.api_base_url}/BoHLKeBvibdYDiJON1oqTg/UserTweets"
        # 最新的推文获取端点哈希值 (2024年3月更新)
        self.user_tweets_url = f"{self.api_base_url}/H8OOoI-5ZE4NxgRr8lfyWg/UserTweets"
        
    def setup_from_config(self, config):
        """从配置加载授权信息"""
        # 从配置文件获取authorization并自动处理%字符问题
        try:
            self.authorization = config.get("twitter_web", "authorization", fallback="").replace("%%", "%")
            self.csrf_token = config.get("twitter_web", "csrf_token", fallback="")
            self.cookies = config.get("twitter_web", "cookies", fallback="")
        except Exception as e:
            print(f"加载Twitter Web配置时出错: {str(e)}")
            # 设置默认值
            self.authorization = ""
            self.csrf_token = ""
            self.cookies = ""
        
    def save_to_config(self, config):
        """保存授权信息到配置"""
        if not config.has_section("twitter_web"):
            config.add_section("twitter_web")
        # 保存时替换%为%%
        config.set("twitter_web", "authorization", self.authorization.replace("%", "%%"))
        config.set("twitter_web", "csrf_token", self.csrf_token)
        config.set("twitter_web", "cookies", self.cookies)
        
    def authenticate(self):
        """检查授权信息是否有效"""
        if not all([self.authorization, self.csrf_token, self.cookies]):
            print("认证失败: 缺少必要的授权信息 (authorization, csrf_token 或 cookies)")
            self.authenticated = False
            return False
        
        try:
            # 打印授权信息的长度，用于调试
            print(f"调试信息 - 授权信息长度检查:")
            print(f"Authorization长度: {len(self.authorization)}")
            print(f"CSRF Token长度: {len(self.csrf_token)}")
            print(f"Cookies长度: {len(self.cookies)}")
            
            # 应用代理设置
            if self.proxy_settings and self.proxy_settings.enabled:
                print(f"正在应用代理设置: {self.proxy_settings.proxy_type}://{self.proxy_settings.host}:{self.proxy_settings.port}")
                self.proxy_settings.apply_proxy()
            else:
                print("未使用代理或代理未启用")
            
            # 尝试获取用户信息验证授权 - 使用多个测试账号，以防某个账号不可用
            test_users = ["elonmusk", "Twitter", "jack"]  # 添加多个备选测试账号
            
            for test_user in test_users:
                print(f"尝试验证授权 - 使用测试账号: @{test_user}")
                user_info = self._get_user_info(test_user)
                
                # 检查HTTP请求是否成功
                if user_info:
                    print(f"成功获取到用户信息响应")
                    
                    # 详细检查响应内容结构
                    if "data" in user_info:
                        print("响应中包含data字段")
                        if "user" in user_info["data"]:
                            print("响应中包含user字段")
                            if user_info["data"]["user"]:
                                print("授权验证成功!")
                                self.authenticated = True
                                return True
                            else:
                                print("user字段为空")
                        else:
                            print(f"响应中缺少user字段。响应结构: {list(user_info['data'].keys())}")
                    else:
                        print(f"响应中缺少data字段。响应结构: {list(user_info.keys())}")
                        
                        # 输出完整响应用于调试
                        print(f"完整响应内容: {json.dumps(user_info, ensure_ascii=False, indent=2)[:500]}... (截断)")
                
                # 如果此测试账号验证成功，就不再尝试其他账号
                if self.authenticated:
                    break
            
            # 如果所有测试账号都验证失败    
            if not self.authenticated:
                print("所有测试账号验证都失败了")
                return False
                
        except Exception as e:
            print(f"Twitter授权验证失败，发生异常: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印完整的异常堆栈信息
            self.authenticated = False
            return False
            
    def _get_user_info(self, username):
        """获取用户信息"""
        headers = self._get_headers()
        
        # 更新为2024年最新Twitter GraphQL API所需的参数格式
        params = {
            "variables": json.dumps({
                "screen_name": username,
                "withSafetyModeUserFields": True,
                "withSuperFollowsUserFields": True,
                "withNftAvatar": True,
                "withBirdwatchPivots": True,
                "highlights_tweets_tab_ui_enabled": True,
                "creator_subscriptions_tweet_preview_api_enabled": True,
                "hidden_profile_likes_enabled": True,
                "subscriptions_verification_info_verified_since_enabled": True
            }),
            "features": json.dumps({
                "blue_business_profile_image_shape_enabled": True,
                "responsive_web_graphql_exclude_directive_enabled": True,
                "verified_phone_label_enabled": False,
                "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                "responsive_web_graphql_timeline_navigation_enabled": True,
                "view_counts_everywhere_api_enabled": True,
                "longform_notetweets_consumption_enabled": True,
                "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": False,
                "tweetypie_unmention_optimization_enabled": True,
                "responsive_web_edit_tweet_api_enabled": True,
                "graphql_is_translatable_rweb_tweet_is_translatable_enabled": True,
                "freedom_of_speech_not_reach_fetch_enabled": True,
                "standardized_nudges_misinfo": True,
                "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
                "longform_notetweets_rich_text_read_enabled": True,
                "longform_notetweets_inline_media_enabled": True,
                "responsive_web_enhance_cards_enabled": False,
                "highlights_tweets_tab_ui_enabled": True,
                "creator_subscriptions_tweet_preview_api_enabled": True,
                "hidden_profile_likes_enabled": True,
                "subscriptions_verification_info_verified_since_enabled": True
            }),
            "fieldToggles": json.dumps({
                "withAuxiliaryUserLabels": False,
                "withArticleRichContentState": False
            })
        }
        
        try:
            print(f"正在请求用户信息: {username}")
            print(f"请求URL: {self.user_by_screen_name_url}")
            
            # 标记是否要尝试直接连接（无代理）
            try_direct = False
            
            # 使用会话对象管理连接
            session = requests.Session()
            
            # 为会话配置代理
            if self.proxy_settings and self.proxy_settings.enabled:
                # 使用requests库的代理配置方式，而不是依赖socks库全局修改
                proxy_dict = {}
                
                # 构建代理URL
                auth_part = ""
                if self.proxy_settings.username and self.proxy_settings.password:
                    auth_part = f"{self.proxy_settings.username}:{self.proxy_settings.password}@"
                
                # 根据代理类型设置不同的代理URL格式
                if self.proxy_settings.proxy_type == "SOCKS5":
                    # 尝试多种代理配置方式，处理SSL问题
                    try_direct = False  # 是否尝试直接连接（无代理）
                    
                    # 第一种方式：尝试使用HTTP隧道代替SOCKS5直接转发HTTPS连接
                    proxy_dict = {
                        "http": f"socks5://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}",
                        "https": f"http://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}"
                    }
                    print("方式1: 使用HTTP隧道发送HTTPS请求，避免SSL版本不匹配问题")
                    
                    # 如果代理设置为127.0.0.1，可能是本地代理，也可以尝试直接连接
                    if self.proxy_settings.host == "127.0.0.1" or self.proxy_settings.host == "localhost":
                        try_direct = True
                    
                elif self.proxy_settings.proxy_type == "SOCKS4":
                    proxy_dict = {
                        "http": f"socks4://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}",
                        "https": f"http://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}"
                    }
                    print("使用HTTP隧道发送HTTPS请求，避免SSL版本不匹配问题")
                else:  # HTTP/HTTPS代理
                    proxy_dict = {
                        "http": f"http://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}",
                        "https": f"http://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}"
                    }
                
                print(f"使用代理: {proxy_dict}")
                session.proxies = proxy_dict
                
                # 如果是本地代理且标记为尝试直接连接，则添加一次直接连接的尝试
                if try_direct:
                    print("检测到本地代理，将同时尝试直接连接方式作为备选")
                
                # 如果是HTTPS代理，确保SSL正确配置
                if not self.proxy_settings.verify_ssl:
                    # 禁用SSL验证警告
                    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                    
                    # 在requests级别禁用SSL验证
                    session.verify = False
                    
                    # 修改全局SSL设置 - 禁用主机名检查和证书验证
                    ssl._create_default_https_context = ssl._create_unverified_context
                    
                    # 使用标准的重试适配器
                    adapter = requests.adapters.HTTPAdapter(
                        pool_connections=1,
                        pool_maxsize=1,
                        max_retries=self.max_retries,
                        pool_block=False
                    )
                    session.mount('https://', adapter)
            
            # 配置重试策略
            retry = urllib3.util.Retry(
                total=self.max_retries,
                backoff_factor=0.5,
                status_forcelist=[500, 502, 503, 504]
            )
            adapter = requests.adapters.HTTPAdapter(max_retries=retry)
            session.mount("http://", adapter)
            # HTTPS适配器在启用代理情况下已配置，此处只在未配置代理时配置
            if not (self.proxy_settings and self.proxy_settings.enabled):
                session.mount("https://", adapter)
            
            # 发送请求 - 先使用配置的代理方式
            proxy_result = None
            direct_result = None
            
            # 1. 尝试使用代理
            if self.proxy_settings and self.proxy_settings.enabled:
                for attempt in range(self.max_retries):
                    try:
                        # 从URL中提取主机名，用于设置server_hostname
                        url_parts = urlparse(self.user_by_screen_name_url)
                        hostname = url_parts.netloc
                        
                        print(f"使用代理方式，尝试 #{attempt+1}")
                        response = session.get(
                            self.user_by_screen_name_url,
                            headers=headers,
                            params=params,
                            timeout=self.timeout,
                            verify=self.verify_ssl  # 控制SSL验证
                        )
                        
                        print(f"响应状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            print("请求成功，正在解析JSON响应")
                            return response.json()
                        else:
                            print(f"获取用户信息失败，HTTP状态码: {response.status_code}")
                            print(f"错误响应: {response.text[:500]}... (截断)")
                            # 保存结果，如果直接连接也失败，可以返回这个
                            proxy_result = response
                            
                            # 检查是否有特定的错误代码
                            if response.status_code == 401:
                                print("401错误: 授权验证失败，请检查authorization是否有效")
                                # 401表示认证问题，无论用什么连接方式都会失败，直接返回
                                if not try_direct:
                                    return None
                            elif response.status_code == 403:
                                print("403错误: 禁止访问，可能是请求频率限制或csrf_token无效")
                                # 如果没有被标记为尝试直连，403可能是代理问题，返回
                                if not try_direct:
                                    break
                            elif response.status_code == 404:
                                print("404错误: API端点不存在，GraphQL哈希可能已过期")
                                # 如果是404，说明API端点问题，直连也会失败，直接返回
                                if not try_direct:
                                    return None
                            
                            # 如果状态码为401或403，不再重试
                            if response.status_code in [401, 403]:
                                break
                            
                            # 其他状态码则等待后重试
                            if attempt < self.max_retries - 1:
                                wait_time = 2 ** attempt  # 指数退避
                                print(f"等待{wait_time}秒后重试...")
                                time.sleep(wait_time)
                        
                    except (requests.exceptions.SSLError, ssl.SSLError) as e:
                        print(f"SSL错误: {str(e)}")
                        print("尝试关闭SSL验证...")
                        self.verify_ssl = False
                        if attempt < self.max_retries - 1:
                            time.sleep(1)
                        
                    except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                        print(f"连接错误或超时: {str(e)}")
                        if attempt < self.max_retries - 1:
                            wait_time = 2 ** attempt
                            print(f"等待{wait_time}秒后重试...")
                            time.sleep(wait_time)
                        
                    except Exception as e:
                        print(f"请求过程中发生未知错误: {str(e)}")
                        import traceback
                        traceback.print_exc()
                        if attempt < self.max_retries - 1:
                            time.sleep(2)
            
            # 2. 如果代理方式失败且已标记尝试直连，则进行直连尝试
            if try_direct:
                print("\n正在尝试直接连接方式（不使用代理）...")
                # 创建新的会话对象，避免之前的代理设置影响
                direct_session = requests.Session()
                # 重置代理设置
                direct_session.proxies = {}
                
                # 配置重试策略
                retry = urllib3.util.Retry(
                    total=self.max_retries,
                    backoff_factor=0.5,
                    status_forcelist=[500, 502, 503, 504]
                )
                adapter = requests.adapters.HTTPAdapter(max_retries=retry)
                direct_session.mount("http://", adapter)
                direct_session.mount("https://", adapter)
                
                for attempt in range(self.max_retries):
                    try:
                        print(f"直接连接方式，尝试 #{attempt+1}")
                        response = direct_session.get(
                            self.user_by_screen_name_url,
                            headers=headers,
                            params=params,
                            timeout=self.timeout,
                            verify=True  # 直连时启用SSL验证
                        )
                        
                        print(f"响应状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            print("直接连接请求成功，正在解析JSON响应")
                            return response.json()
                        else:
                            print(f"直接连接获取用户信息失败，HTTP状态码: {response.status_code}")
                            print(f"错误响应: {response.text[:500]}... (截断)")
                            direct_result = response
                            
                            # 检查是否有特定的错误代码
                            if response.status_code == 401:
                                print("401错误: 授权验证失败，请检查authorization是否有效")
                                break
                            elif response.status_code == 403:
                                print("403错误: 禁止访问，可能是请求频率限制或csrf_token无效")
                                break
                            elif response.status_code == 404:
                                print("404错误: API端点不存在，GraphQL哈希可能已过期")
                                break
                            
                            # 如果状态码为401或403，不再重试
                            if response.status_code in [401, 403]:
                                break
                            
                            # 其他状态码则等待后重试
                            if attempt < self.max_retries - 1:
                                wait_time = 2 ** attempt  # 指数退避
                                print(f"等待{wait_time}秒后重试...")
                                time.sleep(wait_time)
                        
                    except Exception as e:
                        print(f"直接连接请求过程中发生错误: {str(e)}")
                        if attempt < self.max_retries - 1:
                            wait_time = 2 ** attempt
                            print(f"等待{wait_time}秒后重试...")
                            time.sleep(wait_time)
            
            # 如果有代理结果，返回它；否则返回直连结果；如果都没有，返回None
            if proxy_result and proxy_result.status_code != 403:
                # 如果代理结果不是403，优先返回代理结果
                return None
            elif direct_result:
                # 代理失败但有直连结果，返回直连结果
                return None
            else:
                # 两种方式都失败
                print(f"在尝试所有连接方式后仍无法获取用户信息")
                return None
                
        except Exception as e:
            print(f"获取用户信息请求异常: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印完整的异常堆栈信息
            return None
    
    def _get_headers(self):
        """获取请求头"""
        # 检查Authorization格式
        auth = self.authorization
        if auth and not auth.startswith("Bearer "):
            print("警告: Authorization不是以'Bearer '开头的，正在自动添加前缀")
            auth = "Bearer " + auth
        
        # 检查csrf_token
        if self.csrf_token:
            print(f"CSRF令牌前10个字符: {self.csrf_token[:10]}...")
        
        # 打印cookies中的关键字段
        if self.cookies:
            cookie_parts = self.cookies.split(";")
            print(f"Cookie包含{len(cookie_parts)}个部分")
            important_cookies = ["auth_token", "ct0", "twid"]
            found_important = []
            
            for cookie in important_cookies:
                if any(c.strip().startswith(f"{cookie}=") for c in cookie_parts):
                    found_important.append(cookie)
            
            print(f"找到重要的Cookie: {', '.join(found_important) or '未找到'}")
            
            # 从cookie中提取ct0值，确保与csrf_token匹配
            ct0_value = ""
            for part in cookie_parts:
                if part.strip().startswith("ct0="):
                    ct0_value = part.strip()[4:]
                    break
            
            # 如果从cookie中找到ct0值，确保与csrf_token匹配
            if ct0_value and ct0_value != self.csrf_token:
                print(f"警告: cookie中的ct0值({ct0_value[:5]}...)与csrf_token({self.csrf_token[:5]}...)不匹配，将使用cookie中的值")
                self.csrf_token = ct0_value
        
        # 构建并返回请求头
        headers = {
            "authorization": auth,
            "x-csrf-token": self.csrf_token,
            "cookie": self.cookies,
            "user-agent": self.user_agent,
            "content-type": "application/json",
            "x-twitter-active-user": "yes",
            "x-twitter-client-language": "en",
            # 添加以下头部以增强CSRF验证成功率
            "x-twitter-auth-type": "OAuth2Session",
            "sec-ch-ua": '"Chromium";v="112", "Google Chrome";v="112", "Not:A-Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "referrer": "https://twitter.com/",
            "referrerPolicy": "strict-origin-when-cross-origin"
        }
        
        print("已生成请求头")
        return headers
        
    def get_user_tweets(self, username, count=10, since_date=None):
        """获取用户的推文"""
        if not self.authenticated:
            return []
            
        try:
            # 获取用户ID
            user_info = self._get_user_info(username)
            if not user_info or "data" not in user_info or "user" not in user_info["data"] or not user_info["data"]["user"]:
                print(f"无法获取用户信息: {username}")
                return []
                
            user_id = user_info["data"]["user"]["result"]["rest_id"]
            print(f"成功获取用户ID: {user_id}")
            
            headers = self._get_headers()
            
            # 创建请求会话
            session = requests.Session()
            
            # 为会话配置代理
            if self.proxy_settings and self.proxy_settings.enabled:
                # 使用requests库的代理配置方式，而不是依赖socks库全局修改
                proxy_dict = {}
                
                # 构建代理URL
                auth_part = ""
                if self.proxy_settings.username and self.proxy_settings.password:
                    auth_part = f"{self.proxy_settings.username}:{self.proxy_settings.password}@"
                
                # 根据代理类型设置不同的代理URL格式
                if self.proxy_settings.proxy_type == "SOCKS5":
                    # 尝试多种代理配置方式，处理SSL问题
                    try_direct = False  # 是否尝试直接连接（无代理）
                    
                    # 第一种方式：尝试使用HTTP隧道代替SOCKS5直接转发HTTPS连接
                    proxy_dict = {
                        "http": f"socks5://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}",
                        "https": f"http://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}"
                    }
                    print("方式1: 使用HTTP隧道发送HTTPS请求，避免SSL版本不匹配问题")
                    
                    # 如果代理设置为127.0.0.1，可能是本地代理，也可以尝试直接连接
                    if self.proxy_settings.host == "127.0.0.1" or self.proxy_settings.host == "localhost":
                        try_direct = True
                    
                elif self.proxy_settings.proxy_type == "SOCKS4":
                    proxy_dict = {
                        "http": f"socks4://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}",
                        "https": f"http://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}"
                    }
                    print("使用HTTP隧道发送HTTPS请求，避免SSL版本不匹配问题")
                else:  # HTTP/HTTPS代理
                    proxy_dict = {
                        "http": f"http://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}",
                        "https": f"http://{auth_part}{self.proxy_settings.host}:{self.proxy_settings.port}"
                    }
                
                print(f"使用代理: {proxy_dict}")
                session.proxies = proxy_dict
                
                # 如果是本地代理且标记为尝试直接连接，则添加一次直接连接的尝试
                if try_direct:
                    print("检测到本地代理，将同时尝试直接连接方式作为备选")
                
                # 如果是HTTPS代理，确保SSL正确配置
                if not self.proxy_settings.verify_ssl:
                    # 禁用SSL验证警告
                    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                    
                    # 在requests级别禁用SSL验证
                    session.verify = False
                    
                    # 修改全局SSL设置 - 禁用主机名检查和证书验证
                    ssl._create_default_https_context = ssl._create_unverified_context
                    
                    # 使用标准的重试适配器
                    adapter = requests.adapters.HTTPAdapter(
                        pool_connections=1,
                        pool_maxsize=1,
                        max_retries=self.max_retries,
                        pool_block=False
                    )
                    session.mount('https://', adapter)
            
            # 配置重试策略
            retry = urllib3.util.Retry(
                total=self.max_retries,
                backoff_factor=0.5,
                status_forcelist=[500, 502, 503, 504]
            )
            adapter = requests.adapters.HTTPAdapter(max_retries=retry)
            session.mount("http://", adapter)
            # HTTPS适配器在启用代理情况下已配置，此处只在未配置代理时配置
            if not (self.proxy_settings and self.proxy_settings.enabled):
                session.mount("https://", adapter)
            
            # 更新为最新的API参数格式
            variables = {
                "userId": user_id,
                "count": min(count, 50),  # 每次最多获取50条
                "includePromotedContent": False,
                "withQuickPromoteEligibilityTweetFields": False,
                "withVoice": True,
                "withV2Timeline": True,
                "withSuperFollowsUserFields": True,
                "withBirdwatchPivots": True,
                "withDownvotePerspective": False,
                "withReactionsMetadata": False,
                "withReactionsPerspective": False,
                "withSuperFollowsTweetFields": True,
                "withNftAvatar": True
            }
            
            features = {
                "responsive_web_graphql_exclude_directive_enabled": True,
                "verified_phone_label_enabled": False,
                "creator_subscriptions_tweet_preview_api_enabled": True,
                "responsive_web_graphql_timeline_navigation_enabled": True,
                "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                "c9s_tweet_anatomy_moderator_badge_enabled": True,
                "tweetypie_unmention_optimization_enabled": True,
                "responsive_web_edit_tweet_api_enabled": True,
                "graphql_is_translatable_rweb_tweet_is_translatable_enabled": True,
                "view_counts_everywhere_api_enabled": True,
                "longform_notetweets_consumption_enabled": True,
                "responsive_web_twitter_article_tweet_consumption_enabled": True,
                "tweet_awards_web_tipping_enabled": False,
                "freedom_of_speech_not_reach_fetch_enabled": True,
                "standardized_nudges_misinfo": True,
                "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
                "rweb_video_timestamps_enabled": True,
                "longform_notetweets_rich_text_read_enabled": True,
                "longform_notetweets_inline_media_enabled": True,
                "responsive_web_media_download_video_enabled": False,
                "responsive_web_enhance_cards_enabled": False,
                "vibe_api_enabled": True,
                "responsive_web_twitter_blue_verified_badge_is_enabled": True,
                "highlights_tweets_tab_ui_enabled": True,
                "creator_subscriptions_tweet_preview_api_enabled": True,
                "rweb_lists_timeline_redesign_enabled": True,
                "responsive_web_text_conversations_enabled": True,
                "unified_cards_ad_metadata_container_dynamic_card_content_query_enabled": True,
                "interactive_text_enabled": True,
                "responsive_web_uc_gql_enabled": True,
                "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
                "longform_notetweets_rich_text_read_enabled": True,
                "longform_notetweets_inline_media_enabled": True,
                "responsive_web_enhance_cards_enabled": False
            }
            
            # 添加新的fieldToggles参数
            field_toggles = {
                "withArticleRichContentState": False,
                "withAuxiliaryUserLabels": False
            }
            
            all_tweets = []
            cursor = None
            remaining_count = count
            
            while remaining_count > 0:
                # 更新游标参数
                if cursor:
                    variables["cursor"] = cursor
                
                params = {
                    "variables": json.dumps(variables),
                    "features": json.dumps(features),
                    "fieldToggles": json.dumps(field_toggles)
                }
                
                print(f"发送请求到: {self.user_tweets_url}")
                
                # 重试机制
                for attempt in range(self.max_retries):
                    try:
                        response = session.get(
                            self.user_tweets_url,
                            headers=headers,
                            params=params,
                            timeout=self.timeout,
                            verify=self.verify_ssl
                        )
                        
                        print(f"响应状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            break
                        
                        print(f"获取推文失败: {response.status_code}")
                        print(f"错误响应: {response.text[:300]}... (截断)")
                        
                        if response.status_code in [401, 403]:
                            # 认证错误，不再重试
                            return all_tweets
                        
                        if attempt < self.max_retries - 1:
                            wait_time = 2 ** attempt
                            print(f"等待{wait_time}秒后重试...")
                            time.sleep(wait_time)
                        
                    except Exception as e:
                        print(f"请求推文时出错: {str(e)}")
                        if attempt < self.max_retries - 1:
                            time.sleep(2)
                        else:
                            return all_tweets
                
                if response.status_code != 200:
                    break
                
                data = response.json()
                
                # 提取推文
                tweets_data = self._extract_tweets_from_response(data, since_date)
                all_tweets.extend(tweets_data)
                
                # 获取下一页游标
                cursor = self._get_next_cursor(data)
                if not cursor or len(tweets_data) == 0 or len(all_tweets) >= count:
                    break
                    
                remaining_count = count - len(all_tweets)
                
            return all_tweets[:count]  # 确保只返回要求数量的推文
                
        except Exception as e:
            print(f"获取推文失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
    
    def _extract_tweets_from_response(self, data, since_date=None):
        """从响应中提取推文数据"""
        tweets = []
        
        try:
            if not data or "data" not in data:
                return tweets

            # 确保since_date是datetime.date类型
            if since_date:
                if isinstance(since_date, str):
                    since_date = datetime.datetime.strptime(since_date, "%Y-%m-%d").date()
                elif isinstance(since_date, datetime.datetime):
                    since_date = since_date.date()
                print(f"Debug - since_date类型: {type(since_date)}, 值: {since_date}")
                
            timeline = data["data"]["user"]["result"]["timeline_v2"]["timeline"]["instructions"]
            
            # 查找包含推文条目的指令
            entries = []
            for instruction in timeline:
                if instruction["type"] == "TimelineAddEntries":
                    entries = instruction["entries"]
                    break
            
            for entry in entries:
                entry_id = entry["entryId"]
                
                # 跳过非推文条目
                if not entry_id.startswith("tweet-"):
                    continue
                    
                content = entry.get("content", {})
                if "itemContent" not in content:
                    continue
                    
                tweet_item = content["itemContent"].get("tweet_results", {}).get("result", {})
                if not tweet_item or tweet_item.get("__typename") != "Tweet":
                    continue
                    
                # 提取推文数据
                tweet_id = tweet_item.get("rest_id", "")
                legacy = tweet_item.get("legacy", {})
                created_at_str = legacy.get("created_at", "")
                
                if not created_at_str:
                    continue
                    
                # 解析创建时间
                created_at = datetime.datetime.strptime(created_at_str, "%a %b %d %H:%M:%S %z %Y")
                
                # 日期类型比较前的调试信息
                if since_date:
                    print(f"Debug - created_at类型: {type(created_at.date())}, 值: {created_at.date()}")
                
                # 过滤日期 - 确保类型匹配
                if since_date and created_at.date() < since_date:
                    continue
                    
                # 获取用户信息
                user_data = tweet_item.get("core", {}).get("user_results", {}).get("result", {})
                user_legacy = user_data.get("legacy", {})
                
                # 创建推文对象
                tweet_data = {
                    "id": tweet_id,
                    "text": legacy.get("full_text", ""),
                    "created_at": created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "user": {
                        "name": user_legacy.get("name", ""),
                        "screen_name": user_legacy.get("screen_name", ""),
                        "profile_image_url": user_legacy.get("profile_image_url_https", "")
                    },
                    "media": [],
                    "urls": []
                }
                
                # 提取媒体
                extended_entities = legacy.get("extended_entities", {})
                if "media" in extended_entities:
                    for media in extended_entities["media"]:
                        media_data = {
                            "type": media.get("type", "photo"),
                            "url": media.get("media_url_https", ""),
                            "display_url": media.get("display_url", "")
                        }
                        
                        if media["type"] == "video":
                            # 获取视频URL
                            variants = media.get("video_info", {}).get("variants", [])
                            variants = [v for v in variants if "bitrate" in v]
                            if variants:
                                best_variant = max(variants, key=lambda x: x["bitrate"])
                                media_data["video_url"] = best_variant["url"]
                                
                        tweet_data["media"].append(media_data)
                
                # 提取URL
                entities = legacy.get("entities", {})
                if "urls" in entities:
                    for url_entity in entities["urls"]:
                        tweet_data["urls"].append({
                            "url": url_entity.get("url", ""),
                            "expanded_url": url_entity.get("expanded_url", ""),
                            "display_url": url_entity.get("display_url", "")
                        })
                
                tweets.append(tweet_data)
                
        except Exception as e:
            print(f"处理推文数据时出错: {str(e)}")
            import traceback
            traceback.print_exc()  # 添加详细的堆栈跟踪
            
        return tweets
    
    def _get_next_cursor(self, data):
        """从响应中提取下一页游标"""
        try:
            timeline = data["data"]["user"]["result"]["timeline_v2"]["timeline"]["instructions"]
            
            # 查找替换条目指令，其中包含游标
            for instruction in timeline:
                if instruction["type"] == "TimelineAddToModule":
                    continue
                    
                entries = []
                if instruction["type"] == "TimelineReplaceEntry":
                    entries = [instruction["entry"]]
                elif instruction["type"] == "TimelineAddEntries":
                    entries = instruction["entries"]
                    
                for entry in entries:
                    if entry["entryId"].startswith("cursor-bottom-"):
                        content = entry.get("content", {})
                        if content.get("entryType") == "TimelineTimelineCursor" and "value" in content:
                            return content["value"]
            
            return None
                
        except Exception as e:
            print(f"获取下一页游标时出错: {str(e)}")
            return None 