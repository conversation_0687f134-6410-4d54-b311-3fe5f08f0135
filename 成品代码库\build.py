import os
import sys
import PyInstaller.__main__
import shutil

def build_exe():
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 图标文件路径
    icon_path = os.path.join(current_dir, '5.ico')
    manifest_path = os.path.join(current_dir, 'app.manifest')
    
    # 检查必要文件是否存在
    if not os.path.exists(icon_path):
        print("错误: 未找到图标文件 5.ico")
        sys.exit(1)
    if not os.path.exists(manifest_path):
        print("错误: 未找到manifest文件 app.manifest")
        sys.exit(1)

    # PyInstaller参数
    params = [
        '微情报搜情助手.py',  # 主程序文件
        '--onefile',  # 打包成单个文件
        '--noconsole',  # 不显示控制台窗口
        f'--icon={icon_path}',  # 设置图标
        '--name=微情报搜情助手',  # 输出文件名
        '--clean',  # 清理临时文件
        f'--manifest={manifest_path}',  # 添加manifest文件
        '--add-data=5.ico;.',  # 添加图标文件到打包中
        '--version-file=version.txt',  # 添加版本信息
        '--hidden-import=PyQt5',
        '--hidden-import=PyQt5.QtCore',
        '--hidden-import=PyQt5.QtGui',
        '--hidden-import=PyQt5.QtWidgets',
        '--hidden-import=requests',
        '--hidden-import=bs4',
        '--hidden-import=jieba',
        '--hidden-import=openai',
        '--hidden-import=urllib3',
        '--hidden-import=json',
        '--hidden-import=datetime',
        '--hidden-import=hashlib',
        '--hidden-import=hmac',
        '--hidden-import=base64',
        '--hidden-import=re',
        '--hidden-import=time',
    ]

    # 创建版本信息文件
    version_info = '''
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404b0',
          [StringStruct(u'CompanyName', u'台州市公安局'),
          StringStruct(u'FileDescription', u'微情报搜情助手'),
          StringStruct(u'FileVersion', u'1.0.0'),
          StringStruct(u'InternalName', u'微情报搜情助手'),
          StringStruct(u'LegalCopyright', u'Copyright (C) 2024 台州市公安局 解晟'),
          StringStruct(u'OriginalFilename', u'微情报搜情助手.exe'),
          StringStruct(u'ProductName', u'微情报搜情助手'),
          StringStruct(u'ProductVersion', u'1.0.0')])
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
    
    # 写入版本信息文件
    with open('version.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)

    # 设置环境变量以支持Windows 7
    os.environ['PYTHONPATH'] = os.path.dirname(os.path.abspath(__file__))
    
    # 运行PyInstaller
    PyInstaller.__main__.run(params)
    
    print("打包完成！")
    
    # 清理临时文件和目录
    files_to_remove = ['version.txt']
    dirs_to_remove = ['build', '__pycache__']
    
    # 删除临时文件
    for file_name in files_to_remove:
        file_path = os.path.join(current_dir, file_name)
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"已删除文件: {file_name}")
            except Exception as e:
                print(f"删除文件 {file_name} 时出错: {str(e)}")
    
    # 删除临时目录
    for dir_name in dirs_to_remove:
        dir_path = os.path.join(current_dir, dir_name)
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"已清理目录: {dir_name}")
            except Exception as e:
                print(f"清理目录 {dir_name} 时出错: {str(e)}")

if __name__ == '__main__':
    build_exe() 