{"name": "大型文件理解（思考模式）", "nodes": [{"parameters": {"content": "## Workflow Overview / 工作流程概述\n\n*   **Purpose / 目的:** This workflow demonstrates how to upload a document to the Google Generative Language API (Gemini) and use the 'generateContent' endpoint to analyze it (e.g., summarize) based on user-provided parameters (model, thinking budget).\n    這個工作流程展示了如何將文件上傳到 Google Generative Language API (Gemini)，並使用 'generateContent' 端點根據使用者提供的參數（模型、思考預算）來分析文件（例如，摘要）。\n\n*   **Steps / 步驟:**\n    1.  **Input Collection / 輸入收集:** Get file and analysis parameters from a form trigger.\n        從表單觸發器獲取文件和分析參數。\n    2.  **Data Preparation / 資料準備:** Extract file metadata and combine with user inputs.\n        提取文件後設資料並與使用者輸入結合。\n    3.  **File Upload (Resumable) / 文件上傳 (可續傳):** Perform a resumable upload to the Gemini API file storage.\n        對 Gemini API 文件儲存執行可續傳上傳。\n    4.  **AI Analysis / AI 分析:** Call the Gemini 'generateContent' endpoint with the uploaded file URI and parameters.\n        使用已上傳的文件 URI 和參數呼叫 Gemini 的 'generateContent' 端點。\n\n## Gemini thinking Budget\nSet budget on thinking models\n\nThe thinkingBudget parameter gives the model guidance on the number of thinking tokens it can use when generating a response. A greater number of tokens is typically associated with more detailed thinking, which is needed for solving more complex tasks. thinkingBudget must be an integer in the range 0 to 24576. Setting the thinking budget to 0 disables thinking.\n\nDepending on the prompt, the model might overflow or underflow the token budget.\n\n繁體中文:\n## Gemini 思考預算\n設定模型的思考預算\n\n`thinkingBudget` 參數為模型提供指導，告知其在生成回應時可以使用多少思考權杖（token）。較多的權杖通常與更詳細的思考相關，這對於解決更複雜的任務是必需的。`thinkingBudget` 必須是 0 到 24576 之間的整數。將思考預算設為 0 會禁用思考功能。\n\n根據提示的不同，模型可能會超出或未達到權杖預算。", "height": 1420, "width": 600}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "e5772abf-44bc-40c3-b4a9-d568778f740e", "name": "Sticky Note (Overview)1"}, {"parameters": {"content": "# large file Understanding with thinking - audio, documents, video\n", "height": 80, "width": 3260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 0], "id": "e0dda680-5f95-475d-b60d-81d8e2baa36f", "name": "Sticky Note (Form Trigger)8"}, {"parameters": {"content": "## Receive Input & Prepare Data\n\n*   **Nodes:** `On form submission`, `Edit Fields`\n*   **Purpose:**\n    1.  `On form submission`: The workflow starts when a user submits the linked form, providing a file, a selected AI model, and a thinking budget value.\n    2.  `Edit Fields`: This node processes the form input. It extracts metadata from the uploaded file (like size, MIME type, filename) and combines it with the selected model and thinking budget. It renames fields for clarity (e.g., `NUM_BYTES`, `MIME_TYPE`, `DISPLAY_NAME`).\n*   **Configuration:**\n    *   `On form submission`: Configured with form fields for 'data' (file), 'model' (dropdown), and 'thinkingBudget' (number).\n    *   `Edit Fields`: Uses expressions like `={{ $binary.data.fileSize }}` to get binary data info and `={{ $json.model }}` to get JSON data. Renames relevant fields.\n\n---\n\n## 接收輸入並準備資料\n\n*   **節點：** `On form submission` (表單提交時觸發)、`Edit Fields` (編輯欄位)\n*   **目的：**\n    1.  `On form submission`: 工作流程在使用者提交連結的表單時啟動，接收一個檔案、選擇的 AI 模型以及思考預算值。\n    2.  `Edit Fields`: 此節點處理表單輸入。它從上傳的檔案中提取後設資料（例如大小、MIME 類型、檔案名稱），並與選擇的模型和思考預算結合。它會重新命名欄位以提高清晰度（例如：`NUM_BYTES`、`MIME_TYPE`、`DISPLAY_NAME`）。\n*   **設定：**\n    *   `On form submission`: 配置有「data」（檔案）、「model」（下拉選單）和「thinkingBudget」（數字）等表單欄位。\n    *   `Edit Fields`: 使用 `={{ $binary.data.fileSize }}` 等表達式獲取二進位資料資訊，並使用 `={{ $json.model }}` 獲取 JSON 資料。重新命名相關欄位。", "height": 1000, "width": 650, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 420], "id": "1bae6be4-937f-445a-aeee-47fa4bcdec98", "name": "Sticky Note - Input & Prepare"}, {"parameters": {"content": "## Initiate Resumable Upload\n\n*   **Node:** `getUpLoadUrl1` (HTTP Request)\n*   **Purpose:** This node initiates a resumable file upload process to the Google Generative Language API's file upload endpoint. It sends metadata about the file (size, type, display name) and requests a unique upload URL for the next step.\n*   **Configuration:**\n    *   `Method`: `POST`\n    *   `URL`: `https://generativelanguage.googleapis.com/upload/v1beta/files`\n    *   `Headers`: Includes `X-Goog-Upload-Protocol: resumable`, `X-Goog-Upload-Command: start`, `X-Goog-Upload-Header-Content-Length` (using `={{ $binary.data.fileSize }}`), `X-Goog-Upload-Header-Content-Type` (using `={{ $binary.data.mimeType }}`), `Content-Type: application/json`.\n    *   `Body`: JSON body containing file `display_name` (using `={{ $json.data.filename }}`).\n    *   `Options`: `Response: fullResponse` is crucial to capture the `x-goog-upload-url` from the response headers.\n\n---\n\n## 啟動可續傳上傳\n\n*   **節點：** `getUpLoadUrl1` (HTTP Request)\n*   **目的：** 此節點向 Google Generative Language API 的檔案上傳端點啟動一個可續傳的檔案上傳流程。它發送檔案的後設資料（大小、類型、顯示名稱），並請求一個唯一的上傳 URL 以用於下一步。\n*   **設定：**\n    *   `Method` (方法)：`POST`\n    *   `URL` (網址)：`https://generativelanguage.googleapis.com/upload/v1beta/files`\n    *   `Headers` (標頭)：包含 `X-Goog-Upload-Protocol: resumable`、`X-Goog-Upload-Command: start`、`X-Goog-Upload-Header-Content-Length` (使用 `={{ $binary.data.fileSize }}`), `X-Goog-Upload-Header-Content-Type` (使用 `={{ $binary.data.mimeType }}`), `Content-Type: application/json`。\n    *   `Body` (內文)：包含檔案 `display_name` (使用 `={{ $json.data.filename }}`) 的 JSON 內文。\n    *   `Options` (選項)：`Response: fullResponse` (完整回應) 至關重要，用於從回應標頭中捕獲 `x-goog-upload-url`。", "height": 1000, "width": 650, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1280, 420], "id": "f7e135df-1ca4-4903-aad5-c5aaa328f169", "name": "Sticky Note - Initiate Upload"}, {"parameters": {"content": "## Combine Data\n\n*   **Node:** `Merge3` (Merge)\n*   **Purpose:** This node combines the data prepared in \"Edit Fields\" (which includes original form inputs and file metadata) with the response from \"getUpLoadUrl1\" (which contains the upload URL in its headers). This merged data item is then passed to the next step for the actual file upload.\n*   **Configuration:**\n    *   `Mode`: `combine`\n    *   `Combine By`: `combineByPosition` (Combines the first item from input 0 with the first item from input 1).\n\n---\n\n## 合併資料\n\n*   **節點：** `Merge3` (合併)\n*   **目的：** 此節點將在「Edit Fields」中準備好的資料（包含原始表單輸入和檔案後設資料）與來自「getUpLoadUrl1」的回應（其標頭中包含上傳 URL）結合起來。這個合併後的資料項目隨後傳遞到下一步進行實際的檔案上傳。\n*   **設定：**\n    *   `Mode` (模式)：`combine` (合併)\n    *   `Combine By` (合併方式)：`combineByPosition` (按位置合併，將輸入 0 的第一個項目與輸入 1 的第一個項目合併)。", "height": 1000, "width": 600, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1940, 420], "id": "e26feff0-ecec-46d9-844c-329bc358e7db", "name": "Sticky Note - <PERSON><PERSON> Data"}, {"parameters": {"content": "## Upload File Data\n\n*   **Node:** `HTTP Request8` (HTTP Request)\n*   **Purpose:** This node performs the actual upload of the binary file data to the upload URL obtained in the previous step. It uses specific headers required by the resumable upload protocol to indicate the command and file size.\n*   **Configuration:**\n    *   `Method`: `POST`\n    *   `URL`: Uses the upload URL from the previous node's header, `={{ $json.headers['x-goog-upload-url'] }}`.\n    *   `Headers`: Includes `Content-Length` (using `={{ $('Edit Fields').item.json.NUM_BYTES }}`), `X-Goog-Upload-Offset: 0`, `X-Goog-Upload-Command: upload, finalize`. Note the reference to `Edit Fields` for file size, which is available in the merged data.\n    *   `Body`: Sends the binary file data from the original input (`Input Data Field Name: data`).\n\n---\n\n## 上傳檔案資料\n\n*   **節點：** `HTTP Request8` (HTTP Request)\n*   **目的：** 此節點將二進位檔案資料實際上傳到上一步獲得的上傳 URL。它使用可續傳上傳協定所需的特定標頭來指示命令和檔案大小。\n*   **設定：**\n    *   `Method` (方法)：`POST`\n    *   `URL` (網址)：使用上一個節點回應標頭中的上傳 URL，`={{ $json.headers['x-goog-upload-url'] }}`。\n    *   `Headers` (標頭)：包含 `Content-Length` (使用 `={{ $('Edit Fields').item.json.NUM_BYTES }}`), `X-Goog-Upload-Offset: 0`, `X-Goog-Upload-Command: upload, finalize`。注意這裡引用了「Edit Fields」中的檔案大小，此資訊在合併後的資料中是可用的。\n    *   `Body` (內文)：發送原始輸入中的二進位檔案資料 (`Input Data Field Name`: `data`)。", "height": 990, "width": 650, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2560, 420], "id": "a4047440-c4c0-4df0-8140-2d05544ebdec", "name": "Sticky Note - Upload File"}, {"parameters": {"content": "## Call Gemini API with File\n\n*   **Node:** `HTTP Request9` (HTTP Request)\n*   **Purpose:** This is the final step where the Gemini API is called to process the uploaded file. The API request includes the file's URI (returned in the response from the *previous* upload step, HTTP Request 8), its MIME type, a text prompt (e.g., \"summarize\"), the selected model from the form input, and the specified thinking budget.\n*   **Configuration:**\n    *   `Method`: `POST`\n    *   `URL`: Uses the selected model from the form input, `https://generativelanguage.googleapis.com/v1beta/models/{{ $('Edit Fields').item.json.model }}:generateContent`. Note the reference to `Edit Fields` for the model name.\n    *   `Headers`: `Content-Type: application/json`.\n    *   `Body`: JSON body constructed to call `generateContent`, including:\n        *   `parts`: Contains a text prompt (`\"text\": \"summarize\"`) and `file_data` referencing the uploaded file using `mime_type` (using `={{ $json.file.mimeType }}`) and `file_uri` (using `={{ $json.file.uri }}`). The file URI comes from the response of the previous upload node.\n        *   `generationConfig`: Includes `thinkingConfig` with the specified `thinkingBudget` (using `={{ $('Edit Fields').item.json.thinkingBudget }}`). Note reference to `Edit Fields` again.\n\n---\n\n## 使用檔案呼叫 Gemini API\n\n*   **節點：** `HTTP Request9` (HTTP Request)\n*   **目的：** 這是最後一步，呼叫 Gemini API 來處理上傳的檔案。API 請求包含檔案的 URI（在*上一步*上傳節點 HTTP Request 8 的回應中傳回）、其 MIME 類型、一個文字提示（例如：「summarize」）、表單輸入中選擇的模型以及指定的思考預算。\n*   **設定：**\n    *   `Method` (方法)：`POST`\n    *   `URL` (網址)：使用表單輸入中選擇的模型，`https://generativelanguage.googleapis.com/v1beta/models/{{ $('Edit Fields').item.json.model }}:generateContent`。注意這裡引用了「Edit Fields」中的模型名稱。\n    *   `Headers` (標頭)：`Content-Type: application/json`。\n    *   `Body` (內文)：構建用於呼叫 `generateContent` 的 JSON 內文，包括：\n        *   `parts` (部分)：包含一個文字提示（`\"text\": \"summarize\"`）和 `file_data`，後者使用 `mime_type` (使用 `={{ $json.file.mimeType }}`) 和 `file_uri` (使用 `={{ $json.file.uri }}`) 引用上傳的檔案。檔案 URI 來自上一個上傳節點的回應。\n        *   `generationConfig` (生成配置)：包含 `thinkingConfig` 和指定的 `thinkingBudget` (使用 `={{ $('Edit Fields').item.json.thinkingBudget }}`)。再次注意引用「Edit Fields」。", "height": 1000, "width": 670, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3220, 420], "id": "342b67a8-b7ef-4e63-b1d5-d8b66c56f99f", "name": "<PERSON>y Note - Call Gemini"}, {"parameters": {"formTitle": "大型文件理解（思考模式）", "formDescription": "上传文件、图片、影片等（可以超过20M）", "formFields": {"values": [{"fieldLabel": "data", "fieldType": "file", "multipleFiles": false}, {"fieldLabel": "model", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "gemini-2.5-flash-preview-05-20"}, {"option": "gemini-2.5-flash-preview-04-17"}, {"option": "gemini-2.0-flash"}]}}, {"fieldLabel": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "number", "placeholder": "0-24576"}, {"fieldLabel": "prompt", "fieldType": "textarea", "placeholder": "提取图中的内容并保持原有格式输出/帮我总结一下这篇文章的主要内容"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [760, 180], "id": "2f8090cc-cd8e-4bd0-b863-7790a313e528", "name": "On form submission6", "webhookId": "e975c4e7-7d42-465b-a574-593c008ae403"}, {"parameters": {"method": "POST", "url": "={{ $json.headers['x-goog-upload-url'] }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Length", "value": "={{ $('Edit Fields4').item.json.NUM_BYTES }}"}, {"name": "X-Goog-Upload-Offset", "value": "0"}, {"name": "X-Goog-Upload-Command", "value": "upload, finalize"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "={{ $('Edit Fields4').item.json.data.filename }}", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2800, 200], "id": "d253ea8b-cc57-4a29-b0cf-f0f25a3ed700", "name": "HTTP Request12"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2200, 200], "id": "14970b5c-1f95-4ab6-8fac-4daf8b82886f", "name": "Merge4"}, {"parameters": {"assignments": {"assignments": [{"id": "31fc816c-88eb-4832-8c58-9409d5d76b34", "name": "NUM_BYTES", "value": "={{ $binary.data.fileSize }}", "type": "string"}, {"id": "9d83561b-0409-4e4e-8315-fff3dc395b88", "name": "MIME_TYPE", "value": "={{ $binary.data.mimeType }}", "type": "string"}, {"id": "f705ece3-5ea6-4f9d-bc5b-332876289bca", "name": "DISPLAY_NAME", "value": "={{ $binary.data.fileName }}", "type": "string"}, {"id": "efc87b7e-c3a4-47e0-8290-2f97398e5531", "name": "model", "value": "={{ $json.model }}", "type": "string"}, {"id": "dd29755c-cda9-4756-8c05-6697264f5e2d", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "={{ $if($json && $json.thinkingBudget, $json.thinkingBudget, 0) }}", "type": "number"}, {"id": "864d0d52-2ce1-437b-93a9-d1cc77c32736", "name": "prompt", "value": "={{ $json.prompt }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1160, 180], "id": "fbea7c2f-1d92-4a68-a99e-a2fbc1cfb980", "name": "Edit Fields4"}, {"parameters": {"method": "POST", "url": "=https://generativelanguage.googleapis.com/v1beta/models/{{ $('Edit Fields4').item.json.model }}:generateContent ", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n      \"contents\": [{\n        \"parts\":[\n          {\n          \"text\": \"{{ $('Edit Fields4').item.json.prompt }}\"},\n          {\"file_data\":{\n                 \"mime_type\": \"{{ $json.file.mimeType }}\", \n                \"file_uri\": \"{{ $json.file.uri }}\"}\n          }\n         ]\n        }],\n        \"generationConfig\": {\n          \"thinkingConfig\": {\n            \"thinkingBudget\": {{ $('Edit Fields4').item.json.thinkingBudget }}\n    }\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3440, 200], "id": "cd2547d5-c255-47fa-8545-e54b186f76d5", "name": "HTTP Request15"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/upload/v1beta/files", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Goog-Upload-Protocol", "value": "resumable"}, {"name": "X-Goog-Upload-Command", "value": "start"}, {"name": "X-Goog-Upload-Header-Content-Length", "value": "={{ $binary.data.fileSize }}"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-Goog-Upload-Header-Content-Type", "value": "={{ $binary.data.mimeType }}"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "application/json", "body": "={   \"file\": {     \"display_name\": \"{{ $json.data.filename }}\"   } }", "options": {"response": {"response": {"fullResponse": true}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1620, 260], "id": "dd82276f-e4a5-468f-af04-905e3e9e801f", "name": "getUpLoadUrl3"}, {"parameters": {"content": "# Workflow", "height": 300, "width": 3260, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 100], "id": "49d70ccb-7531-4d38-87b8-4619897d0264", "name": "Sticky Note (Form Trigger)9"}, {"parameters": {"operation": "toText", "sourceProperty": "candidates[0].content.parts[0].text", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [3680, 200], "id": "6a6d47d9-b99e-45b8-97ce-0b4dbbd62d24", "name": "Convert to File"}], "pinData": {}, "connections": {"On form submission6": {"main": [[{"node": "Edit Fields4", "type": "main", "index": 0}]]}, "HTTP Request12": {"main": [[{"node": "HTTP Request15", "type": "main", "index": 0}]]}, "Merge4": {"main": [[{"node": "HTTP Request12", "type": "main", "index": 0}]]}, "Edit Fields4": {"main": [[{"node": "Merge4", "type": "main", "index": 0}, {"node": "getUpLoadUrl3", "type": "main", "index": 0}]]}, "getUpLoadUrl3": {"main": [[{"node": "Merge4", "type": "main", "index": 1}]]}, "HTTP Request15": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "23847583-0caa-490c-a825-55c0dcbfbbe0", "meta": {"instanceId": "04695fa805d662a7ca811a5d9568e6cc080c07e6ec9f81360f8146eccb701cc6"}, "id": "ZaYhIfSUYHj1vFcg", "tags": []}