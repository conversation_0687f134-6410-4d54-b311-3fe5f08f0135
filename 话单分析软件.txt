我想做一个话单分析软件，支持win7系统，exe格式。首先，它可以支持上传csv、xlsx、xls、txt格式的多个文件，软件包含“己方号码”“己方卡号”“己方机身码”“己方位置区”“己方小区”“己方基站名称”“对方号码”“呼叫日期”“呼叫时间”“截获时间”“时长”“呼叫类型”“己方归属地”“己方归属地名称”“对方归属地”“对方归属地名称”“己方通话地”“己方通话地名称”“对方通话地”“对方通话地名称”等字段。其中，“呼叫日期”的格式支持yyyy/m/d或yyyy/mm/dd两种格式，“呼叫时间”的格式为h:mm:ss，“截获时间”的格式为yyyy-mm-dd hh:mm:ss的格式。上传的文件要有选择字段的功能，如果有文件中有上述字段的则直接予以匹配，如果没有上述字段则可以选择匹配上述字段。同时，上传的文件中“己方号码”“对方号码”为必须具备的字段，“呼叫日期”“呼叫时间”和“截获时间”之一是必须具备的字段（即如果有呼叫日期和呼叫时间则可以不用截获时间，如果有截获时间则不需要呼叫日期和呼叫时间），其他字段不是必须具备的字段。
该话单分析软件需要具备以下几个分析功能：1.上传文件的匹配好字段后可以展示完整的话单，既可以展示单个号码的话单，也可以展示多个号码合并起来的话单，且每个字段都可以进行排序，排序时扩展选定区域。2.对方号码统计。既可以对上传的某个号码的话单中的对方号码进行统计，也可以对多个号码的话单中的对方号码进行统计，统计后单独展示，字段包含“己方号码”“对方号码”“通话次数”“通话时间段”“总时长”。其中，“通话时间段”是将“呼叫日期”与“呼叫时间”拼接后，统计对方号码起止时间或者对方号码“截获时间”的起止时间。“通话次数”是对方号码出现的总次数。“总时长”是对方号码的时长总和。3.共同联系人分析。通过上传的多个号码换单的“己方号码”“对方号码”“呼叫日期”“呼叫时间”“截获时间”的列，根据己方号码对应的对方号码来分析共同联系人，并展示对方号码具体的“呼叫日期”和“呼叫时间”拼接后的起止时间或“截获时间”的起止时间及己方号码出现的个数和总次数。
请帮我写一个Python脚本。