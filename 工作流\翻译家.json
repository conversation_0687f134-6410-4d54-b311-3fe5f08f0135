{"name": "翻译家", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-140, -80], "id": "dc6b4f57-4cb1-486d-a7dd-b864648a5c7d", "name": "When chat message received", "webhookId": "7a442e68-ff89-41e9-9abf-840efef38a61"}, {"parameters": {"options": {"systemMessage": "=你是一个精通多国语言的翻译专家。请根据{{ $json.chatInput }}中的内容，如果是中文的，请将中文翻译成英文和日文；如果是非中文的，请将所有非中文的翻译成中文。我发给你所有的内容都是需要翻译的内容，你只需要回答翻译结果。翻译结果如果是中文的请符合中文的语言习惯，如果是英文或者日文也请符合相应语言的习惯。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [80, 40], "id": "d481ccd0-dec2-4368-944b-0e1017c41648", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17-thinking", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [100, 240], "id": "a5feac91-6d45-4388-82fb-6e2439cb1681", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "szF5eVdIdZNa8Mzn", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"options": {"systemMessage": "你是一个精通多国语言的翻译专家。请根据{{ $json.chatInput }}中的内容，如果是中文的，请将中文翻译成英文和日文；如果是非中文的，请将所有非中文的翻译成中文。我发给你所有的内容都是需要翻译的内容，你只需要回答翻译结果。翻译结果如果是中文的请符合中文的语言习惯，如果是英文或者日文也请符合相应语言的习惯。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [80, -220], "id": "8f7c2e5a-3b48-4bfe-8b71-daecb2bda930", "name": "AI Agent1"}, {"parameters": {"model": {"__rl": true, "value": "deepseek-ai/DeepSeek-R1", "mode": "list", "cachedResultName": "deepseek-ai/DeepSeek-R1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [100, -80], "id": "dfb9a65f-6e68-415f-b7fa-6fc49e2c207a", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "JHzhQqdAw0zsKgsv", "name": "OpenAi account 2"}}}, {"parameters": {"options": {"systemMessage": "你是一个精通多国语言的翻译专家。请根据{{ $json.chatInput }}中的内容，如果是中文的，请将中文翻译成英文和日文；如果是非中文的，请将所有非中文的翻译成中文。我发给你所有的内容都是需要翻译的内容，你只需要回答翻译结果。翻译结果如果是中文的请符合中文的语言习惯，如果是英文或者日文也请符合相应语言的习惯。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [80, -540], "id": "c9a7ccfc-3dc8-426c-90f4-51c20b18ca5a", "name": "AI Agent2"}, {"parameters": {"model": {"__rl": true, "value": "Qwen/Qwen3-235B-A22B", "mode": "list", "cachedResultName": "Qwen/Qwen3-235B-A22B"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [100, -360], "id": "0de5eabe-4de1-4005-9656-50190f32ae72", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "JHzhQqdAw0zsKgsv", "name": "OpenAi account 2"}}}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [580, -200], "id": "540980fa-8132-4f3c-adf7-0302c35b7ffc", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "const items = $input.all(); // 获取来自 Merge 节点的所有输入条目\nlet combinedReadableText = \"\";\n\n// 定义AI模型的名称，顺序与Merge节点输入的顺序对应\nconst modelNames = ['Qwen3-235B', 'Deepseek-R1', 'Gemini-2.5-flash'];\n\nitems.forEach((item, index) => {\n  let singleAgentOutputText = \"\";\n\n  // 1. 提取单个AI Agent的输出文本\n  if (item.json && typeof item.json.output === 'string') {\n    singleAgentOutputText = item.json.output.trim(); // 移除首尾可能存在的空白字符\n  } else if (typeof item.json === 'string') {\n    // 兼容Merge节点直接输出字符串数组的情况\n    singleAgentOutputText = item.json.trim();\n  }\n\n  if (singleAgentOutputText) {\n    // 2. 移除Markdown的加粗标记 (**)\n    singleAgentOutputText = singleAgentOutputText.replace(/\\*\\*/g, \"\");\n\n    // 3. 获取当前AI Agent的模型名称\n    //    如果items的数量超过了modelNames数组的长度，则使用通用名称\n    const agentName = modelNames[index] || `未知AI模型 ${index + 1}`;\n\n    // 4. 为每个AI Agent的输出添加指定的模型名称作为标识符\n    combinedReadableText += `--- ${agentName} ---\\n`;\n    combinedReadableText += singleAgentOutputText;\n    combinedReadableText += \"\\n\\n\"; // 使用两个换行符来清晰分隔不同Agent的输出\n  }\n});\n\n// 移除最后添加的多余的两个换行符\nif (combinedReadableText.length > 0 && combinedReadableText.endsWith(\"\\n\\n\")) {\n  combinedReadableText = combinedReadableText.slice(0, -2);\n} else if (combinedReadableText.length > 0 && combinedReadableText.endsWith(\"\\n\")) {\n  combinedReadableText = combinedReadableText.slice(0, -1);\n}\n\n// 返回一个包含所有整合后且更易读的翻译文本的对象\nreturn { all_translations_readable: combinedReadableText };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [760, -200], "id": "88d78ab0-a850-4906-b8f8-e9302e2b3f07", "name": "Code"}, {"parameters": {"operation": "toText", "sourceProperty": "all_translations_readable", "options": {"encoding": "utf8"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [960, -200], "id": "f9f0107a-8643-4d75-b71e-c7c627471c3c", "name": "Convert to File"}, {"parameters": {"operation": "write", "fileName": "/home/<USER>/翻译结果.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1180, -200], "id": "6c6f51c3-32ad-4b29-a777-ff6a9ced5c60", "name": "Read/Write Files from Disk"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}, {"node": "AI Agent2", "type": "main", "index": 0}, {"node": "AI Agent1", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "AI Agent2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent2", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Merge": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "51c30a99-f944-4f12-8d25-cdd84bf7a3da", "meta": {"templateCredsSetupCompleted": true, "instanceId": "39f146a945d7fc9791e42ead0daaf69c5dec2e750aba053525a0f0592eacdd62"}, "id": "vJRiK7dItz6IxOSa", "tags": []}