import sys
import json
from crawler import HotNewsCrawler

def test_toutiao_methods():
    """测试今日头条热点爬取的各种方法"""
    print("开始测试今日头条热点爬取...")
    
    crawler = HotNewsCrawler()
    
    # 测试直接方法
    print("\n测试 fetch_toutiao_hot_direct 方法:")
    direct_results = crawler.fetch_toutiao_hot_direct(limit=10)
    print(f"获取结果数量: {len(direct_results)}")
    if direct_results:
        print("样例数据:")
        print(json.dumps(direct_results[0], ensure_ascii=False, indent=2))
    
    # 测试方法1
    print("\n测试 fetch_toutiao_hot_method1 方法:")
    method1_results = crawler.fetch_toutiao_hot_method1(limit=10)
    print(f"获取结果数量: {len(method1_results)}")
    if method1_results:
        print("样例数据:")
        print(json.dumps(method1_results[0], ensure_ascii=False, indent=2))
    
    # 测试方法2
    print("\n测试 fetch_toutiao_hot_method2 方法:")
    method2_results = crawler.fetch_toutiao_hot_method2(limit=10)
    print(f"获取结果数量: {len(method2_results)}")
    if method2_results:
        print("样例数据:")
        print(json.dumps(method2_results[0], ensure_ascii=False, indent=2))
    
    # 测试备用API
    print("\n测试 _fetch_toutiao_from_oioweb 方法:")
    oioweb_results = crawler._fetch_toutiao_from_oioweb(limit=10)
    print(f"获取结果数量: {len(oioweb_results)}")
    if oioweb_results:
        print("样例数据:")
        print(json.dumps(oioweb_results[0], ensure_ascii=False, indent=2))
    
    # 测试微博转换方法
    print("\n测试 _fetch_toutiao_from_weibo 方法:")
    weibo_results = crawler._fetch_toutiao_from_weibo(limit=10)
    print(f"获取结果数量: {len(weibo_results)}")
    if weibo_results:
        print("样例数据:")
        print(json.dumps(weibo_results[0], ensure_ascii=False, indent=2))
    
    # 测试组合方法
    print("\n测试 fetch_toutiao_hot 组合方法:")
    combined_results = crawler.fetch_toutiao_hot(limit=10)
    print(f"获取结果数量: {len(combined_results)}")
    if combined_results:
        print("样例数据:")
        print(json.dumps(combined_results[0], ensure_ascii=False, indent=2))
    
    # 验证热度值
    print("\n验证热度值:")
    if combined_results:
        for item in combined_results[:5]:
            print(f"标题: {item['title']}, 热度: {item['heat']}, 平台: {item['platform']}")
    
    print("\n测试完成")

if __name__ == "__main__":
    test_toutiao_methods() 