{"name": "自然语言查询MySQL数据库", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [100, 140], "id": "4c83bc97-f2ff-4f0e-afcb-dd957f68a289", "name": "When clicking ‘Test workflow’"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n    t.TABLE_NAME AS `tablename`,\n    t.TABLE_COMMENT AS `tablecomment`,\n    c.COLUMN_NAME AS `columnname`,\n    c.COLUMN_COMMENT AS `columncomment`,\n    c.COLUMN_TYPE AS `columntype`\nFROM \n    information_schema.TABLES t\nJOIN \n    information_schema.COLUMNS c \n    ON t.TABLE_SCHEMA = c.TABLE_SCHEMA AND t.TABLE_NAME = c.TABLE_NAME\nWHERE \n    t.TABLE_SCHEMA = DATABASE()\nORDER BY \n    t.TABLE_NAME, c.ORDINAL_POSITION; -- 按表和字段顺序排序", "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [340, 140], "id": "9b9705ba-4795-4ae2-91df-f605ca0c557c", "name": "MySQL", "credentials": {"mySql": {"id": "tKvsiDJpKOdyynpO", "name": "MySQL account 2"}}}, {"parameters": {"jsCode": "const result = {};\nitems.forEach(item => {\n  const tableName = item.json.tablename;\n\n  // 如果该表名还没添加到 result，则初始化\n  if (!result[tableName]) {\n    result[tableName] = {\n      tablename: tableName,\n      tablecomment: item.json.tablecomment || \"\",\n      fields: [] // 用于存储该表的所有字段\n    };\n  }\n\n  // 添加字段信息\n  result[tableName].fields.push({\n    columnname: item.json.columnname,\n    columncomment: item.json.columncomment || \"\",\n    columntype: item.json.columntype || \"\"\n  });\n});\n\n// 转换为数组返回\nconst tempRes= Object.values(result).map(table => ({ json: table }));\nreturn [\n  {\n    json: {\n      fileContent: tempRes.map(item => {\n        let tableName = `# ${item.json.tablecomment} ${item.json.tablename}`;\n        let fields = item.json.fields.map(field => `- ${field.columnname} ${field.columntype} ${field.columncomment}`).join(\"\\n\");\n        return `${tableName}\\n${fields}\\n`;\n      }).join(\"\\n\")\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [540, 140], "id": "bf685153-d6c8-4c87-9c1e-a64b13b22615", "name": "Code"}, {"parameters": {"operation": "toText", "sourceProperty": "fileContent", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [760, 140], "id": "17adfe63-9b28-4d58-8572-2f185e34709d", "name": "Convert to File"}, {"parameters": {"operation": "write", "fileName": "/home/<USER>/hrmshcema.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [980, 140], "id": "3316cec9-175b-46d9-a8fe-43ff7efbee8d", "name": "Read/Write Files from Disk"}, {"parameters": {"content": "## 生成数据结构文件\n** 请在数据库结构发生变化的时候执行这个工作流 **", "height": 320, "width": 1180, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "12bb71ee-6b0b-415e-b484-9c664291bdb3", "name": "<PERSON><PERSON>"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [60, 520], "id": "3858d4a2-f115-42c7-ada2-d7091630cd6d", "name": "When chat message received", "webhookId": "aa9571f3-5576-4f00-8150-b65c5cabea33"}, {"parameters": {"fileSelector": "/home/<USER>/hrmshcema.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [280, 520], "id": "0ccc7e78-2bef-43a2-96a1-4d2677b79eb7", "name": "Read/Write Files from Disk1"}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [500, 520], "id": "33d92d7a-0eb6-44bb-8144-95bf1c42d777", "name": "Extract from File"}, {"parameters": {"promptType": "define", "text": "={{ $('When chat message received').item.json.chatInput }}", "options": {"systemMessage": "=你是一个经验丰富的DBA，精通MySQL数据库。你会根据用户的需求和数据结构，生成一个查询语句。只需要返回一个可执行的SQL语句，不需要任何解释。\n数据结构：{{ $json.data }}\n## 对话示例\n用户：查询职工总人数\n正确的回答输出：select count(*) as totalworkers from zm_workers where history is null and state=1;\n错误的回答输出：```sql select count(*) as totalworkers from zm_workers where history is null and state=1;```\n注意事项：输出的回答结果以select、insert等数据操纵语言开头，不要出现额外的sql、think等内容。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [720, 520], "id": "ad82eed0-e2bb-4645-aa53-436a87cb0523", "name": "AI Agent"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "{{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.mySql", "typeVersion": 2.4, "position": [1080, 520], "id": "db18457e-316d-4a5d-a4bf-2cddd7c36282", "name": "MySQL1", "credentials": {"mySql": {"id": "tKvsiDJpKOdyynpO", "name": "MySQL account 2"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1320, 520], "id": "ab7c23ac-2ab6-4b0e-a866-f318bd7fb018", "name": "Convert to File1"}, {"parameters": {"operation": "write", "fileName": "=/home/<USER>/selectResult.csv", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1540, 520], "id": "bf514080-e5b1-45c9-8884-2220d0e62faa", "name": "Read/Write Files from Disk2"}, {"parameters": {"content": "## 自然语言查询MySQL数据库", "height": 480, "width": 1860}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [20, 380], "id": "678d374c-6d5e-4c90-86b9-bc32c5db3e29", "name": "Sticky Note1"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17-thinking", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [600, 740], "id": "b40ec5c5-5442-4c2c-b5af-a1c6411b67b3", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "OgN8N8yxscAZwEGQ", "name": "Google Gemini(PaLM) Api account"}}}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "MySQL", "type": "main", "index": 0}]]}, "MySQL": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "MySQL1", "type": "main", "index": 0}]]}, "MySQL1": {"main": [[{"node": "Convert to File1", "type": "main", "index": 0}]]}, "Convert to File1": {"main": [[{"node": "Read/Write Files from Disk2", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5f676a06-a2d0-4aa3-96ce-759f9e0c829f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "da293d8bc9d55da5a991575f3ab795aa37c2d9ef15407feeb5edfc2322d6467d"}, "id": "frODIyIiwZ2f63bD", "tags": []}