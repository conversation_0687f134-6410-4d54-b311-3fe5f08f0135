# Project Structure

## Root Directory Organization

### Core Application Folders
- **成品代码库/** - Production-ready Python tools and utilities
- **全网热点新闻分析软件/** - Hot news analysis application with complete module structure
- **开源情报搜索助手/** - OSINT search assistant with config, data, and utils subdirectories
- **claudia/** - Tauri-based desktop application (Rust + web frontend)
- **工作流/** - Workflow configurations (.yml, .json files for automation)

### Configuration & Settings
- **.kiro/** - Kiro IDE settings and steering rules
- **.comate/** - Comate MCP configuration
- **.roo/** - Additional MCP configuration
- **config/** - Application-specific configurations
- **ico/** - Icon resources for applications

### Data & Processing
- **HD/** - Data files organized by identifier/case
- **待匹配企业名单/** - Enterprise matching data sets
- **决赛附件/** - Competition/case attachments
- **数学题/** - Math problem images and resources

### Build & Distribution
- **dist/** - Built/compiled applications
- **build/** - Build artifacts and temporary files
- **__pycache__/** - Python bytecode cache

## File Naming Conventions

- **Chinese naming**: Most files use descriptive Chinese names
- **Executable suffix**: Production tools end with `.exe`
- **Data files**: Use descriptive names with relevant identifiers
- **Configuration files**: Standard formats (`.json`, `.ini`, `.yml`)

## Development Patterns

- **Standalone tools**: Each major function is a separate executable
- **Modular structure**: Tools organized in logical groupings
- **Data-driven**: Heavy emphasis on Excel and database file processing
- **Configuration external**: Settings kept in separate config files
- **Bilingual support**: Mix of Chinese and English in codebase

## Key Entry Points

- Root level Python scripts for quick utilities
- **成品代码库/** for production tools
- **claudia/src/** for Tauri application development
- Individual application folders for complex tools