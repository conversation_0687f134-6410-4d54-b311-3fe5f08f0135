import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import threading
from datetime import datetime
import io
import pickle
import struct
from embedded_data import get_embedded_data

class DataQueryTool:
    def __init__(self, root):
        self.root = root
        self.root.title("身份证号码查询工具")
        self.root.geometry("800x600")
        
        # 使用字典作为内存数据库，提供更快的查询速度
        self.data_dict = {
            'name': {},    # 姓名索引
            'id': {},      # 身份证号索引
            'phone': {},   # 电话号码索引
            'records': []  # 所有记录
        }
        self.data_loaded = False
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建数据状态标签
        self.status_label = ttk.Label(self.main_frame, text="正在加载数据...")
        self.status_label.grid(row=0, column=0, columnspan=2, pady=10)
        
        # 创建进度条
        self.progress = ttk.Progressbar(self.main_frame, length=300, mode='determinate')
        self.progress.grid(row=1, column=0, columnspan=2, pady=5)
        
        # 创建查询框架
        self.query_frame = ttk.LabelFrame(self.main_frame, text="查询条件", padding="10")
        self.query_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # 创建查询输入框
        ttk.Label(self.query_frame, text="姓名:").grid(row=0, column=0, padx=5)
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(self.query_frame, textvariable=self.name_var)
        self.name_entry.grid(row=0, column=1, padx=5)
        
        ttk.Label(self.query_frame, text="身份证号:").grid(row=0, column=2, padx=5)
        self.id_var = tk.StringVar()
        self.id_entry = ttk.Entry(self.query_frame, textvariable=self.id_var)
        self.id_entry.grid(row=0, column=3, padx=5)
        
        ttk.Label(self.query_frame, text="电话号码:").grid(row=0, column=4, padx=5)
        self.phone_var = tk.StringVar()
        self.phone_entry = ttk.Entry(self.query_frame, textvariable=self.phone_var)
        self.phone_entry.grid(row=0, column=5, padx=5)
        
        # 创建查询按钮
        self.query_btn = ttk.Button(self.query_frame, text="查询", command=self.query_data)
        self.query_btn.grid(row=0, column=6, padx=10)
        self.query_btn.config(state='disabled')  # 初始禁用查询按钮
        
        # 创建结果显示区域
        self.result_frame = ttk.LabelFrame(self.main_frame, text="查询结果", padding="10")
        self.result_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        # 创建Treeview用于显示结果
        self.tree = ttk.Treeview(self.result_frame, columns=("XM", "GMSFHM", "LXDH"), show="headings")
        self.tree.heading("XM", text="姓名")
        self.tree.heading("GMSFHM", text="身份证号")
        self.tree.heading("LXDH", text="电话号码")
        
        # 设置列宽
        self.tree.column("XM", width=100)
        self.tree.column("GMSFHM", width=200)
        self.tree.column("LXDH", width=150)
        
        # 添加滚动条
        self.scrollbar = ttk.Scrollbar(self.result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=self.scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置grid权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(3, weight=1)
        self.result_frame.columnconfigure(0, weight=1)
        self.result_frame.rowconfigure(0, weight=1)
        
        # 加载数据
        self.root.after(100, self.load_embedded_data)
    
    def load_embedded_data(self):
        """加载嵌入的数据到内存"""
        try:
            # 获取嵌入的数据
            data_content = get_embedded_data()
            lines = data_content.splitlines()
            total_lines = len(lines)
            
            self.progress['maximum'] = total_lines
            processed_lines = 0
            
            # 批量处理数据
            batch_size = 10000
            current_batch = []
            
            for line in lines:
                try:
                    fields = line.strip().split(',')
                    if len(fields) >= 3:
                        # 去除引号
                        name = fields[0].strip('"').strip()
                        id_num = fields[1].strip('"').strip()
                        phone = fields[2].strip('"').strip()
                        record = (name, id_num, phone)
                        
                        # 添加到记录列表
                        record_idx = len(self.data_dict['records'])
                        self.data_dict['records'].append(record)
                        
                        # 更新索引
                        self.data_dict['name'].setdefault(name, set()).add(record_idx)
                        self.data_dict['id'].setdefault(id_num, set()).add(record_idx)
                        self.data_dict['phone'].setdefault(phone, set()).add(record_idx)
                        
                        processed_lines += 1
                        if processed_lines % 1000 == 0:
                            self.progress['value'] = processed_lines
                            self.status_label.config(text=f"正在加载数据... {processed_lines:,} 条记录")
                            self.root.update_idletasks()
                        
                except Exception as e:
                    print(f"处理行时出错: {str(e)}")
                    continue
            
            self.data_loaded = True
            self.status_label.config(text=f"数据加载完成，共{processed_lines}条记录")
            self.query_btn.config(state='normal')  # 启用查询按钮
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {str(e)}")
            self.status_label.config(text="加载数据失败")
    
    def query_data(self):
        """查询数据"""
        if not self.data_loaded:
            messagebox.showwarning("警告", "数据正在加载中，请稍后再试")
            return
            
        name = self.name_var.get().strip()
        id_num = self.id_var.get().strip()
        phone = self.phone_var.get().strip()
        
        if not any([name, id_num, phone]):
            messagebox.showwarning("警告", "请至少输入一个查询条件")
            return
        
        try:
            # 清空现有结果
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 使用集合操作查找匹配的记录
            result_indices = None
            
            if name:
                # 模糊匹配姓名
                name_matches = set()
                for key in self.data_dict['name'].keys():
                    if name in key:
                        name_matches.update(self.data_dict['name'][key])
                result_indices = name_matches if result_indices is None else result_indices.intersection(name_matches)
            
            if id_num:
                # 模糊匹配身份证号
                id_matches = set()
                for key in self.data_dict['id'].keys():
                    if id_num in key:
                        id_matches.update(self.data_dict['id'][key])
                result_indices = id_matches if result_indices is None else result_indices.intersection(id_matches)
            
            if phone:
                # 模糊匹配电话号码
                phone_matches = set()
                for key in self.data_dict['phone'].keys():
                    if phone in key:
                        phone_matches.update(self.data_dict['phone'][key])
                result_indices = phone_matches if result_indices is None else result_indices.intersection(phone_matches)
            
            # 获取结果
            results = []
            if result_indices:
                results = [self.data_dict['records'][i] for i in list(result_indices)[:1000]]
            
            # 显示结果
            for row in results:
                self.tree.insert("", "end", values=row)
            
            if len(results) >= 1000:
                messagebox.showinfo("查询完成", "找到超过1000条记录，仅显示前1000条")
            else:
                messagebox.showinfo("查询完成", f"共找到{len(results)}条记录")
            
        except Exception as e:
            messagebox.showerror("错误", f"查询失败: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = DataQueryTool(root)
    root.mainloop() 