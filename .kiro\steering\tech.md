# Technology Stack

## Core Technologies

- **Python 3.x**: Primary development language for data processing tools
- **JavaScript/HTML/CSS**: Web interfaces and browser-based tools
- **Rust + Tauri**: Cross-platform desktop applications (claudia project)
- **SQLite**: Local database storage for analysis results
- **Excel Processing**: Heavy use of .xlsx/.xls file manipulation

## Key Python Libraries

- **pandas**: Data manipulation and analysis
- **openpyxl/xlrd**: Excel file processing
- **tkinter**: GUI applications (based on naming patterns)
- **requests**: Web scraping and API calls
- **sqlite3**: Database operations
- **AI/LLM libraries**: For intelligent analysis features


## Configuration

- **MCP Integration**: Model Context Protocol configs in `.comate/mcp.json` and `.roo/mcp.json`
- **Proxy Settings**: `config/proxy_config.json` for network configurations
- **Application Configs**: Various `.ini` and `.json` files for tool-specific settings
