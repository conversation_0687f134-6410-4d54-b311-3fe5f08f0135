import base64

def generate_base64_data(input_file, output_file):
    """将输入文件转换为base64编码并写入到输出文件中"""
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 转换为base64
        encoded = base64.b64encode(content.encode('utf-8')).decode('utf-8')
        
        # 写入到输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('TZJKM_DATA = """')
            f.write(encoded)
            f.write('"""')
            
        print(f"成功将 {input_file} 转换为base64数据并保存到 {output_file}")
        
    except Exception as e:
        print(f"转换失败: {str(e)}")

if __name__ == "__main__":
    input_file = "tzjkm.txt"  # 源数据文件
    output_file = "base64_tzjkm.py"  # 输出文件
    generate_base64_data(input_file, output_file) 