app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: Echarts可视化助手
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.2.1@88c1b2c816ef2ea36fc411b35298a621b3260d34bc08bd9357772092728aadde
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: document-extractor
      id: 1749370106767-source-1749370147129-target
      source: '1749370106767'
      sourceHandle: source
      target: '1749370147129'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: document-extractor
        targetType: llm
      id: 1749370147129-source-1749370157620-target
      source: '1749370147129'
      sourceHandle: source
      target: '1749370157620'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: parameter-extractor
      id: 1749370157620-source-1749370264279-target
      source: '1749370157620'
      sourceHandle: source
      target: '1749370264279'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: parameter-extractor
        targetType: code
      id: 1749370264279-source-1749370328937-target
      source: '1749370264279'
      sourceHandle: source
      target: '1749370328937'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: answer
      id: 1749370328937-source-1749370382887-target
      source: '1749370328937'
      sourceHandle: source
      target: '1749370382887'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: parameter-extractor
        targetType: code
      id: 1749370264279-source-1749371940911-target
      source: '1749370264279'
      sourceHandle: source
      target: '1749371940911'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: answer
      id: 1749371940911-source-1749370382887-target
      source: '1749371940911'
      sourceHandle: source
      target: '1749370382887'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions: []
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          - remote_url
          label: text
          max_length: 48
          options: []
          required: true
          type: file
          variable: text
      height: 90
      id: '1749370106767'
      position:
        x: 88
        y: 275
      positionAbsolute:
        x: 88
        y: 275
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_array_file: false
        selected: false
        title: 文档提取器
        type: document-extractor
        variable_selector:
        - '1749370106767'
        - text
      height: 92
      id: '1749370147129'
      position:
        x: 392
        y: 275
      positionAbsolute:
        x: 392
        y: 275
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1749370147129'
          - text
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.0-flash
          provider: langgenius/gemini/google
        prompt_template:
        - id: 25d96f40-f616-4d39-a928-271ed499438a
          role: system
          text: '#角色

            你是一个数据整理专家，擅长数据的整理和转换

            #数据

            {{#1749370147129.text#}}

            #任务

            将数据转化为CSV格式'
        selected: false
        title: 格式转换csv
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1749370157620'
      position:
        x: 696
        y: 275
      positionAbsolute:
        x: 696
        y: 275
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: gemini-2.0-flash
          provider: langgenius/gemini/google
        parameters:
        - description: csvdata
          name: csvdata
          required: false
          type: string
        query:
        - '1749370157620'
        - text
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1749370264279'
      position:
        x: 1000
        y: 275
      positionAbsolute:
        x: 1000
        y: 275
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import csv\nimport json\n\ndef main(csv_string):\n    # 将CSV字符串分割成行\n\
          \    lines = csv_string.strip().split('\\n')\n\n    # 使用csv模块读取数据\n    reader\
          \ = csv.reader(lines)\n\n    # 将所有行转换为列表\n    data = [row for row in reader]\n\
          \n    # 将数字字符串转换为浮点数\n    for row in data[1:]:  # 跳过标题行\n        for i in\
          \ range(1, len(row)):\n            try:\n                row[i] = float(row[i])\n\
          \            except ValueError:\n                pass\n\n    # 创建完整的ECharts配置\n\
          \    echarts_config = {\n        \"legend\": {},\n        \"tooltip\": {},\n\
          \        \"dataset\": {\n            \"source\": data\n        },\n    \
          \    \"xAxis\": [\n            {\"type\": \"category\", \"gridIndex\": 0},\n\
          \            {\"type\": \"category\", \"gridIndex\": 1}\n        ],\n  \
          \      \"yAxis\": [\n            {\"gridIndex\": 0},\n            {\"gridIndex\"\
          : 1}\n        ],\n        \"grid\": [\n            {\"bottom\": \"55%\"\
          },\n            {\"top\": \"55%\"}\n        ],\n        \"series\": [\n\
          \            # 第一个网格中的柱状图系列\n            {\"type\": \"bar\", \"seriesLayoutBy\"\
          : \"row\"},\n            {\"type\": \"bar\", \"seriesLayoutBy\": \"row\"\
          },\n            {\"type\": \"bar\", \"seriesLayoutBy\": \"row\"},\n    \
          \        {\"type\": \"bar\", \"seriesLayoutBy\": \"row\"},\n           \
          \ {\"type\": \"bar\", \"seriesLayoutBy\": \"row\"},\n            {\"type\"\
          : \"bar\", \"seriesLayoutBy\": \"row\"},\n            # 第二个网格中的柱状图系列\n \
          \           {\"type\": \"bar\", \"xAxisIndex\": 1, \"yAxisIndex\": 1},\n\
          \            {\"type\": \"bar\", \"xAxisIndex\": 1, \"yAxisIndex\": 1},\n\
          \            {\"type\": \"bar\", \"xAxisIndex\": 1, \"yAxisIndex\": 1},\n\
          \            {\"type\": \"bar\", \"xAxisIndex\": 1, \"yAxisIndex\": 1},\n\
          \            {\"type\": \"bar\", \"xAxisIndex\": 1, \"yAxisIndex\": 1}\n\
          \        ]\n    }\n\n    #生成输出文件\n    output = \"\\n```echarts\\n\" + json.dumps(echarts_config,\
          \ indent=2, ensure_ascii=False) + \"\\n```\"\n    return {\"output\":output}"
        code_language: python3
        desc: ''
        outputs:
          output:
            children: null
            type: string
        selected: false
        title: 代码执行
        type: code
        variables:
        - value_selector:
          - '1749370264279'
          - csvdata
          variable: csv_string
      height: 54
      id: '1749370328937'
      position:
        x: 1304
        y: 275
      positionAbsolute:
        x: 1304
        y: 275
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1749370147129.text#}}

          <br/>

          {{#1749370328937.output#}}

          <br/>

          {{#1749371940911.output2#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 143
      id: '1749370382887'
      position:
        x: 1735.3596910987121
        y: 275
      positionAbsolute:
        x: 1735.3596910987121
        y: 275
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import csv\nimport json\n\ndef main(csv_string):\n    # 解析CSV数据\n \
          \   lines = csv_string.strip().split('\\n')\n    reader = csv.reader(lines)\n\
          \    data = [row for row in reader]\n       \n    # 转换数值类型并计算总粉丝数\n    total_fans\
          \ = 0\n    for row in data[1:]:  # 跳过标题行\n        for i in range(1, len(row)):\n\
          \            try:\n                row[i] = float(row[i])\n            \
          \    total_fans += row[i]\n            except ValueError:\n            \
          \    pass\n\n    # 创建ECharts配置\n    echarts_config = {\n        \"title\"\
          : [\n            {\"text\": \"城市粉丝分布对比\", \"left\": \"center\"}\n      \
          \  ],\n        \"legend\": {\"data\": [data[0][0]]},\n        \"tooltip\"\
          : {\"trigger\": \"axis\", \"shared\": True},\n        \"dataset\": {\n \
          \           \"source\": data\n        },\n        \"grid\": [\n        \
          \    {\n                \"top\": \"10%\",   # 柱状图顶部位置\n                \"\
          bottom\": \"65%\", # 柱状图底部位置\n                \"left\": \"0%\",   # 柱状图左侧位置\n\
          \                \"right\": \"10%\",  # 柱状图右侧位置\n                \"containLabel\"\
          : True\n            }\n        ],\n        \"xAxis\": [\n            {\"\
          type\": \"category\", \"gridIndex\": 0},\n            {\"show\": False,\
          \ \"gridIndex\": 1}  # 隐藏玫瑰图的x轴\n        ],\n        \"yAxis\": [\n    \
          \        {\"gridIndex\": 0},\n            {\"show\": False, \"gridIndex\"\
          : 1}  # 隐藏玫瑰图的y轴\n        ],\n        \"series\": [\n            # 柱状图系列（第一个网格）\n\
          \            {\n                \"type\": \"bar\",\n                \"seriesLayoutBy\"\
          : \"column\",\n                \"xAxisIndex\": 0,\n                \"yAxisIndex\"\
          : 0,\n                \"label\": {\"show\": True, \"position\": \"top\"\
          }\n            },\n            # 玫瑰图系列（第二个网格）\n            {\n         \
          \       \"type\": \"pie\",\n                \"radius\": [\"30%\", \"70%\"\
          ],\n                \"center\": [\"40%\", \"75%\"],\n                \"\
          roseType\": \"radius\",\n                \"seriesLayoutBy\": \"column\"\
          ,\n                \"label\": {\"show\": True, \"formatter\": \"{b}: {d}%\"\
          },\n                \"encode\": {\"itemName\": data[0][0], \"value\": data[0][1:]},\n\
          \                \"xAxisIndex\": 1,\n                \"yAxisIndex\": 1\n\
          \            }\n        ]\n    }\n\n    # 生成输出\n    return {\"output2\"\
          : f\"\\n```echarts\\n{json.dumps(echarts_config, indent=2, ensure_ascii=False)}\\\
          n```\"}"
        code_language: python3
        desc: ''
        outputs:
          output2:
            children: null
            type: string
        selected: false
        title: 代码执行 2
        type: code
        variables:
        - value_selector:
          - '1749370264279'
          - csvdata
          variable: csv_string
      height: 54
      id: '1749371940911'
      position:
        x: 1304
        y: 436.02165320044855
      positionAbsolute:
        x: 1304
        y: 436.02165320044855
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 3.6453419708450383
      y: 110.08359016930507
      zoom: 0.6909564497278353
