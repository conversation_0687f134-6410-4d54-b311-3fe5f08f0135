import pandas as pd
import numpy as np
from collections import Counter
import jieba
import jieba.analyse
from sklearn.feature_extraction.text import TfidfVectorizer
import logging
import re
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("analyzer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("HotNewsAnalyzer")

class HotNewsAnalyzer:
    """热点新闻分析类，负责分析爬取的热点数据"""
    
    def __init__(self, news_data=None):
        self.news_data = news_data
        self.df = None
        if news_data:
            self.df = pd.DataFrame(news_data)
            
        # 加载停用词
        self.stopwords = self._load_stopwords()
        
    def _load_stopwords(self):
        """加载停用词"""
        try:
            # 加载常用停用词
            stopwords = set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'])
            return stopwords
        except Exception as e:
            logger.error(f"加载停用词失败: {str(e)}")
            return set()
    
    def load_data(self, news_data):
        """加载新闻数据"""
        self.news_data = news_data
        self.df = pd.DataFrame(news_data)
        logger.info(f"已加载 {len(self.df)} 条新闻数据")
    
    def load_from_csv(self, filepath):
        """从CSV文件加载数据"""
        try:
            self.df = pd.read_csv(filepath)
            self.news_data = self.df.to_dict('records')
            logger.info(f"已从 {filepath} 加载 {len(self.df)} 条新闻数据")
        except Exception as e:
            logger.error(f"从CSV加载数据失败: {str(e)}")
    
    def preprocess_data(self):
        """数据预处理"""
        if self.df is None or self.df.empty:
            logger.warning("没有数据需要处理")
            return
        
        # 确保热度为数值型
        self.df['heat'] = pd.to_numeric(self.df['heat'], errors='coerce').fillna(0)
        
        # 去除标题中的特殊字符
        self.df['clean_title'] = self.df['title'].apply(lambda x: re.sub(r'[^\w\s]', '', str(x)))
        
        # 使用jieba分词
        self.df['words'] = self.df['clean_title'].apply(self.tokenize)
        
        logger.info("数据预处理完成")
    
    def tokenize(self, text):
        """对文本进行分词"""
        words = jieba.cut(text)
        return ' '.join([word for word in words if word not in self.stopwords and len(word.strip()) > 1])
    
    def normalize_heat(self):
        """归一化不同平台的热度数据"""
        if self.df is None or self.df.empty:
            logger.warning("没有数据需要归一化")
            return
        
        # 按平台分组，对热度进行归一化
        for platform, group in self.df.groupby('platform'):
            max_heat = group['heat'].max()
            min_heat = group['heat'].min()
            if max_heat > min_heat:  # 避免除零错误
                self.df.loc[self.df['platform'] == platform, 'normalized_heat'] = (self.df.loc[self.df['platform'] == platform, 'heat'] - min_heat) / (max_heat - min_heat)
            else:
                self.df.loc[self.df['platform'] == platform, 'normalized_heat'] = 1.0
        
        # 计算综合热度分数 (0-100)
        self.df['heat_score'] = self.df['normalized_heat'] * 100
        
        logger.info("热度归一化完成")
    
    def get_top_news(self, top_n=100):
        """获取热度最高的新闻"""
        if self.df is None or self.df.empty:
            logger.warning("没有数据可供分析")
            return []
        
        # 按热度分数排序
        if 'heat_score' in self.df.columns:
            sorted_df = self.df.sort_values(by='heat_score', ascending=False)
        else:
            sorted_df = self.df.sort_values(by='heat', ascending=False)
        
        return sorted_df.head(top_n).to_dict('records')
    
    def analyze_cross_platform(self):
        """分析跨平台出现的热点新闻"""
        if self.df is None or self.df.empty:
            logger.warning("没有数据可供分析")
            return []
        
        logger.info("开始跨平台热点分析")
        
        # 使用TF-IDF提取关键词
        tfidf = TfidfVectorizer(max_features=2000)  # 增加特征数量
        tfidf_matrix = tfidf.fit_transform(self.df['clean_title'])
        
        # 获取每个标题的关键词
        feature_names = tfidf.get_feature_names_out()
        title_keywords = {}
        
        for i, title in enumerate(self.df['title']):
            feature_index = tfidf_matrix[i, :].nonzero()[1]
            tfidf_scores = zip(feature_index, [tfidf_matrix[i, x] for x in feature_index])
            title_keywords[title] = {feature_names[i]: s for i, s in tfidf_scores}
        
        # 查找相似新闻
        similar_news_groups = []
        processed_titles = set()
        
        # 基于标题文本相似度的直接比较函数
        def text_similarity(text1, text2):
            # 计算两个文本的字符重叠率
            set1 = set(text1)
            set2 = set(text2)
            overlap = len(set1 & set2)
            total = len(set1 | set2)
            if total == 0:
                return 0
            return overlap / total
        
        # 主要处理流程
        for i, row in self.df.iterrows():
            title = row['title']
            
            # 跳过已处理的标题
            if title in processed_titles:
                continue
                
            # 获取关键词
            keywords = title_keywords.get(title, {})
            
            similar_news = []
            platforms_covered = set([row['platform']])
            
            for j, other_row in self.df.iterrows():
                if i == j:
                    continue
                    
                other_title = other_row['title']
                if other_title in processed_titles:
                    continue
                
                # 多重相似度判断
                is_similar = False
                
                # 方法1: 关键词重叠
                other_keywords = title_keywords.get(other_title, {})
                common_keywords = set(keywords.keys()) & set(other_keywords.keys())
                
                # 如果有1个以上关键词重叠且在不同平台
                if len(common_keywords) >= 1 and other_row['platform'] != row['platform']:
                    is_similar = True
                
                # 方法2: 直接文本相似度
                if not is_similar:
                    similarity = text_similarity(row['clean_title'], other_row['clean_title'])
                    # 如果相似度超过阈值且在不同平台
                    if similarity > 0.4 and other_row['platform'] != row['platform']:
                        is_similar = True
                
                # 方法3: 包含关系检查
                if not is_similar:
                    # 如果一个标题完全包含在另一个标题中
                    if (row['clean_title'] in other_row['clean_title'] or 
                        other_row['clean_title'] in row['clean_title']) and other_row['platform'] != row['platform']:
                        is_similar = True
                
                # 如果判定为相似，添加到组中
                if is_similar:
                    similar_news.append(other_row.to_dict())
                    processed_titles.add(other_title)
                    platforms_covered.add(other_row['platform'])
            
            # 如果找到相似新闻，并且至少跨越两个平台
            if similar_news and len(platforms_covered) >= 2:
                group = {
                    'main_news': row.to_dict(),
                    'similar_news': similar_news,
                    'platforms': list(platforms_covered),
                    'total_platforms': len(platforms_covered)
                }
                similar_news_groups.append(group)
                processed_titles.add(title)
        
        # 按平台覆盖数和主新闻热度排序
        sorted_groups = sorted(similar_news_groups, key=lambda x: (x['total_platforms'], x['main_news'].get('heat_score', x['main_news'].get('heat', 0))), reverse=True)
        
        logger.info(f"分析了 {len(sorted_groups)} 组跨平台热点新闻")
        return sorted_groups
    
    def analyze_keywords(self, top_n=20):
        """分析热门关键词"""
        if self.df is None or self.df.empty:
            logger.warning("没有数据可供分析")
            return {}
        
        # 使用TF-IDF提取关键词
        all_titles = ' '.join(self.df['clean_title'])
        keywords = jieba.analyse.extract_tags(all_titles, topK=top_n, withWeight=True)
        
        result = {word: weight for word, weight in keywords}
        logger.info(f"分析了 {len(result)} 个热门关键词")
        return result
    
    def analyze_platform_distribution(self):
        """分析不同平台的热点分布"""
        if self.df is None or self.df.empty:
            logger.warning("没有数据可供分析")
            return {}
        
        platform_counts = self.df['platform'].value_counts().to_dict()
        logger.info(f"分析了 {len(platform_counts)} 个平台的热点分布")
        return platform_counts
    
    def analyze_hourly_trend(self):
        """分析热点新闻的时间趋势"""
        if self.df is None or self.df.empty or 'crawl_time' not in self.df.columns:
            logger.warning("没有时间数据可供分析")
            return {}
        
        # 转换时间字符串为datetime对象
        self.df['datetime'] = pd.to_datetime(self.df['crawl_time'])
        self.df['hour'] = self.df['datetime'].dt.hour
        
        # 按小时统计新闻数量
        hourly_counts = self.df['hour'].value_counts().sort_index().to_dict()
        logger.info(f"分析了 {len(hourly_counts)} 个小时的热点趋势")
        return hourly_counts
    
    def get_comprehensive_analysis(self, top_n=100):
        """获取综合分析结果"""
        if self.df is None or self.df.empty:
            logger.warning("没有数据可供综合分析")
            return {}
        
        # 预处理数据
        self.preprocess_data()
        self.normalize_heat()
        
        result = {
            'top_news': self.get_top_news(top_n),
            'cross_platform_news': self.analyze_cross_platform(),
            'keywords': self.analyze_keywords(),
            'platform_distribution': self.analyze_platform_distribution(),
            'hourly_trend': self.analyze_hourly_trend(),
            'total_news': len(self.df),
            'analysis_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        logger.info("综合分析完成")
        return result

# 测试代码
if __name__ == "__main__":
    import json
    from crawler import HotNewsCrawler
    
    # 爬取数据
    crawler = HotNewsCrawler()
    news_data = crawler.fetch_all_platforms(limit=20)
    
    # 分析数据
    analyzer = HotNewsAnalyzer(news_data)
    analyzer.preprocess_data()
    analyzer.normalize_heat()
    
    # 输出分析结果
    top_news = analyzer.get_top_news(10)
    print(f"Top 10 热点新闻: {json.dumps(top_news, ensure_ascii=False, indent=2)}")
    
    cross_platform = analyzer.analyze_cross_platform()
    print(f"跨平台热点新闻: {len(cross_platform)} 组") 