{"enabled": true, "name": "代码错误检查修复器", "description": "监控Python代码文件的变化，自动检查语法错误、逻辑错误和常见问题，并提供修复建议或自动修复", "version": "1", "when": {"type": "fileEdited", "patterns": ["*.py", "成品代码库/*.py", "全网热点新闻分析软件/*.py", "开源情报搜索助手/*.py"]}, "then": {"type": "askAgent", "prompt": "请检查以下Python代码文件中的错误并提供修复方案：\n\n1. 检查语法错误（缩进、括号匹配、语法结构等）\n2. 检查逻辑错误（变量未定义、函数调用错误、类型错误等）\n3. 检查常见问题（导入错误、编码问题、异常处理等）\n4. 检查代码规范（PEP8标准、命名规范等）\n5. 提供具体的修复建议和改进后的代码\n\n请重点关注：\n- 中文编码问题\n- Excel文件处理相关错误\n- 数据库连接和查询错误\n- GUI界面相关问题\n- 网络请求和爬虫相关错误\n\n如果发现错误，请提供完整的修复代码，并解释修复的原因。"}}