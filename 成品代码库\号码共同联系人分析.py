import os
import pandas as pd

# 获取当前目录下所有的.xls文件
filenames = [f for f in os.listdir('.') if f.endswith('.xls')]

dfs = []

for filename in filenames:
    # 读取Excel文件
    df = pd.read_excel(filename)
    # 获取己方号码（假设文件名就是己方号码）
    own_number = os.path.splitext(filename)[0]
    # 添加己方号码列，确保每行都有正确的己方号码
    df['己方号码'] = own_number
    dfs.append(df)

# 合并所有数据
all_data = pd.concat(dfs, ignore_index=True)

# 找到出现在多个己方号码中的对方号码
contact_counts = all_data.groupby('对方号码')['己方号码'].nunique()
common_contacts = contact_counts[contact_counts > 1].index

# 过滤出共同联系人数据
common_data = all_data[all_data['对方号码'].isin(common_contacts)]

# 创建结果列表
results = []

for contact in common_contacts:
    contact_data = common_data[common_data['对方号码'] == contact].copy()
    # 将呼叫日期和呼叫时间合并为Datetime
    contact_data['呼叫日期时间'] = pd.to_datetime(contact_data['呼叫日期'].astype(str) + ' ' + contact_data['呼叫时间'].astype(str))
    # 获取最早和最晚的呼叫日期时间
    earliest_call = contact_data['呼叫日期时间'].min()
    latest_call = contact_data['呼叫日期时间'].max()
    # 统计每个己方号码的呼叫次数
    own_number_counts = contact_data.groupby('己方号码').size().reset_index(name='通话次数')
    # 总通话次数
    total_calls = contact_data.shape[0]
    # 己方号码的个数
    num_own_numbers = own_number_counts.shape[0]

    # 构建结果字典
    result_entry = {
        '对方号码': contact,
        '最早呼叫时间': earliest_call,
        '最晚呼叫时间': latest_call,
        '己方号码出现的个数': num_own_numbers,
        '总次数': total_calls
    }

    # 添加每个己方号码的通话次数
    for idx, row in own_number_counts.iterrows():
        own_num = row['己方号码']
        call_count = row['通话次数']
        result_entry[f'己方号码 {own_num} 通话次数'] = call_count

    results.append(result_entry)

# 将结果转换为DataFrame
results_df = pd.DataFrame(results)

# 保存为新的Excel文件
results_df.to_excel('共同联系人分析结果.xlsx', index=False)
