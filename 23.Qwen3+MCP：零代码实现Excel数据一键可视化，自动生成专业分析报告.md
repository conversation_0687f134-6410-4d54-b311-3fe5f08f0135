# Qwen3+MCP：零代码实现Excel数据一键可视化，自动生成专业分析报告

## 引言

通过本教程，你将学习如何利用Qwen3模型和MCP工具，实现Excel数据的自动化可视化和专业分析报告生成。即使你是技术小白，也能轻松完成配置，体验AI的强大能力。我们提供了两种实现方式：Cherry Studio和Cursor，你可以根据个人喜好选择任一方式。

## 方案一：使用Cherry Studio实现

### 1. 准备工作

#### 1.1 下载并安装Cherry Studio

1. 访问[Cherry Studio官网](https://cherry-ai.com/)
2. 下载并安装软件

#### 1.2 配置Qwen3模型

1. **登录阿里云百炼**
   - 访问[阿里云百炼平台](https://bailian.console.aliyun.com/console?tab=home#/home)
   - 点击"模型"选项
   - 申请APIKey

2. **在Cherry Studio中配置模型**
   - 点击设置
   - 选择"模型服务-阿里云百炼"
   - 如未自动显示模型，前往[阿里云百炼平台](https://bailian.console.aliyun.com/console?tab=home#/home)
   - 点击"模型广场"，选择Qwen3模型，复制名称
   - 在Cherry Studio中添加模型

### 2. 配置MCP服务

1. **在Cherry Studio中安装工具**
   - 打开Cherry Studio，点击左下角设置图标
   - 找到"MCP服务器选项"，点击安装UV和Bun
   - 如安装过程中遇到问题，可手动将"bun.exe、uv.exe、uvx.exe"文件放入目录：`C:\Users\<USER>\.cherrystudio\bin`
   - 安装完成后重启Cherry Studio确保工具生效

2. **添加MCP服务**

```json
{
  "mcpServers": {
    "sequential-thinking": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    },
    "files": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "D:/aiboshihaihai"
      ]
    },
    "excel": {
      "command": "cmd",
      "args": ["/c", "npx", "--yes", "@negokaz/excel-mcp-server"],
      "env": {
          "EXCEL_MCP_PAGING_CELLS_LIMIT": "4000"
      }
    },
    "quickchart-server": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@gongrzhe/quickchart-mcp-server"
      ]
    }
  }
}
```

> **重要提示**：在`"files"`服务配置中，请将路径修改为你实际希望允许AI访问和操作的本地文件夹路径。例如，如果想让AI操作D盘下的`AI_Work`文件夹，就修改为`"D:/AI_Work"`。请谨慎设置此路径，确保只授予必要的权限。

### 3. 创建智能体

编写以下系统提示词：

```
# 角色

你是一位经验丰富的数据可视化与分析专家。你精通于解读、处理和分析Excel数据，能够根据数据特征和用户目标，智能推荐并生成具有洞察力的可视化图表，并最终以精美、专业的HTML报告形式呈现分析结果。

## 核心能力

### 1. 数据理解与处理
    - **任务规划:** (内部执行) 利用`sequential-thinking` mcp服务规划处理流程。
    - **数据读取:** 精确读取用户提供的Excel文件内容。
    - **数据清洗与准备:** 理解数据结构，处理缺失值、异常值（如果适用），确保数据质量满足分析要求。

### 2. 智能可视化推荐与生成
    - **需求分析:** 根据用户目标（若提供）和数据内容，提取核心分析维度。
    - **图表推荐:** 基于数据类型（如时间序列、分类、比例等）和分析维度，推荐最合适的可视化图表（例如：柱状图、折线图、饼图、散点图、热力图等）。
    - **图表生成:** (内部执行) 调用`QuickChart` mcp服务，根据选定数据和图表类型生成清晰、准确的可视化图表。

### 3. 深度数据分析与洞察提炼
    - **探索性分析:** 对数据进行全面的探索性分析，识别关键模式、趋势、关联性及潜在异常点。
    - **洞察总结:** 提炼数据背后的核心洞见，并以简洁明了的语言进行阐述。
    - **报告撰写:** 生成详细的数据分析文字报告，包含主要发现、数据解读、趋势预测（如果适用）和可行性建议。

### 4. 精美HTML报告构建与输出
    - **内容整合:** 将生成的可视化图表与数据分析文字报告有机整合。
    - **风格设计:** 采用**Apple设计风格**，注重**简洁、清晰**的视觉呈现。使用**卡片式布局**组织内容，确保**充足的留白**和**优雅的排版**。适当运用**高质量图标**增强信息传达。
    - **格式输出:** 生成单一、完整的HTML文件。确保报告**内容丰富、结构清晰、易于阅读**，并具备良好的跨设备**响应式**表现。

## 工作流程
1.  接收并读取用户上传的Excel文件。
2.  进行数据清洗与准备。
3.  执行数据分析，提炼关键洞察。
4.  根据分析结果和数据特点，推荐并生成可视化图表。
5.  撰写数据分析文字报告。
6.  整合图表与文字分析，构建并输出最终的Apple风格HTML报告。
7.  如有必要，可向用户提出澄清性问题以确保分析方向和结果符合用户预期。

## 约束条件
-   **沟通语言:** 必须使用**中文**进行交流和报告撰写。
-   **数据来源:** 分析对象**严格限制**为用户上传的Excel文件。
-   **图表相关性:** 生成的图表必须与数据内容和分析目标紧密相关。
-   **分析客观性:** 分析报告需保持**客观、中立、专业**，基于数据事实。
-   **最终交付:** 最终产出物为**可分享的HTML格式报告、标准markdown格式报告和所有图表文件**。
```

### 4. 生成报告

勾选mcp服务

在聊天框中输入：

```
请对D:\aiboshihaihai\april_2024_sales.xlsx数据进行可视化及分析，将报告保存在D:\aiboshihaihai
```

## 方案二：使用Cursor实现

### 1. 准备工作

#### 1.1 下载并安装Cursor

- 访问[Cursor官网](https://www.cursor.com/)下载客户端
- 按照提示完成安装

#### 1.2 下载并安装Node.js

- 访问[Node.js官网](https://nodejs.org/)下载适合你操作系统的版本
- 按照默认设置完成安装
- **验证安装（可选）**：
  1. 打开终端（Windows上是"命令提示符(cmd)"或"PowerShell"，macOS上是"终端(Terminal)"）
  2. 输入`node -v`并按回车键
  3. 如果终端显示了Node.js的版本号（例如`v20.11.0`），则说明安装成功

### 2. 配置MCP服务

#### 2.1 打开Cursor设置

- 启动Cursor软件
- 找到并打开"首选项(Preferences)" -> "Cursor Settings"

#### 2.2 添加MCP服务器配置

- 在设置界面中，找到**MCP**选项卡
- 点击"**Add a new global MCP server**"
- 如果是首次配置，Cursor会提示你创建一个`mcp.json`文件。点击**创建(Create)**

#### 2.3 编辑`mcp.json`文件

将以下JSON配置复制并粘贴到刚刚创建的`mcp.json`文件中：

```json
{
  "mcpServers": {
    "sequential-thinking": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    },
    "files": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "D:/aiboshihaihai"
      ]
    },
    "excel": {
      "command": "cmd",
      "args": ["/c", "npx", "--yes", "@negokaz/excel-mcp-server"],
      "env": {
          "EXCEL_MCP_PAGING_CELLS_LIMIT": "4000"
      }
    },
    "quickchart-server": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@gongrzhe/quickchart-mcp-server"
      ]
    }
  }
}
```

> **重要提示**：同样，在`"files"`工具配置中，需要将路径修改为你实际希望允许AI访问的本地文件夹路径。请谨慎设置此路径，确保只授予必要的权限。

#### 2.4 验证MCP服务状态

- 回到Cursor的**MCP**设置界面
- 你应该能看到刚才配置的各个MCP服务列表
- 如果服务名称前面显示**绿色对勾✅**，表示该服务已成功加载并准备就绪
- 如果没有显示绿灯或加载失败，请尝试点击旁边的**刷新(Refresh)**按钮

### 3. 使用MCP工具

#### 3.1 切换到Agent模式

- 在Cursor的聊天界面，确保顶部的模式切换到**Agent**模式。只有在Agent模式下，AI才能调用MCP工具

### 4. 生成报告

使用与Cherry Studio相同的提示词（见上文），在聊天框中输入：

```
请对D:\aiboshihaihai\april_2024_sales.xlsx数据进行可视化及分析，将报告保存在D:\aiboshihaihai
```

## 总结

通过这个简单的教程，我们成功配置了一个功能强大的Excel数据可视化与分析智能体。它能够：

1. 自动读取和分析Excel数据
2. 智能生成合适的可视化图表
3. 提炼数据中的关键洞察
4. 生成专业美观的分析报告

无需编写任何代码，你就能拥有一个专业的数据分析助手。快去尝试吧！

希望这篇教程对你有帮助！记得点赞、收藏、关注，我们下次见！

*注：如需获取更多资源，可关注微信公众号：AI博士嗨嗨，回复【数据分析】获取相关代码和笔记。*
