import sys
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QTextEdit, QPushButton, 
                            QComboBox, QTabWidget, QLineEdit, QMessageBox, QFileDialog,
                            QStyleFactory, QTextBrowser)
from PyQt5.QtCore import Qt
import openai  # 修改这里，直接导入 openai
import requests
from urllib.parse import urlparse
import hmac
import base64
import datetime
import hashlib
from bs4 import BeautifulSoup
import time
import jieba
import re
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import os
from PyQt5.QtGui import QIcon

class ConfigDialog(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_config()

    def init_ui(self):
        layout = QVBoxLayout()
        
        # API类型选择
        self.api_type = QComboBox()
        self.api_type.addItems(["通义千问", "讯飞星火", "OpenAI兼容", "Google Gemini"])
        self.api_type.currentIndexChanged.connect(self.on_api_type_changed)
        layout.addWidget(QLabel("API类型："))
        layout.addWidget(self.api_type)
        
        # 通义千问配置
        self.qianwen_group = QWidget()
        qianwen_layout = QVBoxLayout(self.qianwen_group)
        
        self.qianwen_url = QLineEdit()
        self.qianwen_url.setPlaceholderText("输入通义千问接口地址")
        self.qianwen_model = QLineEdit()
        self.qianwen_model.setPlaceholderText("输入通义千问模型名称")
        self.qianwen_key = QLineEdit()
        self.qianwen_key.setPlaceholderText("输入通义千问 API Key")
        
        qianwen_layout.addWidget(QLabel("接口地址："))
        qianwen_layout.addWidget(self.qianwen_url)
        qianwen_layout.addWidget(QLabel("模型名称："))
        qianwen_layout.addWidget(self.qianwen_model)
        qianwen_layout.addWidget(QLabel("API Key："))
        qianwen_layout.addWidget(self.qianwen_key)
        
        # 讯飞星火配置
        self.spark_group = QWidget()
        spark_layout = QVBoxLayout(self.spark_group)
        
        self.spark_url = QLineEdit()
        self.spark_url.setPlaceholderText("输入讯飞星火接口地址")
        self.spark_url.setText("https://spark-api-open.xf-yun.com/v1/chat/completions")
        self.spark_model = QLineEdit()
        self.spark_model.setPlaceholderText("输入讯飞星火模型名称（如：spark-v3）")
        self.spark_key = QLineEdit()
        self.spark_key.setPlaceholderText("输入讯飞星火 API Key")
        self.spark_secret = QLineEdit()
        self.spark_secret.setPlaceholderText("输入讯飞星火 API Secret")
        
        spark_layout.addWidget(QLabel("接口地址："))
        spark_layout.addWidget(self.spark_url)
        spark_layout.addWidget(QLabel("模型名称："))
        spark_layout.addWidget(self.spark_model)
        spark_layout.addWidget(QLabel("API Key："))
        spark_layout.addWidget(self.spark_key)
        spark_layout.addWidget(QLabel("API Secret："))
        spark_layout.addWidget(self.spark_secret)
        
        # OpenAI兼容接口配置
        self.openai_group = QWidget()
        openai_layout = QVBoxLayout(self.openai_group)
        
        self.openai_url = QLineEdit()
        self.openai_url.setPlaceholderText("输入OpenAI兼容接口地址")
        self.openai_model = QLineEdit()
        self.openai_model.setPlaceholderText("输入模型名称")
        self.openai_key = QLineEdit()
        self.openai_key.setPlaceholderText("输入API Key")
        
        openai_layout.addWidget(QLabel("接口地址："))
        openai_layout.addWidget(self.openai_url)
        openai_layout.addWidget(QLabel("模型名称："))
        openai_layout.addWidget(self.openai_model)
        openai_layout.addWidget(QLabel("API Key："))
        openai_layout.addWidget(self.openai_key)

        # Google Gemini配置
        self.gemini_group = QWidget()
        gemini_layout = QVBoxLayout(self.gemini_group)
        
        self.gemini_url = QLineEdit()
        self.gemini_url.setPlaceholderText("输入Gemini接口地址")
        self.gemini_url.setText("https://generativelanguage.googleapis.com/v1beta/models")
        self.gemini_model = QLineEdit()
        self.gemini_model.setPlaceholderText("输入模型名称（如：gemini-pro）")
        self.gemini_model.setText("gemini-pro")
        self.gemini_key = QLineEdit()
        self.gemini_key.setPlaceholderText("输入API Key")
        
        gemini_layout.addWidget(QLabel("接口地址："))
        gemini_layout.addWidget(self.gemini_url)
        gemini_layout.addWidget(QLabel("模型名称："))
        gemini_layout.addWidget(self.gemini_model)
        gemini_layout.addWidget(QLabel("API Key："))
        gemini_layout.addWidget(self.gemini_key)
        
        # 添加代理设置组
        proxy_group = QWidget()
        proxy_layout = QVBoxLayout(proxy_group)
        
        # 代理开关
        self.proxy_enabled = QComboBox()
        self.proxy_enabled.addItems(["禁用代理", "启用代理"])
        proxy_layout.addWidget(QLabel("代理设置："))
        proxy_layout.addWidget(self.proxy_enabled)
        
        # 代理主机
        self.proxy_host = QLineEdit()
        self.proxy_host.setPlaceholderText("代理主机地址（如：127.0.0.1）")
        proxy_layout.addWidget(QLabel("代理主机："))
        proxy_layout.addWidget(self.proxy_host)
        
        # 代理端口
        self.proxy_port = QLineEdit()
        self.proxy_port.setPlaceholderText("代理端口（如：10808）")
        proxy_layout.addWidget(QLabel("代理端口："))
        proxy_layout.addWidget(self.proxy_port)
        
        # 测试代理按钮
        test_proxy_btn = QPushButton("测试代理连接")
        test_proxy_btn.clicked.connect(self.test_proxy)
        proxy_layout.addWidget(test_proxy_btn)
        
        # 将代理设置添加到主布局
        layout.addWidget(proxy_group)
        
        # 添加所有配置组
        layout.addWidget(self.qianwen_group)
        layout.addWidget(self.spark_group)
        layout.addWidget(self.openai_group)
        layout.addWidget(self.gemini_group)
        
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)
        
        self.setLayout(layout)
        self.on_api_type_changed(0)  # 初始化界面

    def on_api_type_changed(self, index):
        """根据选择的API类型显示对应的配置界面"""
        self.qianwen_group.setVisible(index == 0)  # 通义千问
        self.spark_group.setVisible(index == 1)    # 讯飞星火
        self.openai_group.setVisible(index == 2)   # OpenAI兼容
        self.gemini_group.setVisible(index == 3)   # Google Gemini

    def load_config(self):
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
                self.api_type.setCurrentIndex(config.get('api_type', 0))
                
                # 通义千问配置
                self.qianwen_url.setText(config.get('qianwen_url', ''))
                self.qianwen_model.setText(config.get('qianwen_model', ''))
                self.qianwen_key.setText(config.get('qianwen_key', ''))
                
                # 讯飞星火配置
                self.spark_url.setText(config.get('spark_url', 'https://spark-api-open.xf-yun.com/v1/chat/completions'))
                self.spark_model.setText(config.get('spark_model', ''))
                self.spark_key.setText(config.get('spark_key', ''))
                self.spark_secret.setText(config.get('spark_secret', ''))
                
                # OpenAI兼容配置
                self.openai_url.setText(config.get('openai_url', ''))
                self.openai_model.setText(config.get('openai_model', ''))
                self.openai_key.setText(config.get('openai_key', ''))

                # Google Gemini配置
                self.gemini_url.setText(config.get('gemini_url', 'https://generativelanguage.googleapis.com/v1beta/models'))
                self.gemini_model.setText(config.get('gemini_model', 'gemini-pro'))
                self.gemini_key.setText(config.get('gemini_key', ''))
                
                # 加载代理设置
                self.proxy_enabled.setCurrentIndex(config.get('proxy_enabled', 0))
                self.proxy_host.setText(config.get('proxy_host', '127.0.0.1'))
                self.proxy_port.setText(config.get('proxy_port', '10808'))
        except FileNotFoundError:
            pass

    def save_config(self):
        config = {
            'api_type': self.api_type.currentIndex(),
            
            # 通义千问配置
            'qianwen_url': self.qianwen_url.text(),
            'qianwen_model': self.qianwen_model.text(),
            'qianwen_key': self.qianwen_key.text(),
            
            # 讯飞星火配置
            'spark_url': self.spark_url.text(),
            'spark_model': self.spark_model.text(),
            'spark_key': self.spark_key.text(),
            'spark_secret': self.spark_secret.text(),
            
            # OpenAI兼容配置
            'openai_url': self.openai_url.text(),
            'openai_model': self.openai_model.text(),
            'openai_key': self.openai_key.text(),

            # Google Gemini配置
            'gemini_url': self.gemini_url.text(),
            'gemini_model': self.gemini_model.text(),
            'gemini_key': self.gemini_key.text(),
            
            # 保存代理设置
            'proxy_enabled': self.proxy_enabled.currentIndex(),
            'proxy_host': self.proxy_host.text(),
            'proxy_port': self.proxy_port.text(),
        }
        with open('config.json', 'w') as f:
            json.dump(config, f)
        
        # 更新代理环境变量
        self.update_proxy_settings()
        QMessageBox.information(self, "提示", "配置已保存")

    def test_proxy(self):
        """测试代理连接"""
        try:
            if self.proxy_enabled.currentIndex() == 0:
                QMessageBox.information(self, "提示", "代理当前已禁用")
                return
                
            host = self.proxy_host.text()
            port = self.proxy_port.text()
            
            if not host or not port:
                QMessageBox.warning(self, "警告", "请输入代理主机和端口")
                return
                
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)  # 设置超时时间为2秒
            
            result = sock.connect_ex((host, int(port)))
            sock.close()
            
            if result == 0:
                QMessageBox.information(self, "提示", "代理连接测试成功")
            else:
                QMessageBox.warning(self, "警告", "代理连接测试失败")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"测试代理时出错：{str(e)}")

    def update_proxy_settings(self):
        """更新代理环境变量设置"""
        if self.proxy_enabled.currentIndex() == 1:  # 启用代理
            host = self.proxy_host.text()
            port = self.proxy_port.text()
            if host and port:
                os.environ['HTTP_PROXY'] = f'http://{host}:{port}'
                os.environ['HTTPS_PROXY'] = f'http://{host}:{port}'
        else:  # 禁用代理
            os.environ.pop('HTTP_PROXY', None)
            os.environ.pop('HTTPS_PROXY', None)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置Windows 7兼容的样式
        self.setStyle(QStyleFactory.create('Fusion'))
        self.hot_events = []
        self.init_ui()
        self.load_config()

    def init_ui(self):
        self.setWindowTitle('微情报生成助手')
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置窗口图标
        icon_path = '8.ico'
        try:
            if hasattr(sys, '_MEIPASS'):  # 如果是打包后的exe
                icon_path = os.path.join(sys._MEIPASS, '8.ico')
            elif not os.path.exists(icon_path):
                # 尝试在不同位置查找图标
                possible_paths = ['1.ico', './icons/8.ico', './resources/8.ico']
                for path in possible_paths:
                    if os.path.exists(path):
                        icon_path = path
                        break
            self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"加载图标失败: {str(e)}")
            # 图标加载失败不影响程序运行
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 顶部布局（只保留配置按钮）
        top_layout = QHBoxLayout()
        
        # 配置按钮
        config_btn = QPushButton("配置")
        config_btn.clicked.connect(self.show_config)
        top_layout.addStretch()  # 添加弹性空间，使配置按钮靠右
        top_layout.addWidget(config_btn)
        layout.addLayout(top_layout)

        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 热点事件页面改造
        self.event_tab = QWidget()
        event_layout = QVBoxLayout(self.event_tab)
        
        # 添加热点事件搜集和选择功能
        event_top_layout = QHBoxLayout()
        self.collect_events_btn = QPushButton("⟳ 搜集热点事件（约需1分钟，请耐心等待）")
        self.collect_events_btn.clicked.connect(self.collect_hot_events)
        self.collect_events_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 250px;
                font-weight: bold;
                margin: 10px 5px;
            }
            QPushButton:hover {
                background-color: #fb8c00;
            }
            QPushButton:pressed {
                background-color: #f57c00;
            }
        """)
        event_top_layout.addWidget(self.collect_events_btn)
        event_layout.addLayout(event_top_layout)
        
        # 热点事件列表
        self.events_list = QComboBox()
        self.events_list.setMinimumWidth(600)
        self.events_list.currentIndexChanged.connect(self.on_event_selected)
        event_layout.addWidget(QLabel("选择热点事件："))
        event_layout.addWidget(self.events_list)
        
        # 事件详情显示
        self.event_edit = QTextBrowser()
        self.event_edit.setOpenExternalLinks(True)  # 启用外部链接打开功能
        event_layout.addWidget(QLabel("事件详情："))
        event_layout.addWidget(self.event_edit)
        
        # 添加内容输入框
        self.content_edit = QTextEdit()
        event_layout.addWidget(QLabel("内容编辑区域（用于分析和润色）："))
        event_layout.addWidget(self.content_edit)
        
        # 添加复制按钮
        copy_btn = QPushButton("复制详情到编辑区")
        copy_btn.clicked.connect(self.copy_to_content)
        event_layout.addWidget(copy_btn)
        
        # 在标签页上方添加字数设置
        summary_layout = QHBoxLayout()
        self.summary_length = QLineEdit()
        self.summary_length.setPlaceholderText("0-1000")
        self.summary_length.setText("200")  # 默认200字
        self.summary_length.setMaximumWidth(100)
        self.summary_length.textChanged.connect(self.validate_summary_length)
        
        summary_layout.addWidget(QLabel("精简字数设置："))
        summary_layout.addWidget(self.summary_length)
        summary_layout.addWidget(QLabel("字"))
        summary_layout.addStretch()
        event_layout.addLayout(summary_layout)

        # 分析页面改造
        self.analysis_tab = QWidget()
        analysis_layout = QVBoxLayout(self.analysis_tab)
        
        # 添加分析模块的字数设置
        analysis_summary_layout = QHBoxLayout()
        self.analysis_summary_length = QLineEdit()
        self.analysis_summary_length.setPlaceholderText("0-1000")
        self.analysis_summary_length.setText("200")
        self.analysis_summary_length.setMaximumWidth(100)
        self.analysis_summary_length.textChanged.connect(self.validate_summary_length)
        
        analysis_summary_layout.addWidget(QLabel("分析精简字数："))
        analysis_summary_layout.addWidget(self.analysis_summary_length)
        analysis_summary_layout.addWidget(QLabel("字"))
        analysis_summary_layout.addStretch()
        analysis_layout.addLayout(analysis_summary_layout)
        
        # AI生成的分析结果显示区（上半部分）
        self.analysis_result = QTextBrowser()
        self.analysis_result.setOpenExternalLinks(True)
        analysis_layout.addWidget(QLabel("分析内容生成（用于编辑和润色）："))
        analysis_layout.addWidget(self.analysis_result)
        
        # 内容编辑区（下半部分）
        self.analysis_edit = QTextEdit()
        analysis_layout.addWidget(QLabel("内容编辑区域："))
        analysis_layout.addWidget(self.analysis_edit)
        
        # 添加复制按钮
        analysis_copy_btn = QPushButton("复制分析结果到编辑区")
        analysis_copy_btn.clicked.connect(self.copy_analysis_to_content)
        analysis_layout.addWidget(analysis_copy_btn)
        
        # 建议页面改造
        self.suggestion_tab = QWidget()
        suggestion_layout = QVBoxLayout(self.suggestion_tab)
        
        # 添加建议模块的字数设置
        suggestion_summary_layout = QHBoxLayout()
        self.suggestion_summary_length = QLineEdit()
        self.suggestion_summary_length.setPlaceholderText("0-1000")
        self.suggestion_summary_length.setText("200")  # 默认200字
        self.suggestion_summary_length.setMaximumWidth(100)
        self.suggestion_summary_length.textChanged.connect(self.validate_summary_length)
        
        suggestion_summary_layout.addWidget(QLabel("建议精简字数："))
        suggestion_summary_layout.addWidget(self.suggestion_summary_length)
        suggestion_summary_layout.addWidget(QLabel("字"))
        suggestion_summary_layout.addStretch()
        suggestion_layout.addLayout(suggestion_summary_layout)
        
        # 建议内容编辑区
        self.suggestion_content_edit = QTextBrowser()
        self.suggestion_content_edit.setOpenExternalLinks(True)
        suggestion_layout.addWidget(QLabel("建议内容生成（用于编辑和润色）："))
        suggestion_layout.addWidget(self.suggestion_content_edit)
        
        # 内容编辑区域显示
        self.suggestion_edit = QTextEdit()
        suggestion_layout.addWidget(QLabel("内容编辑区域："))
        suggestion_layout.addWidget(self.suggestion_edit)
        
        # 添加复制按钮
        suggestion_copy_btn = QPushButton("复制建议结果到编辑区")
        suggestion_copy_btn.clicked.connect(self.copy_suggestion_to_content)
        suggestion_layout.addWidget(suggestion_copy_btn)

        # 添加微情报生成标签页
        self.report_tab = QWidget()
        report_layout = QVBoxLayout(self.report_tab)
        
        # 添加生成按钮
        generate_report_btn = QPushButton("✓ 生成完整微情报")
        generate_report_btn.clicked.connect(self.generate_full_report)
        generate_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 180px;
                font-weight: bold;
                margin: 10px 5px;
            }
            QPushButton:hover {
                background-color: #e53935;
            }
            QPushButton:pressed {
                background-color: #d32f2f;
            }
        """)
        report_layout.addWidget(generate_report_btn)
        
        # 微情报显示区域
        self.report_edit = QTextBrowser()
        self.report_edit.setOpenExternalLinks(True)
        report_layout.addWidget(QLabel("微情报内容："))
        report_layout.addWidget(self.report_edit)
        
        # 添加导出按钮
        export_btn = QPushButton("⤓ 导出微情报")
        export_btn.clicked.connect(self.export_report)
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #9c27b0;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 150px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #8e24aa;
            }
            QPushButton:pressed {
                background-color: #7b1fa2;
            }
        """)
        report_layout.addWidget(export_btn)
        
        # 将微情报标签页添加到标签页组件中
        self.tab_widget.addTab(self.event_tab, "热点事件")
        self.tab_widget.addTab(self.analysis_tab, "分析")
        self.tab_widget.addTab(self.suggestion_tab, "建议")
        self.tab_widget.addTab(self.report_tab, "微情报生成")
        layout.addWidget(self.tab_widget)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.generate_btn = QPushButton("▶ 生成内容（约需20秒）")
        self.generate_btn.clicked.connect(self.generate_content)
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 180px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
        """)

        self.polish_btn = QPushButton("★ 润色内容（约需20秒）")
        self.polish_btn.clicked.connect(self.polish_content)
        self.polish_btn.setStyleSheet("""
            QPushButton {
                background-color: #43a047;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 180px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #388e3c;
            }
            QPushButton:pressed {
                background-color: #2e7d32;
            }
        """)
        button_layout.addWidget(self.generate_btn)
        button_layout.addWidget(self.polish_btn)
        layout.addLayout(button_layout)

        # 在底部添加版权信息标签
        copyright_label = QLabel("版权所有：台州市公安局 解晟")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                font-family: "Microsoft YaHei";
                color: #555555;
                padding: 12px;
                border-top: 1px solid #dddddd;
                background-color: #f0f0f0;
                font-size: 14px;
                font-weight: normal;
                border-radius: 0 0 4px 4px;
            }
        """)
        layout.addWidget(copyright_label)

        # 设置整体窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f7f7f7;
            }
            QWidget {
                font-family: "Microsoft YaHei";
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 6px;
                selection-background-color: #b5d5ff;
            }
            QPushButton {
                background-color: #4a86e8;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                min-width: 80px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
            QComboBox {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 4px;
                min-height: 24px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #d0d0d0;
            }
            QComboBox:hover {
                border: 1px solid #a0a0a0;
            }
            QLineEdit {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 4px;
                selection-background-color: #b5d5ff;
            }
            QLineEdit:hover {
                border: 1px solid #a0a0a0;
            }
            QLabel {
                font-family: "Microsoft YaHei";
                color: #333333;
                padding: 2px;
            }
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 2px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e1e1e1;
                color: #505050;
                padding: 8px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
            }
            QTabBar::tab:hover:!selected {
                background-color: #d0d0d0;
            }
        """)

        # 修改字体设置，确保在Windows 7上正常显示
        font = self.font()
        font.setFamily('Microsoft YaHei')  # 使用微软雅黑字体
        self.setFont(font)

    def load_config(self):
        try:
            with open('config.json', 'r') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            self.config = {}

    def show_config(self):
        self.config_dialog = ConfigDialog()
        self.config_dialog.show()

    def call_ai_api(self, prompt):
        try:
            self.load_config()
            api_type = self.config.get('api_type', 0)
            
            if api_type == 0:  # 通义千问
                return self._call_qianwen_api(prompt)
            elif api_type == 1:  # 讯飞星火
                return self._call_spark_api(prompt)
            elif api_type == 2:  # OpenAI兼容
                return self._call_openai_api(prompt)
            elif api_type == 3:  # Google Gemini
                return self._call_gemini_api(prompt)
            else:
                raise Exception("未知的API类型")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"AI调用失败：{str(e)}")
            return None

    def _call_spark_api(self, prompt):
        try:
            api_base = self.config.get('spark_url', '').rstrip('/')
            api_key = self.config.get('spark_key', '')
            api_secret = self.config.get('spark_secret', '')
            model = self.config.get('spark_model', '')
            
            parsed_url = urlparse(api_base)
            host = parsed_url.netloc or 'spark-api-open.xf-yun.com'
            
            date = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            signature_origin = f"host: {host}\ndate: {date}\nPOST /v1/chat/completions HTTP/1.1"
            
            signature_sha = hmac.new(
                api_secret.encode('utf-8'),
                signature_origin.encode('utf-8'),
                digestmod=hashlib.sha256
            ).digest()
            
            signature_sha_base64 = base64.b64encode(signature_sha).decode('utf-8')
            authorization = f'hmac username="{api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha_base64}"'

            headers = {
                'Authorization': authorization,
                'Content-Type': 'application/json',
                'Host': host,
                'Date': date,
                'Accept': 'application/json'
            }
            
            payload = {
                'model': model,
                'messages': [{'role': 'user', 'content': prompt}],
                'stream': False,
                'temperature': 0.7,
                'max_tokens': 1024
            }
            
            url = f"{api_base}" if api_base.endswith('/v1/chat/completions') else f"{api_base}/v1/chat/completions"
            
            # 添加重试机制
            max_retries = 3
            retry_count = 0
            while retry_count < max_retries:
                try:
                    # 增加超时时间
                    response = requests.post(
                        url,
                        headers=headers,
                        json=payload,
                        verify=True,
                        timeout=60,  # 增加60秒
                        # 添加代理设置（如果需要）
                        # proxies={
                        #     'http': 'http://your-proxy:port',
                        #     'https': 'https://your-proxy:port'
                        # }
                    )
                    
                    if response.status_code == 200:
                        response_data = response.json()
                        try:
                            return response_data['choices'][0]['message']['content']
                        except KeyError:
                            if 'data' in response_data:
                                return response_data['data'].get('text', '')
                            raise Exception("响应格式解析失败，请检查响应数据结构")
                    else:
                        error_msg = f"讯飞星火API调用失败: HTTP {response.status_code}"
                        try:
                            error_detail = response.json()
                            error_msg += f" - {error_detail.get('message', response.text)}"
                        except:
                            error_msg += f" - {response.text}"
                        raise Exception(error_msg)
                        
                except requests.exceptions.Timeout:
                    retry_count += 1
                    if retry_count == max_retries:
                        raise Exception(f"连接超时，已重试{max_retries}次")
                    print(f"连接超时，正在进行第{retry_count}次重试...")
                    time.sleep(2)  # 重试前等待2秒
                    
                except requests.exceptions.RequestException as e:
                    retry_count += 1
                    if retry_count == max_retries:
                        raise Exception(f"网络请求错误：{str(e)}，已重试{max_retries}次")
                    print(f"网络请求错误，正在进行第{retry_count}次重试...")
                    time.sleep(2)  # 重试前等待2秒
                    
        except Exception as e:
            raise Exception(f"讯飞星火API调用失败：{str(e)}")

    def _call_qianwen_api(self, prompt):
        """调用通义千问接口"""
        try:
            api_base = self.config.get('qianwen_url', '').rstrip('/')
            api_key = self.config.get('qianwen_key', '')
            model = self.config.get('qianwen_model', '')
            
            if not api_base:
                raise Exception("请配置通义千问接口地址")
            if not api_key:
                raise Exception("请配置通义千问 API Key")
            if not model:
                raise Exception("请配置通义千问模型名称")

            if not api_base.endswith('/v1/chat/completions'):
                api_base = f"{api_base}/v1/chat/completions"

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {api_key}'
            }

            payload = {
                'model': model,
                'messages': [{'role': 'user', 'content': prompt}],
                'temperature': 0.7,
                'max_tokens': 2048
            }

            response = requests.post(
                api_base,
                headers=headers,
                json=payload,
                verify=True,
                timeout=60
            )

            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
            else:
                error_msg = f"通义千问 API调用失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', response.text)}"
                except:
                    error_msg += f" - {response.text}"
                raise Exception(error_msg)

        except Exception as e:
            raise Exception(f"通义千问 API调用失败：{str(e)}")

    def _call_openai_api(self, prompt):
        try:
            api_base = self.config.get('openai_url', '').rstrip('/')
            api_key = self.config.get('openai_key', '')
            model = self.config.get('openai_model', '')
            
            if not api_base:
                raise Exception("请配置OpenAI兼容接口地址")
            if not api_key:
                raise Exception("请配置OpenAI API Key")
            if not model:
                raise Exception("请配置OpenAI模型名称")

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {api_key}'
            }

            payload = {
                'model': model,
                'messages': [{'role': 'user', 'content': prompt}],
                'temperature': 0.7,
                'max_tokens': 2048
            }

            response = requests.post(
                api_base,
                headers=headers,
                json=payload,
                verify=True,
                timeout=180
            )

            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
            else:
                error_msg = f"OpenAI API调用失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', response.text)}"
                except:
                    error_msg += f" - {response.text}"
                raise Exception(error_msg)

        except Exception as e:
            raise Exception(f"OpenAI API调用失败：{str(e)}")

    def _call_gemini_api(self, prompt):
        try:
            api_base = self.config.get('gemini_url', '').rstrip('/')
            api_key = self.config.get('gemini_key', '')
            model = self.config.get('gemini_model', 'gemini-pro')
            
            if not api_base:
                raise Exception("请配置Gemini接口地址")
            if not api_key:
                raise Exception("请配置Gemini API Key")
            if not model:
                raise Exception("请配置Gemini模型名称")

            # 构建完整的API URL
            url = f"{api_base}/{model}:generateContent?key={api_key}"

            headers = {
                'Content-Type': 'application/json'
            }

            payload = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 2048,
                }
            }

            response = requests.post(
                url,
                headers=headers,
                json=payload,
                verify=True,
                timeout=60
            )

            if response.status_code == 200:
                response_data = response.json()
                try:
                    # 提取生成的文本内容
                    candidates = response_data.get('candidates', [])
                    if candidates and len(candidates) > 0:
                        content = candidates[0].get('content', {})
                        parts = content.get('parts', [])
                        if parts and len(parts) > 0:
                            return parts[0].get('text', '')
                    raise Exception("响应中未找到生成的文本内容")
                except Exception as e:
                    print(f"解析Gemini响应时出错：{str(e)}")
                    print(f"完整响应：{response_data}")
                    raise Exception("解析响应数据失败")
            else:
                error_msg = f"Gemini API调用失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', response.text)}"
                except:
                    error_msg += f" - {response.text}"
                raise Exception(error_msg)

        except Exception as e:
            raise Exception(f"Gemini API调用失败：{str(e)}")

    def _calculate_similarity(self, text1, text2):
        """计算两个文本的相似度"""
        try:
            # 对文本进行分词和清理
            def clean_text(text):
                # 移除URL、特殊字符等
                text = re.sub(r'http\S+|www.\S+', '', text)
                text = re.sub(r'[^\w\s]', '', text)
                # 分词
                words = jieba.lcut(text)
                return set(words)
            
            # 获取两个文本的词集合
            words1 = clean_text(text1)
            words2 = clean_text(text2)
            
            # 计算Jaccard相似度
            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))
            
            return intersection / union if union > 0 else 0
            
        except Exception as e:
            print(f"计算相似度时出错：{str(e)}")
            return 0

    def _group_similar_events(self, events):
        """对事件进行分组和排序"""
        try:
            # 存储事件组
            event_groups = []
            processed_events = set()
            
            # 遍历所事件
            for i, event1 in enumerate(events):
                if i in processed_events:
                    continue
                    
                # 创建新的事件组
                current_group = {
                    'events': [event1],
                    'sources': {event1['source']},
                    'total_hot_index': self._normalize_hot_index(event1['hot_index'])
                }
                processed_events.add(i)
                
                # 查找相似事件
                for j, event2 in enumerate(events[i+1:], i+1):
                    if j in processed_events:
                        continue
                        
                    # 计算标题相似度
                    title_similarity = self._calculate_similarity(event1['title'], event2['title'])
                    # 计算内容相似度
                    content_similarity = self._calculate_similarity(event1['content'], event2['content'])
                    
                    # 如果标题或内容相似度超过阈值，认为是相似事件
                    if title_similarity > 0.3 or content_similarity > 0.2:
                        current_group['events'].append(event2)
                        current_group['sources'].add(event2['source'])
                        current_group['total_hot_index'] += self._normalize_hot_index(event2['hot_index'])
                        processed_events.add(j)
                
                event_groups.append(current_group)
            
            # 对事件组进行排序
            # 1. 首先按照来源数量排序（多个来源的优先）
            # 2. 然后按照总热度排序
            sorted_groups = sorted(event_groups, 
                                 key=lambda x: (len(x['sources']), x['total_hot_index']), 
                                 reverse=True)
            
            # 展平排序后的事件组
            sorted_events = []
            for group in sorted_groups:
                # 对组内事件按照热度排序
                group_events = sorted(group['events'], 
                                    key=lambda x: self._normalize_hot_index(x['hot_index']), 
                                    reverse=True)
                
                # 处理事件标题
                if len(group['sources']) > 1:
                    # 多源事件，使用所有来源
                    sources_str = '、'.join(sorted(group['sources']))
                    for event in group_events:
                        event['title'] = f"[{sources_str}] {event['title']}"
                else:
                    # 单源事件，使用单个来源
                    source = next(iter(group['sources']))  # 获取唯一的来源
                    for event in group_events:
                        event['title'] = f"[{source}] {event['title']}"
                
                sorted_events.extend(group_events)
            
            return sorted_events
            
        except Exception as e:
            print(f"分组事件时出错：{str(e)}")
            return events

    def _normalize_hot_index(self, hot_index):
        """统一热度值的格式并转换为数值"""
        try:
            # 移除非数字字符
            hot_str = ''.join(filter(str.isdigit, str(hot_index)))
            return float(hot_str) if hot_str else 0
        except:
            return 0

    def collect_hot_events(self):
        """搜集热点事件"""
        try:
            # 获取各平台热搜
            baidu_events = self._get_baidu_hot()
            weibo_events = self._get_weibo_hot()
            douyin_events = self._get_douyin_hot()
            
            # 合并所有来源的事件
            all_events = baidu_events + weibo_events + douyin_events
            
            # 对事件进行分组和排序
            sorted_events = self._group_similar_events(all_events)
            
            # 更新事件列表
            self.hot_events = sorted_events
            self.events_list.clear()
            
            for event in self.hot_events:
                # 标题已经在_group_similar_events中被修改，包含了多源标记
                self.events_list.addItem(event['title'], event)
            
            # 显示统计信息
            total_events = len(self.hot_events)
            multi_source_events = sum(1 for event in self.hot_events if '[' in event['title'] and '、' in event['title'])
            
            QMessageBox.information(
                self, 
                "提示", 
                f"已搜集到 {total_events} 个热点事件\n"
                f"其中 {multi_source_events} 个事件出现在多个平台"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取热点事件失败：{str(e)}")

    def _create_session(self):
        """创建一个置好的请求会话"""
        session = requests.Session()
        session.verify = False  # 禁用SSL验证
        
        # 设置代理（如果环境变量中有代理设置）
        proxies = {
            'http': os.environ.get('HTTP_PROXY') or os.environ.get('http_proxy'),
            'https': os.environ.get('HTTPS_PROXY') or os.environ.get('https_proxy')
        }
        
        if proxies['http'] or proxies['https']:
            session.proxies = {k: v for k, v in proxies.items() if v}
            # 设置代理时需要的额外选项
            session.trust_env = False
            if hasattr(session.adapters['https://'], '_pool_connections'):
                session.adapters['https://']._pool_connections = 1
                session.adapters['https://']._pool_maxsize = 1
        
        return session

    def _get_weibo_hot(self):
        """获取微博热搜"""
        events = []
        try:
            # 在Windows 7上使用更保守的SSL设置
            requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS = 'ALL:@SECLEVEL=1'
            
            # 创建配置好的会话
            session = self._create_session()
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Cookie': 'SUB=_2AkMW'
            }
            
            # 使用session进行请求
            response = session.get(
                'https://weibo.com/ajax/side/hotSearch',
                headers=headers,
                timeout=30
            )
            
            # 微博热搜榜API
            data = response.json()
            
            if 'data' in data and 'realtime' in data['data']:
                hot_items = data['data']['realtime']
                
                for item in hot_items:
                    try:
                        title = item['note']
                        hot_index = item.get('num', '0')  # 热度值
                        category = item.get('category', '')  # 分类
                        link = f"https://s.weibo.com/weibo?q={requests.utils.quote(title)}"
                        
                        # 获取话题详情
                        detail_response = requests.get(link, headers=headers)
                        detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
                        
                        # 尝试获取话题内容
                        content_elements = detail_soup.find_all('div', class_='card-feed')
                        content = []
                        for element in content_elements[:3]:  # 获取前3条相关微博
                            text = element.get_text().strip()
                            if text:
                                content.append(text)
                        
                        event = {
                            'title': title,
                            'content': '\n'.join(content) if content else f'微博热搜：{title}',
                            'hot_index': f'{hot_index}热度',
                            'category': category,
                            'source': '微博热搜',
                            'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'link': link
                        }
                        events.append(event)
                    except Exception as e:
                        print(f"处理微博热搜项目时出错：{str(e)}")
                        continue
                
        except Exception as e:
            print(f"获取微博热搜失败：{str(e)}")
            
        return events

    def _get_baidu_hot(self):
        """获取百度热搜"""
        events = []
        try:
            # 在Windows 7上使用更保守的SSL设置
            requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS = 'ALL:@SECLEVEL=1'
            
            # 创建配置好的会话
            session = self._create_session()
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 使用session进行请求
            response = session.get(
                'https://top.baidu.com/board?tab=realtime',
                headers=headers,
                timeout=30
            )
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 解析百度热搜榜
            hot_items = soup.find_all('div', class_='category-wrap_iQLoo')
            
            for item in hot_items:
                try:
                    title = item.find('div', class_='c-single-text-ellipsis').text.strip()
                    content = item.find('div', class_='hot-desc_1m_jR').text.strip()
                    hot_index = item.find('div', class_='hot-index_1Bl1a').text.strip()
                    
                    # 获取详细内容
                    search_url = f"https://www.baidu.com/s?wd={requests.utils.quote(title)}"
                    detail_response = requests.get(search_url, headers=headers)
                    detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
                    
                    # 获取搜索结果中的新闻内容
                    news_contents = []
                    news_items = detail_soup.find_all('div', class_='c-container')[:3]
                    
                    for news_item in news_items:
                        text = news_item.get_text()
                        if text:
                            news_contents.append(text.strip())
                    
                    event = {
                        'title': title,
                        'content': content + '\n\n' + '\n'.join(news_contents) if news_contents else content,
                        'hot_index': hot_index,
                        'source': '百度热搜',
                        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'link': search_url
                    }
                    events.append(event)
                except Exception as e:
                    print(f"处理百度热项目时出错：{str(e)}")
                    continue
                    
        except Exception as e:
            print(f"获取百度热搜失败：{str(e)}")
            
        return events

    def _get_douyin_hot(self):
        """获取抖音热榜"""
        events = []
        try:
            # 在Windows 7上使用更保守的SSL设置
            requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS = 'ALL:@SECLEVEL=1'
            
            # 创建配置好的会话
            session = self._create_session()
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Referer': 'https://www.douyin.com/',
                'Origin': 'https://www.douyin.com'
            }
            
            # 使用session进行请求
            response = session.get(
                'https://www.douyin.com/aweme/v1/web/hot/search/list/',
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    hot_list = data.get('data', {}).get('word_list', [])
                    
                    for item in hot_list:
                        try:
                            title = item.get('word', '')
                            hot_value = item.get('hot_value', 0)
                            
                            # 构建搜索链接
                            search_url = f"https://www.douyin.com/search/{requests.utils.quote(title)}"
                            
                            # 获取相关视频内容
                            video_api_url = f'https://www.douyin.com/aweme/v1/web/search/item/?keyword={requests.utils.quote(title)}&count=3'
                            video_response = requests.get(video_api_url, headers=headers)
                            video_data = video_response.json()
                            
                            content = []
                            if 'data' in video_data:
                                for video in video_data['data'][:3]:
                                    desc = video.get('desc', '')
                                    if desc:
                                        content.append(desc)
                            
                            event = {
                                'title': title,
                                'content': '\n'.join(content) if content else f'抖音热榜：{title}',
                                'hot_index': f'{hot_value:,} 热度',
                                'source': '抖音热榜',
                                'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'link': search_url
                            }
                            events.append(event)
                            
                            # 添加延时，避免请求过快
                            time.sleep(1)
                            
                        except Exception as e:
                            print(f"处理抖音热榜项目时出错：{str(e)}")
                            continue
                            
                except Exception as e:
                    print(f"解析抖音热榜数据失败：{str(e)}")
            else:
                print(f"抖音API请求失败，状态码：{response.status_code}")
                
            # 如果通过API获取失败，尝试使用备用数据源
            if not events:
                # 使用第三方数据源API
                backup_api_url = "https://api.oioweb.cn/api/common/HotList"
                response = requests.get(backup_api_url)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('code') == 200:
                            douyin_list = data.get('result', {}).get('DouYinHotList', [])
                            
                            for item in douyin_list:
                                try:
                                    title = item.get('title', '')
                                    hot_value = item.get('hot', '未知')
                                    link = item.get('url', f"https://www.douyin.com/search/{requests.utils.quote(title)}")
                                    
                                    event = {
                                        'title': title,
                                        'content': f'抖音热榜：{title}\n热度：{hot_value}',
                                        'hot_index': str(hot_value),
                                        'source': '抖音热榜',
                                        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                        'link': link
                                    }
                                    events.append(event)
                                    
                                except Exception as e:
                                    print(f"处理备用数据源抖音热榜项目时出错：{str(e)}")
                                    continue
                                    
                    except Exception as e:
                        print(f"解析备用数据源失败：{str(e)}")
                
        except Exception as e:
            print(f"获取抖音热榜失败：{str(e)}")
            
        return events

    def on_event_selected(self, index):
        """当选择事件时更新事件详情"""
        if index >= 0:
            event_data = self.events_list.itemData(index)
            try:
                # 获取详细内容
                detailed_content = self._get_event_detail(event_data)
                
                # 对文本进行HTML转义，防止HTML注入
                import html
                title = html.escape(event_data['title'])
                source = html.escape(event_data['source'])
                time_str = html.escape(event_data['time'])
                category = html.escape(event_data['category']) if 'category' in event_data else ''
                hot_index = html.escape(event_data['hot_index'])
                link = event_data['link']
                
                # 将文本内容转换为HTML格式
                detailed_content_html = html.escape(detailed_content).replace('\n', '<br>')
                
                # 组合显示内容为HTML格式（包含可点击链接）
                event_detail_html = f"""<div style="font-family: 'Microsoft YaHei'; line-height: 1.5;">
<h3 style="color: #333;">{title}</h3>

<p><strong>来源：</strong>{source}<br>
<strong>时间：</strong>{time_str}<br>
{f'<strong>分类：</strong>{category}<br>' if category else ''}
</p>

<p><strong>详细内容：</strong><br>
{detailed_content_html}
</p>

<p><strong>热度：</strong>{hot_index}</p>
<p><strong>原文链接：</strong><a href="{link}" style="color: #0066cc; text-decoration: underline;">{link}</a></p>
</div>"""
                self.event_edit.setHtml(event_detail_html)
                
            except Exception as e:
                QMessageBox.warning(self, "警告", f"获取详细内容失败：{str(e)}")

    def _get_event_detail(self, event_data):
        """获取事件的详细内容"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            if event_data['source'] == '抖音热榜':
                # 对于抖音热榜，获取相关视频内容
                search_url = event_data['link']
                response = requests.get(search_url, headers=headers)
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 获取视频内容
                video_contents = []
                video_items = soup.find_all('div', class_='video-card')[:3]  # 获取前3个视频
                
                for item in video_items:
                    try:
                        desc = item.find('div', class_='video-desc')
                        if desc:
                            content = desc.get_text().strip()
                            if content:
                                video_contents.append(content)
                    except:
                        continue
                
                return "\n\n".join(video_contents) if video_contents else event_data['content']
            
            elif event_data['source'] == '百度热':
                # 对于百度热搜，搜索相关新闻
                search_url = f"https://www.baidu.com/s?wd={event_data['title']}"
                response = requests.get(search_url, headers=headers)
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 获取搜索结果的新闻内容
                news_contents = []
                news_items = soup.find_all('div', class_='c-container')[:3]  # 获取前3新闻
                
                for item in news_items:
                    try:
                        content = item.get_text()
                        if content:
                            news_contents.append(content.strip())
                    except:
                        continue
                
                return "\n\n".join(news_contents)
                
            elif event_data['source'] == '新浪新闻':
                # 对于新浪新闻直接获取原文内容
                if 'link' in event_data:
                    response = requests.get(event_data['link'], headers=headers)
                    response.encoding = 'utf-8'
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 获取文章正文
                    article = soup.find('div', class_='article')
                    if article:
                        # 移除不需要的元素
                        for script in article.find_all('script'):
                            script.decompose()
                        for style in article.find_all('style'):
                            style.decompose()
                            
                        return article.get_text().strip()
            
            # 如果无法获取详细内容，返回原有内容
            return event_data['content']
            
        except Exception as e:
            print(f"获取详细内容失败：{str(e)}")
            return event_data['content']  # 返回原有内容作为后备

    def copy_to_content(self):
        """复制事件详情到内容编辑区"""
        # 获取事件详情的HTML内容
        html_content = self.event_edit.toHtml()
        self.content_edit.setHtml(html_content)

    def generate_content(self):
        current_tab = self.tab_widget.currentWidget()
        summary_length = self.get_summary_length("event")
        
        if current_tab == self.event_tab:
            # 检查是否已选择事件
            current_index = self.events_list.currentIndex()
            if current_index < 0:
                QMessageBox.information(self, "提示", "请先选择一个热点事件")
                return
            
            # 获取当前事件详情
            event_data = self.events_list.itemData(current_index)
            current_content = self.event_edit.toPlainText()
            
            # 生成详细描述
            detail_prompt = f"""请对以下热点事件进行详细描述，包括：
1. 事件的详细经过和时间线
2. 涉及的主要人物和机构
3. 事件的起因和背景
4. 目前的最新进展
5. 社会各界的主要反应

事件信息：
{current_content}
"""
            detail_content = self.call_ai_api(detail_prompt)
            
            if detail_content:
                # 生成精简总结
                summary_prompt = f"""请对以下事件内容进行总结，要求：
1. 总结字数控制在{summary_length}字左右
2. 包含事件的核心要素和关键影响
3. 突出事件的重要性和关注点
4. 语言简洁明了，重点突出

事件内容：
{detail_content}
"""
                summary_content = self.call_ai_api(summary_prompt)
                
                # 组合显示内容
                full_content = f"""标题：{event_data['title']}

来源：{event_data['source']}
时间：{event_data['time']}
{'分类：' + event_data['category'] if 'category' in event_data else ''}

事件总结（{summary_length}字）：
{summary_content}

详细内容：
{detail_content}

热度：{event_data['hot_index']}
原文链接：{event_data['link']}
"""
                # 将纯文本转换为HTML格式
                import html
                import re

                link = event_data['link']
                html_content = html.escape(full_content).replace('\n', '<br>')
                # 将链接URL转换为可点击链接
                url_pattern = r'(https?://[^\s]+)'
                html_content = re.sub(url_pattern, lambda m: f'<a href="{m.group(1)}">{m.group(1)}</a>', html_content)

                # 显示HTML内容
                self.event_edit.setHtml(f'<div style="font-family: Microsoft YaHei; line-height: 1.6;">{html_content}</div>')
                
        elif current_tab == self.analysis_tab:
            content_text = self.content_edit.toPlainText()
            if not content_text:
                QMessageBox.warning(self, "警告", "请先在热点事件模块的内容编辑区输入要分析的内容")
                return
                
            summary_length = self.get_summary_length("analysis")
            analysis_prompt = f"""作为公安机关首席情报研判师，请从公安工作角度对以下事件进行全面分析：

{content_text}

请从以下维度进行分析：
1. 社会治安影响评：
   - 是否有矛盾纠纷和社会治安风险
   - 是否影响社会面治安稳定
   - 是否存在重点人员涉入
   - 是否涉及敏感时期、地区

2. 舆情态势研判：
   - 网络舆情传播态
   - 主要舆论观点倾向
   - 可能的舆情发展方向
   - 是否存在不实信息传播   
   - 是否存在负面舆情
   - 是否存在负面舆情炒作风险

3. 风险隐患排查：
   - 可能存在的违法犯罪风险
   - 是否涉及重点场所、区域
   - 是否存在其他安全隐患
   - 是否影响社会面管控
   - 是否涉及特殊人群

4. 发展趋势预测：
   - 事态可能演变方向
   - 是否会产生连锁反应
   - 是否影响其他地区
   - 是否需要跨部门协作

请以'分析认为，'（注意包含逗号）开头，从公安工作实际出发，分条进行论述。"""
            
            analysis_content = self.call_ai_api(analysis_prompt)
            
            if analysis_content:
                # 确保分析内容以"分析认为，"开头
                if not analysis_content.startswith("分析认为，"):
                    analysis_content = "分析认为，" + analysis_content
                
                # 生成精简版分析
                summary_prompt = f"""请对以下分析内容进行精简总结，要求：
1. 总结字数控制在{summary_length}字左右
2. 保持'分析认为'开头
3. 突出关键分析观点
4. 保持逻辑性和专业性

分析内容：
{analysis_content}
"""
                summary_content = self.call_ai_api(summary_prompt)
                
                # 组合显示内容
                full_content = f"""分析精要（{summary_length}字）：
{summary_content}

详细分析：
{analysis_content}"""

                # 将纯文本转换为HTML格式
                import html
                import re

                html_content = html.escape(full_content).replace('\n', '<br>')
                # 将链接URL转换为可点击链接
                url_pattern = r'(https?://[^\s]+)'
                html_content = re.sub(url_pattern, lambda m: f'<a href="{m.group(1)}">{m.group(1)}</a>', html_content)

                self.analysis_edit.setText(full_content)
                # 分析结果单独保存（这是QTextBrowser）
                analysis_html = html.escape(analysis_content).replace('\n', '<br>')
                analysis_html = re.sub(url_pattern, lambda m: f'<a href="{m.group(1)}">{m.group(1)}</a>', analysis_html)
                self.analysis_result.setHtml(f'<div style="font-family: Microsoft YaHei; line-height: 1.6;">{analysis_html}</div>')
                
        elif current_tab == self.suggestion_tab:
            analysis_text = self.analysis_edit.toPlainText()
            if not analysis_text:
                QMessageBox.warning(self, "警告", "请先在分析模块生成或输入分析内容")
                return
                
            summary_length = self.get_summary_length("suggestion")
            suggestion_prompt = f"""作为公安机关首席情报研判师，基于以下分析内容，请从公安工作角度提出具体工作建议：

{analysis_text}

请提出具体的工作建议，要求：
1. 以'建议各地各部门做好以下几方面工作：'（注意包含冒号）开头

2. 使用'一是、二是、三是、四是'的格式列出具体建议，建议应包含：
   - 前期预防管控措施
   - 现场处置工作要点
   - 舆情引导工作措施
   - 后续跟踪研判工作

3. 具体建议应涵盖：
   - 情报信息研判工作
   - 社会面防控措施
   - 警力部署方案
   - 应急处置预案
   - 舆情引导策略
   - 部门协作机制

4. 工作要求：
   - 符合公安实战需要
   - 具有实操可行性
   - 明确职责分工
   - 注重实际效果
   - 体现依法履职

5. 工作安排：
   - 区分近期和长期措施
   - 明确工作优先次序
   - 强调持续性管控
   - 注重协同配合

6. 具体措施（根据实际情况结合上述内容看能否适用）：
   - 宣传教育
   - 联动机制
   - 风险排查和预警
   - 制定预案和应急处置
   - 加强监控和巡逻
   - 舆情监测与管控
   - 数据共享和管理
   - 企业监管与法律打击
   - 其他措施

请确保建议具有针对性、可操作性，符合公安工作实际。"""
            
            suggestion_content = self.call_ai_api(suggestion_prompt)
            
            if suggestion_content:
                # 确保建议内容以指定文字开头
                if not suggestion_content.startswith("建议各地各部门做好以下几方面工作："):
                    suggestion_content = "建议各地各部门做好以下几方面工作：\n" + suggestion_content
                
                # 生成精简版建议
                summary_prompt = f"""请对以下建议内容进行精简总结，要求：
1. 总结字数控制在{summary_length}字左右
2. 保持'建议各地各部门做好以下几方面工作：'开头
3. 保持'一是、二是、三是、四是'的格式
4. 突出关键建议要点
5. 确保建议的可操作性

建议内容：
{suggestion_content}
"""
                summary_content = self.call_ai_api(summary_prompt)
                
                # 组合显示内容
                full_content = f"""建议精要（{summary_length}字）：
{summary_content}

详细建议：
{suggestion_content}"""

                # 将纯文本转换为HTML格式
                import html
                import re

                html_content = html.escape(full_content).replace('\n', '<br>')
                # 将链接URL转换为可点击链接
                url_pattern = r'(https?://[^\s]+)'
                html_content = re.sub(url_pattern, lambda m: f'<a href="{m.group(1)}">{m.group(1)}</a>', html_content)

                self.suggestion_edit.setText(full_content)
                # 建议结果单独保存（这是QTextBrowser）
                suggestion_html = html.escape(suggestion_content).replace('\n', '<br>')
                suggestion_html = re.sub(url_pattern, lambda m: f'<a href="{m.group(1)}">{m.group(1)}</a>', suggestion_html)
                self.suggestion_content_edit.setHtml(f'<div style="font-family: Microsoft YaHei; line-height: 1.6;">{suggestion_html}</div>')

    def polish_content(self):
        current_tab = self.tab_widget.currentWidget()
        
        if current_tab == self.event_tab:
            # 使用内容编辑区的内容进行润色
            content = self.content_edit.toPlainText()
            if not content:
                QMessageBox.warning(self, "警告", "请先在内容编辑区输入要润色的内容")
                return
            prompt = f"请对以下内容进行优化和润色：\n{content}"
            polished_content = self.call_ai_api(prompt)
            if polished_content:
                self.content_edit.setText(polished_content)
            
        elif current_tab == self.analysis_tab:
            content = self.analysis_edit.toPlainText()
            prompt = f"请对以下分析内容进行优化和润色，保持'分析认为'开头：\n{content}"
            
        elif current_tab == self.suggestion_tab:
            content = self.suggestion_edit.toPlainText()
            prompt = f"请对以下建议内容进行优化和润色，保持'建议各地各部门做好以下几方面工作：'开头，并确保使用'一是、二是、三是、四是'的格式：\n{content}"
        
        polished_content = self.call_ai_api(prompt)
        if polished_content:
            if current_tab == self.event_tab:
                self.content_edit.setText(polished_content)
            elif current_tab == self.analysis_tab:
                self.analysis_edit.setText(polished_content)
            elif current_tab == self.suggestion_tab:
                self.suggestion_edit.setText(polished_content)

    def validate_summary_length(self, text):
        """验证输入的字数是否有效"""
        try:
            if text:
                length = int(text)
                if length < 0 or length > 1000:
                    # 找出是哪个输入框触发的验证
                    sender = self.sender()
                    sender.setText("200")
                    QMessageBox.warning(self, "警告", "请输入0-1000之间的数字")
        except ValueError:
            # 找出是哪个输入框触发的验证
            sender = self.sender()
            sender.setText("200")
            QMessageBox.warning(self, "警告", "请输入有效的数字")

    def get_summary_length(self, module_type):
        """获取当前设置的精简字数"""
        try:
            if module_type == "event":
                length = int(self.summary_length.text() or "200")
            elif module_type == "analysis":
                length = int(self.analysis_summary_length.text() or "200")
            elif module_type == "suggestion":
                length = int(self.suggestion_summary_length.text() or "200")
            return min(max(0, length), 1000)  # 确保0-1000范围内
        except ValueError:
            return 200  # 默认值

    def copy_analysis_to_content(self):
        """复制分析结果到分析编辑区"""
        html_content = self.analysis_edit.toHtml()
        self.analysis_result.setHtml(html_content)

    def copy_suggestion_to_content(self):
        """复制建议结果到建议编辑区"""
        html_content = self.suggestion_edit.toHtml()
        self.suggestion_content_edit.setHtml(html_content)

    def generate_full_report(self):
        """生成完整的微情报"""
        try:
            # 获取事件内容
            event_content = self.content_edit.toPlainText()
            if not event_content:
                QMessageBox.warning(self, "警告", "请先在热点事件模块生成内容")
                return
            
            # 获取分析内容（从内容编辑区域获取）
            analysis_content = self.analysis_edit.toPlainText()  # 修改这里
            if not analysis_content:
                QMessageBox.warning(self, "警告", "请先在分析模块的内容编辑区域生成内容")
                return
            
            # 获取建议内容
            suggestion_content = self.suggestion_edit.toPlainText()
            if not suggestion_content:
                QMessageBox.warning(self, "警告", "请先在建议模块生成内容")
                return
            
            # 使用更安全的时间格式
            current_time = datetime.datetime.now().strftime('%Y-%m-%d')
            
            # 纯文本版本（用于导出）
            report_content = f"""微情报
{current_time}

一、热点事件概况
{event_content}

二、分析研判
{analysis_content}

三、对策建议
{suggestion_content}

"""
            # 保存纯文本版本供导出时使用
            self.report_text = report_content
            
            # 组合HTML格式微情报（用于美化显示）
            import html
            import re
            
            # 处理文本为HTML并转换链接
            def text_to_html_with_links(text):
                # 转义HTML
                html_text = html.escape(text)
                # 将URL转换为可点击链接
                url_pattern = r'(https?://[^\s]+)'
                html_text = re.sub(url_pattern, lambda m: f'<a href="{m.group(1)}">{m.group(1)}</a>', html_text)
                # 替换换行符为<br>
                html_text = html_text.replace('\n', '<br>')
                return html_text
            
            # 转换各部分内容
            event_html = text_to_html_with_links(event_content)
            analysis_html = text_to_html_with_links(analysis_content)
            suggestion_html = text_to_html_with_links(suggestion_content)
            
            # 组合HTML报告
            report_html = f"""
            <div style="font-family: 'Microsoft YaHei'; line-height: 1.6;">
                <h1 style="text-align: center; color: #333;">微情报</h1>
                <p style="text-align: center; color: #666; margin-bottom: 20px;">{current_time}</p>
                
                <h2 style="color: #4a86e8; border-bottom: 1px solid #ddd; padding-bottom: 5px;">一、热点事件概况</h2>
                <div style="margin-left: 20px;">{event_html}</div>
                
                <h2 style="color: #4a86e8; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 20px;">二、分析研判</h2>
                <div style="margin-left: 20px;">{analysis_html}</div>
                
                <h2 style="color: #4a86e8; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 20px;">三、对策建议</h2>
                <div style="margin-left: 20px;">{suggestion_html}</div>
            </div>
            """
            
            # 使用HTML显示
            self.report_edit.setHtml(report_html)
            
            QMessageBox.information(self, "提示", "微情报生成成功")
            
        except Exception as e:
            error_msg = str(e)
            QMessageBox.critical(self, "错误", f"生成微情报失败：{error_msg}")

    def export_report(self):
        """导出微情报到文件"""
        try:
            # 获取保存文件的路径
            current_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            file_name = f"report_{current_time}.txt"  # 使用英文名称避免编码问题
            
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存微情报",
                file_name,
                "文本文件 (*.txt);;所有文件 (*.*)",
                options=QFileDialog.Options()
            )
            
            if file_path:
                # 检查是否已生成微情报
                content = ""
                if hasattr(self, 'report_text') and self.report_text:
                    content = self.report_text
                else:
                    # 如果没有report_text属性，则从控件获取纯文本内容
                    content = self.report_edit.toPlainText()
                    
                # 使用 utf-8-sig 编码保存文件，添加 BOM 头以支持中文
                with open(file_path, 'w', encoding='utf-8-sig', errors='ignore') as f:
                    f.write(content)
                QMessageBox.information(self, "提示", "微情报导出成功")
                
        except Exception as e:
            error_msg = str(e)
            QMessageBox.critical(self, "错误", f"导出微情报失败：{error_msg}")

if __name__ == '__main__':
    # 设置Windows 7兼容的高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 设置SSL兼容性
    import ssl
    try:
        _create_unverified_https_context = ssl._create_unverified_context
    except AttributeError:
        pass
    else:
        ssl._create_default_https_context = _create_unverified_https_context
    
    # 设置平台插件路径
    python_path = os.path.dirname(sys.executable)
    plugins_path = os.path.join(python_path, 'Lib', 'site-packages', 'PyQt5', 'Qt5', 'plugins')
    os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = plugins_path
    
    # 设置Windows 7兼容的样式
    app = QApplication(sys.argv)
    app.setStyle(QStyleFactory.create('Fusion'))
    
    # 设置默认字体
    font = QApplication.font()
    font.setFamily('Microsoft YaHei')
    QApplication.setFont(font)
    
    # 创建主窗口前加载配置并设置代理
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
            if config.get('proxy_enabled', 0) == 1:
                host = config.get('proxy_host', '')
                port = config.get('proxy_port', '')
                if host and port:
                    os.environ['HTTP_PROXY'] = f'http://{host}:{port}'
                    os.environ['HTTPS_PROXY'] = f'http://{host}:{port}'
    except FileNotFoundError:
        pass
    
    window = MainWindow()
    window.show()
    sys.exit(app.exec_()) 