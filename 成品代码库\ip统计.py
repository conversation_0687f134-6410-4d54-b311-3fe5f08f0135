import pandas as pd
import geoip2.database

# 读取CSV文件
df = pd.read_csv('ip.csv')

# 提取IP地址的前三个部分作为网段
df['网段'] = df['IPdz'].str.split('.').str[:3].str.join('.')

# 创建一个用于读取GeoLite2数据库的读者
reader = geoip2.database.Reader('GeoLite2-City.mmdb')

# 定义函数获取地理位置信息
def get_location(ip):
    try:
        response = reader.city(ip)
        country = response.country.name
        subdivision = response.subdivisions.most_specific.name
        city = response.city.name
        return f"{country}, {subdivision}, {city}"
    except:
        return "未知位置"

# 获取每个IP的地理位置
df['位置'] = df['IPdz'].apply(get_location)

# 按网段分组，计算去重的人名列表和人数
unique_names = df.groupby('网段')['jyhm'].apply(lambda x: ', '.join(x.drop_duplicates())).reset_index()
unique_counts = df.groupby('网段')['jyhm'].nunique().reset_index()
locations = df.groupby('网段')['位置'].first().reset_index()

# 合并结果
result = pd.merge(unique_names, unique_counts, on='网段')
result = pd.merge(result, locations, on='网段')

# 重命名列
result.columns = ['网段', '人名列表', '人数', '位置']

# 保存结果到Excel文件
result.to_excel('网段统计结果.xlsx', index=False)

# 关闭GeoLite2数据库的读者
reader.close()

print("统计完成，结果已保存到 '网段统计结果.xlsx'")
