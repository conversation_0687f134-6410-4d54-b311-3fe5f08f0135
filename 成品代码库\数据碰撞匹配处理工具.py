import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import re
import os
import logging
import sys
import warnings
import ctypes

warnings.filterwarnings('ignore')


# 确保能正确处理临时文件路径
if hasattr(sys, '_MEIPASS'):
    os.chdir(sys._MEIPASS)

# 设置pandas选项，避免可能的兼容性问题
pd.set_option('mode.chained_assignment', None)

def set_app_icon():
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        icon_path = os.path.join(sys._MEIPASS, '2.ico')
    else:
        icon_path = '2.ico'
    
    if os.path.exists(icon_path):
        try:
            # 设置任务栏图标
            myappid = 'tzga.datamatcher.1.0'  # 任意字符串，作为应用程序ID
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
            return icon_path
        except:
            return None
    return None

# 数据清洗函数：去除空格、标点符号等
def clean_data(text):
    if pd.isna(text):
        return ''
    # 转换为字符串
    text = str(text)
    # 提取所有数字
    cleaned_text = ''.join(filter(str.isdigit, text))
    # 去除多余的空格
    cleaned_text = cleaned_text.strip()
    return cleaned_text

# 数据匹配和计数函数
def match_data(df1, column1, df2, column2, match_mode="exact", deduplicate=False):
    logging.info(f"开始数据匹配，模式：{match_mode}，去重：{deduplicate}")
    
    try:
        # 创建数据副本，避免修改原始数据
        df1_copy = df1.copy()
        df2_copy = df2.copy()
        
        # 确保列名存在
        if column1 not in df1_copy.columns:
            raise ValueError(f"在第一个文件中未找到列：{column1}")
        if column2 not in df2_copy.columns:
            raise ValueError(f"在第二个文件中未找到列：{column2}")
        
        # 统一数据类型为字符串并处理空值
        df1_copy[column1] = df1_copy[column1].fillna('').astype(str)
        df2_copy[column2] = df2_copy[column2].fillna('').astype(str)
        
        if match_mode == "exact":
            # 精确匹配模式
            if deduplicate:
                # 去重处理
                matched_values = set(df1_copy[column1]) & set(df2_copy[column2])
                matched = pd.DataFrame({
                    column1: list(matched_values),
                    f"{column2}(文件2)": list(matched_values)
                })
            else:
                # 不去重，保留所有匹配记录
                matched = pd.merge(
                    df1_copy[[column1]], 
                    df2_copy[[column2]], 
                    left_on=column1,
                    right_on=column2,
                    how='inner'
                )
                # 重命名第二个文件的列
                matched = matched.rename(columns={column2: f"{column2}(文件2)"})
        else:
            # 模糊匹配模式
            # 创建临时列用于清洗后的数据
            df1_temp = pd.DataFrame()
            df2_temp = pd.DataFrame()
            
            # 复制原始列并添加清洗列
            df1_temp['original'] = df1_copy[column1]
            df1_temp['cleaned'] = df1_copy[column1].apply(clean_data)
            
            df2_temp['original'] = df2_copy[column2]
            df2_temp['cleaned'] = df2_copy[column2].apply(clean_data)
            
            if deduplicate:
                # 去重模式：只保留清洗后的唯一值
                matched_values = set(df1_temp['cleaned']) & set(df2_temp['cleaned'])
                # 创建结果DataFrame
                result = pd.DataFrame()
                
                # 获取第一个文件的匹配记录
                mask1 = df1_temp['cleaned'].isin(matched_values)
                result[column1] = df1_temp.loc[mask1, 'original'].drop_duplicates()
                
                # 获取第二个文件的匹配记录
                mask2 = df2_temp['cleaned'].isin(matched_values)
                result[f"{column2}(文件2)"] = df2_temp.loc[mask2, 'original'].drop_duplicates()
                
                matched = result
            else:
                # 不去重模式：保留所有匹配记录
                matched = pd.merge(
                    df1_temp[['original', 'cleaned']],
                    df2_temp[['original', 'cleaned']],
                    on='cleaned',
                    how='inner'
                )
                # 重命名列
                matched = matched.rename(columns={
                    'original_x': column1,
                    'original_y': f"{column2}(文件2)"
                })
                # 只保留原始值列
                matched = matched[[column1, f"{column2}(文件2)"]]
        
        # 计数
        match_count = len(matched)
        logging.info(f"匹配完成，总匹配数: {match_count}")
        
        return matched, match_count
        
    except Exception as e:
        logging.error(f"匹配过程中出错: {str(e)}")
        raise Exception(f"匹配过程中出错: {str(e)}")

def union_data(df1, columns1, df2, columns2, match_mode="exact", deduplicate=False):
    """
    计算两个数据集的并集，支持多列对应操作，保持字段内容的一一对应关系
    columns1: 第一个文件的列名列表
    columns2: 第二个文件的列名列表
    """
    logging.info(f"开始数据并集操作，模式：{match_mode}，去重：{deduplicate}")
    
    try:
        # 创建数据副本
        df1_copy = df1.copy()
        df2_copy = df2.copy()
        
        # 确保列名存在
        for col1, col2 in zip(columns1, columns2):
            if col1 not in df1_copy.columns:
                raise ValueError(f"在第一个文件中未找到列：{col1}")
            if col2 not in df2_copy.columns:
                raise ValueError(f"在第二个文件中未找到列：{col2}")
        
        # 统一数据类型为字符串并处理空值
        for col1, col2 in zip(columns1, columns2):
            df1_copy[col1] = df1_copy[col1].fillna('').astype(str)
            df2_copy[col2] = df2_copy[col2].fillna('').astype(str)
        
        # 创建结果DataFrame
        result_df = pd.DataFrame()
        
        # 重命名第二个DataFrame的列名以匹配第一个
        rename_dict = {col2: col1 for col1, col2 in zip(columns1, columns2)}
        df2_renamed = df2_copy[columns2].rename(columns=rename_dict)
        
        if match_mode == "exact":
            if deduplicate:
                # 合并两个DataFrame并去重
                result_df = pd.concat([df1_copy[columns1], df2_renamed], ignore_index=True).drop_duplicates()
            else:
                # 合并两个DataFrame保留所有记录
                result_df = pd.concat([df1_copy[columns1], df2_renamed], ignore_index=True)
        else:
            # 模糊匹配模式
            # 为每个文件创建一个包含所有列的清洗后的DataFrame
            df1_cleaned = pd.DataFrame()
            df2_cleaned = pd.DataFrame()
            
            # 对每一列进行清洗
            for col1, col2 in zip(columns1, columns2):
                df1_cleaned[col1] = df1_copy[col1].apply(clean_data)
                df2_cleaned[col2] = df2_copy[col2].apply(clean_data)
            
            # 创建原始值和清洗值的映射
            df1_mapping = pd.concat([df1_copy[columns1], df1_cleaned], axis=1)
            df2_mapping = pd.concat([df2_copy[columns2], df2_cleaned], axis=1)
            
            if deduplicate:
                # 合并清洗后的数据并去重
                result_cleaned = pd.concat([df1_cleaned, df2_cleaned.rename(columns=rename_dict)], 
                                        ignore_index=True).drop_duplicates()
                
                # 恢复原始值
                result_df = pd.DataFrame()
                for col1, col2 in zip(columns1, columns2):
                    # 从第一个文件中获取匹配的原始值
                    mask1 = df1_cleaned[col1].isin(result_cleaned[col1])
                    values1 = df1_copy.loc[mask1, col1]
                    
                    # 从第二个文件中获取匹配的原始值
                    mask2 = df2_cleaned[col2].isin(result_cleaned[col1])
                    values2 = df2_copy.loc[mask2, col2]
                    
                    # 合并原始值
                    result_df[col1] = pd.concat([values1, values2], ignore_index=True).drop_duplicates()
            else:
                # 不去重，保留所有记录
                result_df = pd.concat([
                    df1_copy[columns1],
                    df2_copy[columns2].rename(columns=rename_dict)
                ], ignore_index=True)
        
        # 计数
        result_count = len(result_df)
        logging.info(f"并集操作完成，总数量: {result_count}")
        
        return result_df, result_count
        
    except Exception as e:
        logging.error(f"并集操作过程中出错: {str(e)}")
        raise Exception(f"并集操作过程中出错: {str(e)}")

# 保存结果函数
def save_results(matched_df):
    logging.info("开始保存匹配结果")
    if matched_df.empty:
        messagebox.showwarning("无匹配结果", "没有匹配到任何结果。")
        logging.warning("尝试保存空的匹配结果")
        return
    # 提供保存对话框
    file_path = filedialog.asksaveasfilename(defaultextension=".xlsx",
                                             filetypes=[("Excel files", "*.xlsx"),
                                                        ("CSV files", "*.csv")])
    if file_path:
        try:
            if file_path.endswith('.csv'):
                matched_df.to_csv(file_path, index=False, encoding='utf-8-sig')
                logging.info(f"结果已保存为 CSV 文件: {file_path}")
            else:
                matched_df.to_excel(file_path, index=False, engine='openpyxl')
                logging.info(f"结果已保存为 Excel 文件: {file_path}")
            messagebox.showinfo("保存成功", f"结果已保存到 {file_path}")
        except Exception as e:
            logging.error(f"保存文件时出错: {e}")
            messagebox.showerror("保存失败", f"保存文件时出错: {e}")

# 数据相减函数
def subtract_data(df1, column1, df2, column2, match_mode="exact", deduplicate=False):
    """
    从第一个数据集中减去第二个数据集中的数据
    """
    logging.info(f"开始数据相减，模式：{match_mode}，去重：{deduplicate}")
    
    try:
        # 创建数据副本
        df1_copy = df1.copy()
        df2_copy = df2.copy()
        
        # 确保列名存在
        if column1 not in df1_copy.columns:
            raise ValueError(f"在第一个文件中未找到列：{column1}")
        if column2 not in df2_copy.columns:
            raise ValueError(f"在第二个文件中未找到列：{column2}")
        
        # 统一数据类型为字符串并处理空值
        df1_copy[column1] = df1_copy[column1].fillna('').astype(str)
        df2_copy[column2] = df2_copy[column2].fillna('').astype(str)
        
        if match_mode == "exact":
            # 精确相减模式
            if deduplicate:
                # 去重处理
                values_to_keep = set(df1_copy[column1]) - set(df2_copy[column2])
                result = pd.DataFrame({column1: list(values_to_keep)})
            else:
                # 不去重，保留所有记录
                result = df1_copy[~df1_copy[column1].isin(df2_copy[column2])]
        else:
            # 模糊相减模式
            # 创建临时列用于清洗后的数据
            df1_temp = pd.DataFrame()
            df2_temp = pd.DataFrame()
            
            # 复制原始列并添加清洗列
            df1_temp['original'] = df1_copy[column1]
            df1_temp['cleaned'] = df1_copy[column1].apply(clean_data)
            
            df2_temp['original'] = df2_copy[column2]
            df2_temp['cleaned'] = df2_copy[column2].apply(clean_data)
            
            if deduplicate:
                # 去重模式：只保留清洗后的唯一值
                values_to_keep = set(df1_temp['cleaned']) - set(df2_temp['cleaned'])
                # 找出对应的原始值
                result = df1_temp[df1_temp['cleaned'].isin(values_to_keep)][['original']].drop_duplicates()
                # 重命名列为原始列名
                result = result.rename(columns={'original': column1})
            else:
                # 不去重模式：保留所有不匹配的记录
                result = df1_temp[~df1_temp['cleaned'].isin(df2_temp['cleaned'])][['original']]
                # 重命名列为原始列名
                result = result.rename(columns={'original': column1})
        
        # 计数
        result_count = len(result)
        logging.info(f"相减完成，剩余数量: {result_count}")
        
        return result, result_count
        
    except Exception as e:
        logging.error(f"相减过程中出错: {str(e)}")
        raise Exception(f"相减过程中出错: {str(e)}")

# 主应用类
class DataMatcherApp:
    def __init__(self, root):
        self.root = root
        self.root.title("数据碰撞匹配处理工具")
        self.root.geometry("750x800")  # 增加窗口高度以适应新控件
        self.root.resizable(False, False)
        
        # 设置图标
        icon_path = set_app_icon()
        if icon_path:
            self.root.iconbitmap(icon_path)
        
        # 修改全局字体和颜色
        self.style = {
            'font': ('Microsoft YaHei', 10),
            'bg_color': 'white',
            'button_bg': '#4a90e2',
            'button_fg': 'white',
            'label_fg': '#000000'
        }
        
        self.root.configure(bg=self.style['bg_color'])
        
        # 初始化变量
        self.file1_path = ""
        self.file2_path = ""
        self.df1 = None
        self.df2 = None
        self.matched_df = None
        self.match_mode = tk.StringVar(value="exact")
        self.selected_columns1 = []  # 存储第一个文件选中的列
        self.selected_columns2 = []  # 存储第二个文件选中的列

        # 创建界面组件
        self.create_widgets()

    def create_widgets(self):
        # 创建外层框架用于居中
        outer_frame = tk.Frame(self.root, bg=self.style['bg_color'])
        outer_frame.pack(expand=True, fill=tk.BOTH)
        
        # 创建主框架，设置固定宽度以实现居中效果
        frame = tk.Frame(outer_frame, width=650, padx=15, pady=10, bg=self.style['bg_color'])
        frame.pack(expand=True)
        frame.grid_propagate(True)
        
        # 配置grid列的权重，使内容居中
        frame.grid_columnconfigure(0, weight=1)
        frame.grid_columnconfigure(1, weight=0)

        # 修改文本输入框宽度
        entry_width = 55

        # 第一个文件上传
        label1 = tk.Label(frame, text="选择第一个文件:", font=self.style['font'], 
                bg=self.style['bg_color'], fg=self.style['label_fg'])
        label1.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0,5))

        self.file1_entry = tk.Entry(frame, width=entry_width, font=self.style['font'])
        self.file1_entry.grid(row=1, column=0, sticky='ew', padx=(0,10), pady=(0,10))

        self.file1_button = tk.Button(frame, text="浏览", width=12, 
                                    font=self.style['font'],
                                    bg=self.style['button_bg'],
                                    fg=self.style['button_fg'],
                                    relief=tk.FLAT,
                                    command=self.browse_file1)
        self.file1_button.grid(row=1, column=1, pady=(0,10))

        # 第二个文件上传
        tk.Label(frame, text="选择第二个文件:", font=self.style['font'],
                bg=self.style['bg_color'], fg=self.style['label_fg']).grid(
                row=2, column=0, columnspan=2, sticky=tk.W, pady=(0,5))

        self.file2_entry = tk.Entry(frame, width=entry_width, font=self.style['font'])
        self.file2_entry.grid(row=3, column=0, sticky='ew', padx=(0,10), pady=(0,10))

        self.file2_button = tk.Button(frame, text="浏览", width=12,
                                    font=self.style['font'],
                                    bg=self.style['button_bg'],
                                    fg=self.style['button_fg'],
                                    relief=tk.FLAT,
                                    command=self.browse_file2)
        self.file2_button.grid(row=3, column=1, pady=(0,10))

        # 创建列选择框架
        columns_frame = tk.Frame(frame, bg=self.style['bg_color'])
        columns_frame.grid(row=4, column=0, columnspan=2, sticky='ew', pady=(10,10))
        
        # 第一个文件的列选择框
        tk.Label(columns_frame, text="第一个文件列选择:", font=self.style['font'],
                bg=self.style['bg_color']).grid(row=0, column=0, sticky='w')
        
        # 创建滚动条
        scrollbar1 = tk.Scrollbar(columns_frame, orient="vertical")
        scrollbar1.grid(row=1, column=0, sticky='nse', padx=(0,5))
        
        self.columns1_listbox = tk.Listbox(columns_frame, selectmode='multiple',
                                         width=30, height=5, font=self.style['font'],
                                         exportselection=0,  # 防止选择被清除
                                         yscrollcommand=scrollbar1.set)
        self.columns1_listbox.grid(row=1, column=0, padx=5)
        scrollbar1.config(command=self.columns1_listbox.yview)
        
        # 第二个文件的列选择框
        tk.Label(columns_frame, text="第二个文件列选择:", font=self.style['font'],
                bg=self.style['bg_color']).grid(row=0, column=1, sticky='w')
        
        # 创建滚动条
        scrollbar2 = tk.Scrollbar(columns_frame, orient="vertical")
        scrollbar2.grid(row=1, column=1, sticky='nse', padx=(0,5))
        
        self.columns2_listbox = tk.Listbox(columns_frame, selectmode='multiple',
                                         width=30, height=5, font=self.style['font'],
                                         exportselection=0,  # 防止选择被清除
                                         yscrollcommand=scrollbar2.set)
        self.columns2_listbox.grid(row=1, column=1, padx=5)
        scrollbar2.config(command=self.columns2_listbox.yview)

        # 添加列选择提示标签
        self.selection_hint = tk.Label(columns_frame, text="", font=self.style['font'],
                                     bg=self.style['bg_color'], fg='#666666')
        self.selection_hint.grid(row=2, column=0, columnspan=2, pady=(5,0))

        # 绑定选择事件
        self.columns1_listbox.bind('<<ListboxSelect>>', self.on_listbox_select)
        self.columns2_listbox.bind('<<ListboxSelect>>', self.on_listbox_select)

        # 操作模式选择
        tk.Label(frame, text="选择操作模式:", font=self.style['font'],
                bg=self.style['bg_color'], fg=self.style['label_fg']).grid(
                row=5, column=0, columnspan=2, sticky=tk.W, pady=(10,5))

        # 操作模式单选按钮
        mode_frame = tk.Frame(frame, bg=self.style['bg_color'])
        mode_frame.grid(row=6, column=0, columnspan=2, sticky='ew', pady=(0,10))

        self.operation_mode = tk.StringVar(value="match")
        self.operation_mode.trace_add("write", self.on_operation_mode_change)
        modes = [
            ("A∩B（数据碰撞）", "match"),
            ("A∪B（数据并集）", "union"),
            ("A-B（第一个文件减去第二个文件）", "a_minus_b"),
            ("B-A（第二个文件减去第一个文件）", "b_minus_a")
        ]

        for i, (text, value) in enumerate(modes):
            tk.Radiobutton(mode_frame, text=text, variable=self.operation_mode,
                          value=value, font=self.style['font'],
                          bg=self.style['bg_color']).pack(anchor='w')

        # 处理模式选择
        process_frame = tk.Frame(frame, bg=self.style['bg_color'])
        process_frame.grid(row=7, column=0, columnspan=2, sticky='ew', pady=(0,10))

        tk.Label(process_frame, text="选择处理模式:", font=self.style['font'],
                bg=self.style['bg_color'], fg=self.style['label_fg']).pack(side=tk.LEFT)

        tk.Radiobutton(process_frame, text="精确处理（完全一致）",
                      variable=self.match_mode, value="exact",
                      font=self.style['font'], bg=self.style['bg_color']).pack(side=tk.LEFT, padx=20)
        tk.Radiobutton(process_frame, text="模糊处理（忽略标点空格等噪声数据）",
                      variable=self.match_mode, value="fuzzy",
                      font=self.style['font'], bg=self.style['bg_color']).pack(side=tk.LEFT)

        # 去重选项
        self.deduplicate_var = tk.BooleanVar(value=False)
        tk.Checkbutton(frame, text="去重", 
                      variable=self.deduplicate_var,
                      font=self.style['font'],
                      bg=self.style['bg_color']).grid(
                      row=8, column=0, columnspan=2, sticky='w', pady=(0,10))

        # 按钮和结果区域
        button_frame = tk.Frame(frame, bg=self.style['bg_color'])
        button_frame.grid(row=9, column=0, columnspan=2, pady=(0,10))

        # 按钮样式
        button_style = {
            'font': self.style['font'],
            'bg': self.style['button_bg'],
            'fg': self.style['button_fg'],
            'relief': tk.FLAT,
            'width': 18,
            'pady': 5
        }

        # 开始按钮
        self.match_button = tk.Button(button_frame, text="开始处理",
                                    command=self.start_matching,
                                    **button_style)
        self.match_button.pack(pady=(0,5))

        # 结果标签
        self.result_label = tk.Label(button_frame, text="匹配结果将显示在弹出窗口中。",
                                   font=self.style['font'],
                                   bg=self.style['bg_color'],
                                   fg=self.style['label_fg'])
        self.result_label.pack(pady=(0,5))

        # 保存按钮
        self.save_button = tk.Button(button_frame, text="导出结果",
                                   command=self.save_results_button,
                                   **button_style)
        self.save_button.pack(pady=(0,5))
        self.save_button.config(state=tk.DISABLED)

        # 版权信息
        copyright_label = tk.Label(
            frame, 
            text="版权所有：台州市公安局 解晟",
            font=('Microsoft YaHei', 12),
            fg="#000000",
            bg=self.style['bg_color']
        )
        copyright_label.grid(row=10, column=0, columnspan=2, pady=(15,0))

    def browse_file1(self):
        file_path = filedialog.askopenfilename(filetypes=[("All files", "*.*"),
                                                         ("Excel files", "*.xlsx *.xls"),
                                                         ("CSV files", "*.csv"),
                                                         ("Text files", "*.txt")])
        if file_path:
            self.file1_path = file_path
            self.file1_entry.delete(0, tk.END)
            self.file1_entry.insert(0, file_path)
            logging.info(f"选择第一个文件: {file_path}")
            self.load_file1()

    def browse_file2(self):
        file_path = filedialog.askopenfilename(filetypes=[("All files", "*.*"),
                                                         ("Excel files", "*.xlsx *.xls"),
                                                         ("CSV files", "*.csv"),
                                                         ("Text files", "*.txt")])
        if file_path:
            self.file2_path = file_path
            self.file2_entry.delete(0, tk.END)
            self.file2_entry.insert(0, file_path)
            logging.info(f"选择第二个文件: {file_path}")
            self.load_file2()

    def load_file1(self):
        try:
            self.df1 = self.read_file(self.file1_path)
            columns = list(self.df1.columns)
            if not columns:
                raise ValueError("第一个文件中没有列。")
            
            # 更新列选择列表
            self.columns1_listbox.delete(0, tk.END)
            for col in columns:
                self.columns1_listbox.insert(tk.END, col)
            
            logging.info(f"加载第一个文件的列: {columns}")
        except Exception as e:
            logging.error(f"无法读取第一个文件: {e}")
            messagebox.showerror("文件读取错误", f"无法读取第一个文件: {e}")

    def load_file2(self):
        try:
            self.df2 = self.read_file(self.file2_path)
            columns = list(self.df2.columns)
            if not columns:
                raise ValueError("第二个文件中没有列。")
            
            # 更新列选择列表
            self.columns2_listbox.delete(0, tk.END)
            for col in columns:
                self.columns2_listbox.insert(tk.END, col)
            
            logging.info(f"加载第二个文件的列: {columns}")
        except Exception as e:
            logging.error(f"无法读取第二个文件: {e}")
            messagebox.showerror("文件读取错误", f"无法读取第二个文件: {e}")

    def read_file(self, file_path):
        ext = os.path.splitext(file_path)[1].lower()
        logging.debug(f"读取文件: {file_path}, 扩展名: {ext}")
        
        # 扩展编码列表
        encodings = [
            'utf-8', 'gbk', 'gb2312', 'gb18030', 'big5', 
            'utf-16', 'utf-16le', 'utf-16be',
            'ascii', 'latin1', 'iso-8859-1',
            'cp936', 'cp950', 'cp1252'
        ]
        
        if ext in ['.xlsx', '.xls']:
            engines = {
                '.xlsx': 'openpyxl',
                '.xls': 'xlrd'
            }
            engine = engines.get(ext, 'openpyxl')
            return pd.read_excel(file_path, engine=engine)
        elif ext == '.csv':
            # 尝试不同的编码方式读取CSV文件
            last_error = None
            for encoding in encodings:
                try:
                    # 尝试先读取文件的一小部分来检测编码
                    with open(file_path, 'rb') as f:
                        raw = f.read(1024)
                        if raw.startswith(b'\xff\xfe') or raw.startswith(b'\xfe\xff'):
                            # 检测到 UTF-16 码
                            return pd.read_csv(file_path, encoding='utf-16')
                    
                    # 尝试完整读取文件
                    df = pd.read_csv(
                        file_path, 
                        encoding=encoding,
                        dtype=str  # 将所有列都作为字符串读取
                    )
                    print(f"成功使用 {encoding} 编码读取文件")
                    return df
                except UnicodeDecodeError as e:
                    last_error = e
                    continue
                except Exception as e:
                    print(f"使用 {encoding} 编码读取文件时出错：{str(e)}")
                    last_error = e
                    continue
            
            # 如果所有编码都失败了，尝试使用二进制方式读取
            try:
                with open(file_path, 'rb') as f:
                    content = f.read()
                    # 尝试检测编码
                    import chardet
                    detected = chardet.detect(content)
                    if detected['encoding']:
                        return pd.read_csv(file_path, encoding=detected['encoding'])
            except Exception as e:
                last_error = e
            
            raise ValueError(f"无法使用已知编码方式读取文件，最后一次错误: {str(last_error)}")
        
        elif ext == '.txt':
            # 对txt文件使用相同的编码处理方式
            for encoding in encodings:
                try:
                    return pd.read_csv(file_path, delimiter='\t', encoding=encoding)
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    print(f"使用 {encoding} 编码读取文件时出错：{str(e)}")
                    continue
            raise ValueError(f"无法使用已知编码方式读取文件，请确保文件编码正确")
        else:
            raise ValueError("不支持的文件格式")

    def on_listbox_select(self, event=None):
        """处理列表框选择事件"""
        # 获取当前选择的列
        selected_columns1 = [self.columns1_listbox.get(i) for i in self.columns1_listbox.curselection()]
        selected_columns2 = [self.columns2_listbox.get(i) for i in self.columns2_listbox.curselection()]
        
        # 根据操作模式更新提示
        mode = self.operation_mode.get()
        if mode == "union":
            if len(selected_columns1) != len(selected_columns2):
                self.selection_hint.config(
                    text=f"已选择：文件1 ({len(selected_columns1)}列) - 文件2 ({len(selected_columns2)}列)，需要选择相同数量的列",
                    fg='#ff4444'
                )
            else:
                self.selection_hint.config(
                    text=f"已选择 {len(selected_columns1)} 列，两个文件将一一对应",
                    fg='#009900'
                )
        else:
            if len(selected_columns1) > 1 or len(selected_columns2) > 1:
                self.selection_hint.config(
                    text="交集和差集操作只能选择一列",
                    fg='#ff4444'
                )
            else:
                self.selection_hint.config(text="", fg='#666666')

    def on_operation_mode_change(self, *args):
        """当操作模式改变时调用"""
        mode = self.operation_mode.get()
        if mode == "union":
            # 并集模式：允许多选
            self.columns1_listbox.config(selectmode='multiple')
            self.columns2_listbox.config(selectmode='multiple')
        else:
            # 交集和差集模式：只允许单选
            self.columns1_listbox.config(selectmode='single')
            self.columns2_listbox.config(selectmode='single')
            # 清除多余的选择
            if len(self.columns1_listbox.curselection()) > 1:
                self.columns1_listbox.selection_clear(1, tk.END)
            if len(self.columns2_listbox.curselection()) > 1:
                self.columns2_listbox.selection_clear(1, tk.END)
        
        # 更新选择提示
        self.on_listbox_select()

    def start_matching(self):
        logging.info("点击开始处理按钮")
        if self.df1 is None or self.df2 is None or self.df1.empty or self.df2.empty:
            messagebox.showwarning("文件未选择", "请确保已选择并加载两个文件。")
            logging.warning("文件未选择或加载")
            return
        
        # 获取选中的列
        selected_indices1 = self.columns1_listbox.curselection()
        selected_indices2 = self.columns2_listbox.curselection()
        
        if not selected_indices1 or not selected_indices2:
            messagebox.showwarning("列未选择", "请至少选择一列用于处理。")
            logging.warning("处理列未选择")
            return
        
        # 获取选中的列名
        selected_columns1 = [self.columns1_listbox.get(i) for i in selected_indices1]
        selected_columns2 = [self.columns2_listbox.get(i) for i in selected_indices2]
        
        # 获取操作模式和处理模式
        operation_mode = self.operation_mode.get()
        match_mode = self.match_mode.get()
        deduplicate = self.deduplicate_var.get()
        
        try:
            if operation_mode == "union":
                # 打开字段匹配窗口
                self.create_field_mapping_window(selected_columns1, selected_columns2)
            else:
                # 交集和差集操作只允许选择一列
                if len(selected_columns1) > 1 or len(selected_columns2) > 1:
                    messagebox.showwarning("列选择错误", "交集和差集操作时，每个文件只能选择一列。")
                    return
                
                # 执行操作
                if operation_mode == "match":
                    result_df, count = match_data(self.df1, selected_columns1[0], self.df2, selected_columns2[0], 
                                                match_mode=match_mode, 
                                                deduplicate=deduplicate)
                    operation_name = "匹配"
                elif operation_mode == "a_minus_b":
                    result_df, count = subtract_data(self.df1, selected_columns1[0], self.df2, selected_columns2[0],
                                                   match_mode=match_mode,
                                                   deduplicate=deduplicate)
                    operation_name = "A-B相减"
                else:  # b_minus_a
                    result_df, count = subtract_data(self.df2, selected_columns2[0], self.df1, selected_columns1[0],
                                                   match_mode=match_mode,
                                                   deduplicate=deduplicate)
                    operation_name = "B-A相减"
                
                if count > 0:
                    # 打开字段选择窗口
                    self.create_result_field_selection_window(
                        result_df, 
                        operation_name,
                        count,
                        selected_columns1[0] if operation_mode != "b_minus_a" else selected_columns2[0],
                        self.df1.columns if operation_mode != "b_minus_a" else self.df2.columns,
                        self.df2.columns if operation_mode != "b_minus_a" else self.df1.columns
                    )
                else:
                    messagebox.showinfo("无结果", "没有找到任何结果。")
                    self.save_button.config(state=tk.DISABLED)
                    
        except Exception as e:
            logging.error(f"处理过程中出现错误: {e}")
            messagebox.showerror("处理错误", f"处理过程中出现错误: {e}")

    def create_field_mapping_window(self, columns1, columns2):
        """创建字段匹配窗口"""
        mapping_window = tk.Toplevel(self.root)
        mapping_window.title("字段匹配")
        mapping_window.geometry("600x500")
        
        # 创建说明标签
        tk.Label(mapping_window, 
                text="请选择要合并的字段对，每一行代表一对要合并的字段",
                font=self.style['font']).pack(pady=10)
        
        # 创建框架来容纳映射对
        mapping_frame = tk.Frame(mapping_window)
        mapping_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        # 创建标题
        tk.Label(mapping_frame, text="第一个文件字段", font=self.style['font']).grid(row=0, column=0, padx=5)
        tk.Label(mapping_frame, text="第二个文件字段", font=self.style['font']).grid(row=0, column=1, padx=5)
        
        # 存储所有的下拉框对
        self.mapping_pairs = []
        
        # 添加第一对映射下拉框
        self.add_mapping_pair(mapping_frame, columns1, columns2, 1)
        
        # 添加和删除按钮框架
        button_frame = tk.Frame(mapping_window)
        button_frame.pack(pady=10)
        
        # 添加新映射对的按钮
        tk.Button(button_frame, 
                 text="添加映射对",
                 command=lambda: self.add_mapping_pair(mapping_frame, columns1, columns2),
                 font=self.style['font'],
                 bg=self.style['button_bg'],
                 fg=self.style['button_fg']).pack(side=tk.LEFT, padx=5)
        
        # 删除最后一个映射对的按钮
        tk.Button(button_frame,
                 text="删除最后一对",
                 command=lambda: self.remove_last_mapping_pair(mapping_frame),
                 font=self.style['font'],
                 bg=self.style['button_bg'],
                 fg=self.style['button_fg']).pack(side=tk.LEFT, padx=5)
        
        # 确认按钮
        tk.Button(mapping_window,
                 text="确认并执行并集操作",
                 command=lambda: self.execute_union_with_mapping(mapping_window),
                 font=self.style['font'],
                 bg=self.style['button_bg'],
                 fg=self.style['button_fg']).pack(pady=20)

    def add_mapping_pair(self, frame, columns1, columns2, row_index=None):
        """添加一对字段映射下拉框"""
        if row_index is None:
            row_index = len(self.mapping_pairs) + 1
        
        # 创建一对下拉框
        var1 = tk.StringVar()
        var2 = tk.StringVar()
        
        dropdown1 = tk.OptionMenu(frame, var1, *columns1)
        dropdown2 = tk.OptionMenu(frame, var2, *columns2)
        
        # 设置下拉框样式
        dropdown1.config(font=self.style['font'])
        dropdown2.config(font=self.style['font'])
        
        # 放置下拉框
        dropdown1.grid(row=row_index, column=0, padx=5, pady=5)
        dropdown2.grid(row=row_index, column=1, padx=5, pady=5)
        
        # 如果是第一对，设置默认值
        if row_index == 1 and columns1 and columns2:
            var1.set(columns1[0])
            var2.set(columns2[0])
        
        # 保存这对映射
        self.mapping_pairs.append((var1, var2))

    def remove_last_mapping_pair(self, frame):
        """删除最后一对字段映射"""
        if len(self.mapping_pairs) > 1:  # 保持至少一对映射
            # 获取最后一对映射的变量
            var1, var2 = self.mapping_pairs[-1]
            
            # 从框架中删除下拉框
            for widget in frame.grid_slaves(row=len(self.mapping_pairs)):
                widget.destroy()
            
            # 从列表中移除这对映射
            self.mapping_pairs.pop()

    def execute_union_with_mapping(self, mapping_window):
        """执行带有字段映射的并集操作"""
        # 获取所有映射对
        mappings = [(var1.get(), var2.get()) for var1, var2 in self.mapping_pairs]
        
        # 检查是否有重复或空选择
        selected_cols1 = [col1 for col1, _ in mappings]
        selected_cols2 = [col2 for _, col2 in mappings]
        
        if len(set(selected_cols1)) != len(selected_cols1) or len(set(selected_cols2)) != len(selected_cols2):
            messagebox.showwarning("选择错误", "每个字段只能使用一次，请检查是否有重复选择。")
            return
        
        if '' in selected_cols1 or '' in selected_cols2:
            messagebox.showwarning("选择错误", "请确保所有字段都已选择。")
            return
        
        try:
            # 执行并集操作
            result_df, count = union_data(
                self.df1, selected_cols1,
                self.df2, selected_cols2,
                match_mode=self.match_mode.get(),
                deduplicate=self.deduplicate_var.get()
            )
            
            # 关闭映射窗口
            mapping_window.destroy()
            
            # 显示结果
            if count > 0:
                self.show_results(result_df, count, "并集")
            else:
                messagebox.showinfo("无结果", "没有找到任何结果。")
                self.save_button.config(state=tk.DISABLED)
                
        except Exception as e:
            logging.error(f"并集操作出错: {e}")
            messagebox.showerror("操作错误", f"并集操作出错: {e}")

    def show_results(self, result_df, count, operation_name):
        """显示结果窗口"""
        result_window = tk.Toplevel(self.root)
        mode_text = "精确" if self.match_mode.get() == "exact" else "模糊"
        dedup_text = "（去重）" if self.deduplicate_var.get() else ""
        result_window.title(f"{operation_name}{dedup_text}（{mode_text}）结果")
        
        # 使用文本框展示
        text = tk.Text(result_window, wrap='none')
        text.pack(expand=True, fill='both')
        
        # 插入DataFrame内容
        text.insert(tk.END, result_df.to_string(index=False, max_rows=1000))
        
        # 添加滚动条
        scrollbar_y = tk.Scrollbar(result_window, command=text.yview)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        text.configure(yscrollcommand=scrollbar_y.set)
        
        scrollbar_x = tk.Scrollbar(result_window, command=text.xview, orient='horizontal')
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        text.configure(xscrollcommand=scrollbar_x.set)
        
        # 显示计数
        count_text = "总数量" if operation_name in ["并集", "A-B相减", "B-A相减"] else "匹配总数"
        tk.Label(result_window, text=f"{count_text}: {count}").pack(pady=5)
        
        # 保存结果
        self.matched_df = result_df
        self.save_button.config(state=tk.NORMAL)

    def save_results_button(self):
        if hasattr(self, 'matched_df') and self.matched_df is not None and not self.matched_df.empty:
            save_results(self.matched_df)
        else:
            messagebox.showwarning("无数据", "没有匹配结果可保存。")
            logging.warning("尝试保存无数据的匹配结果")

    def create_result_field_selection_window(self, result_df, operation_name, count, key_column, columns1, columns2):
        """创建结果字段选择窗口"""
        selection_window = tk.Toplevel(self.root)
        selection_window.title("选择要显示的字段")
        selection_window.geometry("600x600")
        
        # 创建说明标签
        tk.Label(selection_window, 
                text="请选择要在结果中显示的字段（可以同时选择两个文件的字段）",
                font=self.style['font']).pack(pady=10)
        
        # 创建框架来容纳字段选择列表
        selection_frame = tk.Frame(selection_window)
        selection_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        # 创建两个列表框框架
        list_frame1 = tk.Frame(selection_frame)
        list_frame1.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        list_frame2 = tk.Frame(selection_frame)
        list_frame2.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # 第一个文件的字段列表
        tk.Label(list_frame1, text="第一个文件字段:", font=self.style['font']).pack()
        self.fields1_listbox = tk.Listbox(list_frame1, selectmode='multiple',
                                        width=30, height=15, font=self.style['font'],
                                        exportselection=0)  # 添加 exportselection=0
        self.fields1_listbox.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar1 = tk.Scrollbar(list_frame1)
        scrollbar1.pack(side=tk.RIGHT, fill=tk.Y)
        self.fields1_listbox.config(yscrollcommand=scrollbar1.set)
        scrollbar1.config(command=self.fields1_listbox.yview)
        
        # 第二个文件的字段列表
        tk.Label(list_frame2, text="第二个文件字段:", font=self.style['font']).pack()
        self.fields2_listbox = tk.Listbox(list_frame2, selectmode='multiple',
                                        width=30, height=15, font=self.style['font'],
                                        exportselection=0)  # 添加 exportselection=0
        self.fields2_listbox.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar2 = tk.Scrollbar(list_frame2)
        scrollbar2.pack(side=tk.RIGHT, fill=tk.Y)
        self.fields2_listbox.config(yscrollcommand=scrollbar2.set)
        scrollbar2.config(command=self.fields2_listbox.yview)
        
        # 填充列表框
        for col in columns1:
            if col != key_column:  # 不显示已经在结果中的关键列
                self.fields1_listbox.insert(tk.END, col)
        
        for col in columns2:
            if col != key_column:  # 不显示已经在结果中的关键列
                self.fields2_listbox.insert(tk.END, col)
        
        # 确认按钮
        tk.Button(selection_window,
                 text="确认选择",
                 command=lambda: self.process_field_selection(
                     selection_window, result_df, operation_name, count, key_column
                 ),
                 font=self.style['font'],
                 bg=self.style['button_bg'],
                 fg=self.style['button_fg']).pack(pady=20)

    def process_field_selection(self, window, result_df, operation_name, count, key_column):
        """处理字段选择并显示最终结果"""
        # 获取选中的字段
        selected_fields1 = [self.fields1_listbox.get(i) for i in self.fields1_listbox.curselection()]
        selected_fields2 = [self.fields2_listbox.get(i) for i in self.fields2_listbox.curselection()]
        
        try:
            # 复制结果DataFrame
            final_result = result_df.copy()
            
            # 确保key_column的数据类型一致性
            if key_column in final_result.columns:
                # 将key_column转换为字符串类型
                final_result[key_column] = final_result[key_column].fillna('').astype(str)
            
            # 合并选中的字段
            if selected_fields1:
                # 创建临时DataFrame，避免修改原始数据
                df1_temp = self.df1.copy()
                # 确保key_column的数据类型一致
                df1_temp[key_column] = df1_temp[key_column].fillna('').astype(str)
                
                # 使用merge合并数据
                final_result = pd.merge(
                    final_result,
                    df1_temp[selected_fields1 + [key_column]],
                    on=key_column,
                    how='left'
                )
            
            if selected_fields2:
                # 创建临时DataFrame，避免修改原始数据
                df2_temp = self.df2.copy()
                
                # 确保所有列的数据类型一致性
                for col in selected_fields2:
                    df2_temp[col] = df2_temp[col].fillna('').astype(str)
                
                # 重命名第二个文件的列，避免列名冲突
                rename_dict = {col: f"{col}_file2" for col in selected_fields2}
                df2_temp = df2_temp.rename(columns=rename_dict)
                selected_fields2_renamed = [rename_dict[col] for col in selected_fields2]
                
                # 确保关键列的数据类型一致
                key_column_file2 = f"{key_column}(文件2)"  # 使用重命名后的key_column
                if key_column in self.df2.columns:
                    df2_temp[key_column] = self.df2[key_column].fillna('').astype(str)
                    # 使用相同的key_column进行合并
                    final_result = pd.merge(
                        final_result,
                        df2_temp[selected_fields2_renamed + [key_column]],
                        left_on=key_column_file2 if key_column_file2 in final_result.columns else key_column,
                        right_on=key_column,
                        how='left'
                    )
                else:
                    # 使用第一个选择的字段作为合并键
                    original_key = selected_fields2[0]
                    df2_temp[original_key] = self.df2[original_key].fillna('').astype(str)
                    final_result = pd.merge(
                        final_result,
                        df2_temp[selected_fields2_renamed + [original_key]],
                        left_on=key_column_file2 if key_column_file2 in final_result.columns else key_column,
                        right_on=original_key,
                        how='left'
                    )
                
                # 重命名第二个文件的列，添加文件标识
                rename_back = {f"{col}_file2": f"{col}(文件2)" for col in selected_fields2}
                final_result = final_result.rename(columns=rename_back)
                
                # 删除可能的重复列
                cols_to_drop = [col for col in final_result.columns 
                              if col.endswith('_file2') or 
                              (col != key_column and col in selected_fields2)]
                final_result = final_result.drop(columns=cols_to_drop, errors='ignore')
            
            # 关闭选择窗口
            window.destroy()
            
            # 显示结果
            self.show_results(final_result, count, operation_name)
            
        except Exception as e:
            logging.error(f"处理字段选择时出错: {e}")
            messagebox.showerror("处理错误", f"处理字段选择时出错: {e}")

# 运行应用程序
if __name__ == "__main__":
    try:
        logging.info("应用程序启动")
        root = tk.Tk()
        app = DataMatcherApp(root)
        root.mainloop()
        logging.info("应用程序正常退出")
    except Exception as e:
        logging.critical(f"应用程序崩溃: {e}")
