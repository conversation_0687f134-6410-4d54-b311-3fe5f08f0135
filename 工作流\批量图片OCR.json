{"name": "批量图片OCR", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "4b099698-6f11-4974-a649-6aee1f83c4c2", "name": "When clicking ‘Test workflow’"}, {"parameters": {"fileSelector": "/picture/**", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [220, 0], "id": "da5baf71-814a-4f21-819a-36e2408b32f7", "name": "Read/Write Files from Disk"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [440, 0], "id": "619118f7-54b6-48ba-8122-51969b2b89bb", "name": "Loop Over Items"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "name": "Replace Me", "typeVersion": 1, "position": [1600, 20], "id": "26444b2b-a2ea-41b7-b12f-48035b61e8ae"}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [680, 20], "id": "f348cc1b-fa23-464b-a112-f3efa725b88b", "name": "Extract from File"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyD7qOlQ8ypC0dxbu8SN3f6ExZrvE_YoSpA", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\"text\": \"提取图中的内容并保持原有格式输出\"},\n        {\n          \"inline_data\": {\n            \"mime_type\": \"image/jpeg\",\n            \"data\": \"{{ $json.data }}\"\n          }\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 65536\n  }\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [880, 20], "id": "20eacab7-19ed-4a97-a5f6-590e953c5177", "name": "HTTP Request"}, {"parameters": {"operation": "toText", "sourceProperty": "candidates[0].content.parts[0].text", "options": {"encoding": "utf8"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1100, 20], "id": "b24ae0f6-d78d-47f3-8511-3abeb66a6a1a", "name": "Convert to File"}, {"parameters": {"operation": "write", "fileName": "=/home/<USER>/{{ $('Loop Over Items').item.json.fileName.split(\".\")[0] }}.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1320, 20], "id": "c0f0be58-f7c5-4e7f-a0a7-6559c2b27b76", "name": "Read/Write Files from Disk1"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Extract from File", "type": "main", "index": 0}]]}, "Replace Me": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "Replace Me", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "64f61c17-9dce-44c2-b1c1-3336b7dcc07e", "meta": {"instanceId": "da293d8bc9d55da5a991575f3ab795aa37c2d9ef15407feeb5edfc2322d6467d"}, "id": "KHBNb3S4liXFhjHk", "tags": []}