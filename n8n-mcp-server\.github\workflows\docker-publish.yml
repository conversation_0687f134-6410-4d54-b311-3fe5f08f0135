name: Build & Publish Docker image

# ➊ Triggers – push to main OR new GitHub Release
on:
  push:
    branches: [main]
  release:
    types: [published]

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    # ➋ The job needs these repo secrets (add in Settings ▸ Secrets ▸ Actions):
    #    DOCKERHUB_USERNAME  – e.g. 'leonardsellem'
    #    DOCKERHUB_TOKEN     – a Docker Hub access token / password
    env:
      IMAGE_NAME: ${{ secrets.DOCKERHUB_USERNAME }}/n8n-mcp-server

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      # ➌ Enable multi-arch builds (amd64 + arm64)
      - uses: docker/setup-qemu-action@v3
      - uses: docker/setup-buildx-action@v3

      # ➍ Log in to Docker Hub
      - uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # ➎ Generate convenient tags & labels (latest, sha-short, release tag …)
      - id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            type=sha,format=short
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=raw,value=latest,enable={{is_default_branch}}

      # ➏ Build & push
      - uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          # optional build-cache (makes repeated builds faster)
          cache-from: type=registry,ref=${{ env.IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ env.IMAGE_NAME }}:buildcache,mode=max