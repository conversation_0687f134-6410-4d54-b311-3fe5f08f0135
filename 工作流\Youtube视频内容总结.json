{"name": "Youtube视频内容总结", "nodes": [{"parameters": {"content": "# large file Understanding with thinking - audio, documents, video\n", "height": 80, "width": 3260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "765556e0-536c-4981-8f25-41f8acbcf349", "name": "Sticky Note (Form Trigger)15"}, {"parameters": {"content": "# Workflow", "height": 280, "width": 3260, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 100], "id": "4482d3cc-c671-4685-a2c8-5467eff1f466", "name": "Sticky Note (Form Trigger)16"}, {"parameters": {"formTitle": "Use youtube data with Gemini", "formDescription": "Enter youtube url to utilize the video data", "formFields": {"values": [{"fieldLabel": "model", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "gemini-2.5-flash-preview-05-20"}, {"option": "gemini-2.5-flash-preview-04-17"}, {"option": "gemini-2.0-flash"}]}}, {"fieldLabel": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "number", "placeholder": "0-24576"}, {"fieldLabel": "youtubeLink"}, {"fieldLabel": "prompt"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [140, 180], "id": "0ae6fcc3-3a76-44c8-b4bd-5e1aea802726", "name": "On form submission", "webhookId": "10224bf5-4491-4c3b-9785-32a4885d8950"}, {"parameters": {"assignments": {"assignments": [{"id": "efc87b7e-c3a4-47e0-8290-2f97398e5531", "name": "model", "value": "={{ $json.model }}", "type": "string"}, {"id": "dd29755c-cda9-4756-8c05-6697264f5e2d", "name": "<PERSON><PERSON><PERSON><PERSON>", "value": "={{ $if($json && $json.thinkingBudget, $json.thinkingBudget, 0) }}", "type": "number"}, {"id": "5e55d5ad-9e65-44bc-a111-f6b0197ba8c3", "name": "youtubeLink", "value": "={{ $json.youtubeLink }}", "type": "string"}, {"id": "39ac7e8e-ae5e-4879-9f9c-e0d99f2fa0bc", "name": "prompt", "value": "={{ $json.prompt }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 180], "id": "08885c5e-e4ad-4708-9b59-305c27424d31", "name": "<PERSON>"}, {"parameters": {"method": "POST", "url": "=https://generativelanguage.googleapis.com/v1beta/models/{{ $('Edit Fields').item.json.model }}:generateContent ", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"{{ $('Edit Fields').item.json.prompt }}. Output according to all input files. List all files that you have access.\"\n        },\n        {\n          \"file_data\": {\n            \"file_uri\": \"{{ $('Edit Fields').item.json.youtubeLink }}\"\n          }\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"thinkingConfig\": {\n      \"thinkingBudget\": {{ $('Edit Fields').item.json.thinkingBudget }}\n    }\n  }\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 180], "id": "8b583b44-cb73-41a7-830f-15583c26c7bf", "name": "HTTP Request16"}, {"parameters": {"operation": "toText", "sourceProperty": "candidates[0].content.parts[0].text", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1020, 180], "id": "6e34fef5-976b-44b2-99c6-69d92f17ac74", "name": "Convert to File"}], "pinData": {}, "connections": {"On form submission": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "HTTP Request16", "type": "main", "index": 0}]]}, "HTTP Request16": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "db8a9a42-f068-46cb-9169-2739dfe7a21e", "meta": {"instanceId": "04695fa805d662a7ca811a5d9568e6cc080c07e6ec9f81360f8146eccb701cc6"}, "id": "qNqSiI0P5B07kvLN", "tags": []}