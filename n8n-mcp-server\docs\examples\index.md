# Usage Examples

This section provides practical examples of using the n8n MCP Server with AI assistants.

## Overview

The examples in this section demonstrate how AI assistants can interact with n8n workflows through the MCP server. They range from basic operations to complex integration scenarios.

## Examples Categories

- [Basic Examples](./basic-examples.md): Simple examples covering fundamental operations like listing workflows, retrieving workflow details, and executing workflows.
- [Advanced Scenarios](./advanced-scenarios.md): More complex examples showing how to chain operations, handle errors, and implement common workflow patterns.
- [Integration Examples](./integration-examples.md): Examples of integrating the n8n MCP Server with different AI assistant platforms and other tools.

## How to Use These Examples

The examples in this section show both:

1. **User Prompts**: What a user might ask an AI assistant to do
2. **Assistant Actions**: How the assistant would use the MCP tools and resources to accomplish the task

You can use these examples as inspiration for your own interactions with the n8n MCP Server or as templates for building more complex workflows.
