{"name": "风险闭环", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "afb92d3c-12a6-4a48-9c4c-4b9baba7d8d8", "name": "When chat message received", "webhookId": "d542bc47-61f9-4d21-8aae-9ff765dfffbe"}, {"parameters": {"options": {"systemMessage": "={{ $json.chatInput }}中的内容是针对公安工作中的风险点提出的具体对策建议。请你首先根据对策建议中的内容，判断需要公安机关中的刑侦、治安、网安、政保、巡特警、宣教等哪些警种部门参与工作，并以“各地收到风险提示后，指令治安、刑侦、宣传……等部门及各辖区派出所开展工作。”为例开头，紧接着对{{ $json.chatInput }}中的内容进行缩写，去掉一是、二是、三是……，总字数控制在110-150字。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [220, 0], "id": "bda45a7c-9c44-4f3f-b1a5-7137a283fe6b", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "hunyuan-turbo-latest", "mode": "list", "cachedResultName": "hunyuan-turbo-latest"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [180, 240], "id": "f0a9468e-a205-4f66-b567-9af03877eb83", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "qKk3EXyKItVPefaK", "name": "腾讯混元"}}}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d5c91dac-abdb-40fa-8899-33013e15a3a5", "meta": {"instanceId": "e91fe71fc41c70f3cd8d49a32329efea986f840395719f4b74ff7ced73716ab0"}, "id": "OF65bxqbKbjIcWPH", "tags": []}