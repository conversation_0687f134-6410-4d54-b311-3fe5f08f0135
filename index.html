<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赛博朋克贪吃蛇</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="neon-text">赛博朋克贪吃蛇</h1>
            <div class="score-container">
                <span class="score-label">分数:</span>
                <span id="score" class="score-value">0</span>
            </div>
        </div>
        <canvas id="gameCanvas"></canvas>
        <div class="controls">
            <button id="startBtn" class="cyber-button">开始游戏</button>
            <button id="restartBtn" class="cyber-button" style="display: none;">重新开始</button>
        </div>
        <div class="game-over" id="gameOver">
            <h2 class="neon-text">游戏结束</h2>
            <p>最终分数: <span id="finalScore">0</span></p>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>