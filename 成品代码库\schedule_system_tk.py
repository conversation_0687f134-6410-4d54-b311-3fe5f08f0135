import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
import json
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import sqlite3
import pandas as pd

class DateEntry(ttk.Frame):
    def __init__(self, master, **kwargs):
        super().__init__(master, **kwargs)
        
        # 年份选择
        ttk.Label(self, text="年:").pack(side=tk.LEFT, padx=5)
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        self.year_entry = ttk.Spinbox(self, from_=2000, to=2100, width=6,
                                    textvariable=self.year_var)
        self.year_entry.pack(side=tk.LEFT, padx=5)
        
        # 月份选择
        ttk.Label(self, text="月:").pack(side=tk.LEFT, padx=5)
        self.month_var = tk.StringVar(value=str(datetime.now().month))
        self.month_entry = ttk.Spinbox(self, from_=1, to=12, width=4,
                                     textvariable=self.month_var)
        self.month_entry.pack(side=tk.LEFT, padx=5)
        
        # 日期选择
        ttk.Label(self, text="日:").pack(side=tk.LEFT, padx=5)
        self.day_var = tk.StringVar(value=str(datetime.now().day))
        self.day_entry = ttk.Spinbox(self, from_=1, to=31, width=4,
                                   textvariable=self.day_var)
        self.day_entry.pack(side=tk.LEFT, padx=5)
    
    def get_date(self):
        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())
            day = int(self.day_var.get())
            return f"{year:04d}-{month:02d}-{day:02d}"
        except ValueError:
            return None

class ScheduleDatabase:
    def __init__(self):
        self.db_path = 'schedule.db'
        self.init_database()

    def init_database(self):
        """初始化数据库，如果表结构有变化则重建数据库"""
        import os
        
        # 如果数据库文件存在，检查是否可写
        if os.path.exists(self.db_path):
            try:
                # 尝试写入权限
                with open(self.db_path, 'a'):
                    pass
            except PermissionError:
                # 如果没有写入权限，尝试删除文件
                try:
                    os.chmod(self.db_path, 0o666)  # 设置文件权限为可读写
                except:
                    # 如果无法修改权限，尝试删除文件
                    try:
                        os.remove(self.db_path)
                    except:
                        messagebox.showerror("错误", "无法访问数据库文件，请确保程序有足够的权限或手动删除schedule.db文件")
                        return
        
        try:
            # 创建新的数据库连接
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(schedules)")
            columns = [column[1] for column in cursor.fetchall()]
            
            # 如果表不存在或没有completed列，重新创建表
            if 'completed' not in columns:
                # 删除旧表
                cursor.execute("DROP TABLE IF EXISTS schedules")
                
                # 创建新表
                cursor.execute('''
                    CREATE TABLE schedules (
                        date TEXT,
                        battalion INTEGER,
                        duty_type INTEGER,
                        mode TEXT,
                        completed INTEGER DEFAULT 0,
                        PRIMARY KEY (date, battalion)
                    )
                ''')
            
            # 创建节假日表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS holidays (
                    date TEXT PRIMARY KEY,
                    holiday_type TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            # 设置数据库文件权限
            try:
                os.chmod(self.db_path, 0o666)
            except:
                pass
                
        except sqlite3.Error as e:
            messagebox.showerror("数据库错误", f"初始化数据库时出错：{str(e)}\n请确保程序有足够的权限或手动删除schedule.db文件")
        except Exception as e:
            messagebox.showerror("错误", f"发生未知错误：{str(e)}\n请确保程序有足够的权限或手动删除schedule.db文件")

    def save_schedule(self, date, battalion, duty_type, mode, completed=0):
        """保存排班信息，包括已值班标记"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO schedules (date, battalion, duty_type, mode, completed)
            VALUES (?, ?, ?, ?, ?)
        ''', (date, battalion, duty_type, mode, completed))
        conn.commit()
        conn.close()

    def get_schedule(self, date):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT battalion, duty_type, mode, completed 
            FROM schedules 
            WHERE date = ?
        ''', (date,))
        result = cursor.fetchall()
        conn.close()
        return result

    def get_schedule_range(self, start_date, end_date):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT date, battalion, duty_type, mode, completed 
            FROM schedules 
            WHERE date BETWEEN ? AND ?
            ORDER BY date, battalion
        ''', (start_date, end_date))
        result = cursor.fetchall()
        conn.close()
        return result

    def get_battalion_stats(self, start_date=None, end_date=None, completed_only=False):
        """获取大队统计信息，可选择只统计已完成的值班"""
        conn = sqlite3.connect(self.db_path)
        query = '''
            SELECT battalion, duty_type, COUNT(*) as count
            FROM schedules
            WHERE 1=1
        '''
        if completed_only:
            query += " AND completed = 1"
        if start_date and end_date:
            query += f" AND date BETWEEN '{start_date}' AND '{end_date}'"
        query += " GROUP BY battalion, duty_type"
        
        stats = pd.read_sql_query(query, conn)
        conn.close()
        return stats

    def save_holiday(self, date, holiday_type):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO holidays (date, holiday_type)
            VALUES (?, ?)
        ''', (date, holiday_type))
        conn.commit()
        conn.close()

    def get_holiday_type(self, date):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT holiday_type FROM holidays WHERE date = ?', (date,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None

    def delete_schedule(self, date):
        """删除指定日期的所有排班"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('DELETE FROM schedules WHERE date = ?', (date,))
        conn.commit()
        conn.close()

    def delete_schedule_range(self, start_date, end_date):
        """删除指定日期范围内的所有排班"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('DELETE FROM schedules WHERE date BETWEEN ? AND ?', 
                      (start_date, end_date))
        conn.commit()
        conn.close()

    def update_completed_status(self, date, battalion, completed):
        """更新值班完成状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE schedules 
            SET completed = ? 
            WHERE date = ? AND battalion = ?
        ''', (completed, date, battalion))
        conn.commit()
        conn.close()

class HolidayManager:
    def __init__(self, db):
        self.db = db
        self.update_holidays()

    def update_holidays(self):
        try:
            # 获取当前年份
            current_year = datetime.now().year
            
            # 2024年法定节假日安排（保持不变）
            holidays_2024 = {
                # 元旦
                "2024-01-01": "元旦节",
                # 春节
                "2024-02-10": "春节",
                "2024-02-11": "春节",
                "2024-02-12": "春节",
                "2024-02-13": "春节",
                "2024-02-14": "春节",
                "2024-02-15": "春节",
                "2024-02-16": "春节",
                "2024-02-17": "春节",
                # 清明节
                "2024-04-04": "清明节",
                "2024-04-05": "清明节",
                "2024-04-06": "清明节",
                # 劳动节
                "2024-05-01": "劳动节",
                "2024-05-02": "劳动节",
                "2024-05-03": "劳动节",
                "2024-05-04": "劳动节",
                "2024-05-05": "劳动节",
                # 端午节
                "2024-06-10": "端午节",
                # 中秋节
                "2024-09-15": "中秋节",
                "2024-09-16": "中秋节",
                "2024-09-17": "中秋节",
                # 国庆节
                "2024-10-01": "国庆节",
                "2024-10-02": "国庆节",
                "2024-10-03": "国庆节",
                "2024-10-04": "国庆节",
                "2024-10-05": "国庆节",
                "2024-10-06": "国庆节",
                "2024-10-07": "国庆节",
            }
            
            # 2025年法定节假日安排（根据最新通知更新）
            holidays_2025 = {
                # 元旦：1月1日放假1天
                "2025-01-01": "元旦节",
                
                # 春节：1月28日至2月4日放假8天
                "2025-01-28": "春节",  # 除夕
                "2025-01-29": "春节",  # 初一
                "2025-01-30": "春节",  # 初二
                "2025-01-31": "春节",  # 初三
                "2025-02-01": "春节",  # 初四
                "2025-02-02": "春节",  # 初五
                "2025-02-03": "春节",  # 初六
                "2025-02-04": "春节",  # 初七
                
                # 清明节：4月4日至6日放假3天
                "2025-04-04": "清明节",
                "2025-04-05": "清明节",
                "2025-04-06": "清明节",
                
                # 劳动节：5月1日至5日放假5天
                "2025-05-01": "劳动节",
                "2025-05-02": "劳动节",
                "2025-05-03": "劳动节",
                "2025-05-04": "劳动节",
                "2025-05-05": "劳动节",
                
                # 端午节：5月31日至6月2日放假3天
                "2025-05-31": "端午节",
                "2025-06-01": "端午节",
                "2025-06-02": "端午节",
                
                # 中秋节、国庆节：10月1日至8日放假8天
                "2025-10-01": "国庆节、中秋节",
                "2025-10-02": "国庆节、中秋节",
                "2025-10-03": "国庆节、中秋节",
                "2025-10-04": "国庆节、中秋节",
                "2025-10-05": "国庆节、中秋节",
                "2025-10-06": "国庆节、中秋节",
                "2025-10-07": "国庆节、中秋节",
                "2025-10-08": "国庆节、中秋节",
            }
            
            # 2025年调休上班日期（这些日期虽然是周末，但要上班）
            workdays_2025 = {
                "2025-01-26",  # 春节调休
                "2025-02-08",  # 春节调休
                "2025-04-27",  # 劳动节调休
                "2025-09-28",  # 国庆节调休
                "2025-10-11",  # 国庆节调休
            }
            
            # 根据当前年份选择节假日数据
            holidays = holidays_2024 if current_year == 2024 else holidays_2025
            
            # 保存节假日数据
            for date, holiday_type in holidays.items():
                self.db.save_holiday(date, holiday_type)
            
            # 添加周末信息（排除调休上班的周末）
            start_date = datetime(current_year, 1, 1)
            end_date = datetime(current_year, 12, 31)
            current_date = start_date
            
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                # 如果是周末且不是调休上班日，则标记为普通周末
                if current_date.weekday() >= 5 and date_str not in workdays_2025:
                    if not self.db.get_holiday_type(date_str):
                        self.db.save_holiday(date_str, '普通周末')
                current_date += timedelta(days=1)
                
        except Exception as e:
            messagebox.showerror("错误", f"更新节假日数据时出错: {str(e)}")

class ScheduleSystem:
    def __init__(self):
        self.db = ScheduleDatabase()
        self.holiday_manager = HolidayManager(self.db)
        self.last_battalion = 1  # 记录上次排班的大队号，用于顺序排班
        
    def get_duty_type(self, date, is_a_post, current_mode):
        holiday_type = self.db.get_holiday_type(date)
        
        if current_mode != '日常':
            # 特殊勤务期间的值班类型
            if is_a_post:
                if holiday_type and '节' in holiday_type:
                    return 2  # 默认为类型2，可手动改为类型1
                elif holiday_type == '普通周末':
                    return 4
                else:
                    return 7
            else:  # B岗
                if holiday_type and '节' in holiday_type:
                    return 3
                elif holiday_type == '普通周末':
                    return 5
                else:
                    return 8
        else:
            # 日常勤务期间的值班类型
            if holiday_type and '节' in holiday_type:
                return 2 if is_a_post else 3  # A岗默认为类型2，可手动改为类型1
            elif holiday_type == '普通周末':
                return 6  # 普通周末统一使用类型6
            else:
                return 7 if is_a_post else 8

    def check_consecutive_duty(self, battalion, date):
        """检查指定大队在指定日期前后是否有任何类型的值班安排
        
        Args:
            battalion: 大队编号
            date: 日期字符串，格式为'%Y-%m-%d'
            
        Returns:
            bool: 如果前一天或后一天有该大队的任何类型值班安排，返回True；否则返回False
        """
        # 获取前一天和后一天的日期
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        prev_date = (date_obj - timedelta(days=1)).strftime('%Y-%m-%d')
        next_date = (date_obj + timedelta(days=1)).strftime('%Y-%m-%d')
        
        # 检查前一天的排班
        prev_schedule = self.db.get_schedule(prev_date)
        for schedule in prev_schedule:
            if schedule[0] == battalion:  # 如果找到同一个大队的任何类型值班
                return True
                
        # 检查后一天的排班
        next_schedule = self.db.get_schedule(next_date)
        for schedule in next_schedule:
            if schedule[0] == battalion:  # 如果找到同一个大队的任何类型值班
                return True
                
        return False

    def get_duty_counts_by_type(self, battalion, end_date=None):
        """获取指定大队各类型值班的次数"""
        stats = self.db.get_battalion_stats(None, end_date)
        counts = {i: 0 for i in range(1, 9)}  # 初始化所有类型的计数为0
        
        battalion_stats = stats[stats['battalion'] == battalion]
        for _, row in battalion_stats.iterrows():
            counts[row['duty_type']] = row['count']
        
        return counts

    def get_patrol_count(self, battalion, end_date=None):
        """获取指定大队的巡逻班次总数"""
        counts = self.get_duty_counts_by_type(battalion, end_date)
        return sum(counts[t] for t in [1, 2, 4, 6, 7])

    def find_best_battalion_for_date(self, date, available_battalions, is_patrol=True, duty_type=None):
        """找到最适合的大队（确保各类型值班次数和总值班次数的平衡）"""
        if not duty_type:
            return available_battalions[0]  # 如果没有指定值班类型，返回第一个可用大队
        
        # 首先过滤掉会造成连续值班的大队
        non_consecutive_battalions = [b for b in available_battalions 
                                    if not self.check_consecutive_duty(b, date)]
        
        # 如果没有可用的大队（所有大队都会造成连续值班），返回None
        if not non_consecutive_battalions:
            return None
        
        # 获取所有大队的各类型值班次数
        battalion_stats = {}
        total_duties = {}  # 每个大队的总值班次数
        type_duties = {}   # 每个大队当前类型的值班次数
        
        for battalion in non_consecutive_battalions:
            stats = self.get_duty_counts_by_type(battalion, date)
            battalion_stats[battalion] = stats
            total_duties[battalion] = sum(stats.values())
            type_duties[battalion] = stats[duty_type]
        
        # 首先选择在当前类型值班次数最少的大队
        min_type_count = min(type_duties.values())
        min_type_battalions = [b for b, count in type_duties.items() if count == min_type_count]
        
        # 如果当前类型最少的大队有多个，从中选择总值班次数最少的
        if len(min_type_battalions) > 1:
            min_total = float('inf')
            best_battalions = []
            for battalion in min_type_battalions:
                total = total_duties[battalion]
                if total < min_total:
                    min_total = total
                    best_battalions = [battalion]
                elif total == min_total:
                    best_battalions.append(battalion)
            return best_battalions[0]
        
        # 如果只有一个当前类型最少的大队
        if min_type_battalions:
            return min_type_battalions[0]
        
        # 如果没有找到合适的大队，返回None
        return None

    def get_next_battalion(self, available_battalions, date_str, duty_type):
        """获取下一个要排班的大队
        确保各类型值班次数和总值班次数的平衡"""
        # 获取所有大队的值班统计
        stats = self.db.get_battalion_stats(None, date_str)
        
        # 首先过滤掉会造成连续值班的大队
        non_consecutive_battalions = [b for b in available_battalions 
                                    if not self.check_consecutive_duty(b, date_str)]
        
        # 如果没有可用的大队（所有大队都会造成连续值班），尝试调整之前的排班
        if not non_consecutive_battalions:
            return None
        
        # 如果没有统计数据，从不会造成连续值班的大队中按顺序返回
        if stats.empty:
            for i in range(self.last_battalion, 13):
                if i in non_consecutive_battalions:
                    self.last_battalion = i
                    return i
            # 如果没有找到，从头开始找
            for i in range(1, self.last_battalion):
                if i in non_consecutive_battalions:
                    self.last_battalion = i
                    return i
            return None
        
        # 计算每个大队的总值班次数和当前类型值班次数
        total_counts = {}
        type_counts = {}
        for battalion in range(1, 13):
            battalion_stats = stats[stats['battalion'] == battalion]
            total_counts[battalion] = battalion_stats['count'].sum() if not battalion_stats.empty else 0
            type_counts[battalion] = battalion_stats[battalion_stats['duty_type'] == duty_type]['count'].sum() if not battalion_stats.empty else 0
        
        # 首先选择在当前类型值班次数最少的大队
        min_type_count = min(type_counts[b] for b in non_consecutive_battalions)
        min_type_battalions = [b for b in non_consecutive_battalions if type_counts[b] == min_type_count]
        
        # 如果当前类型最少的大队有多个，从中选择总值班次数最少的
        if len(min_type_battalions) > 1:
            min_total = float('inf')
            best_battalions = []
            for battalion in min_type_battalions:
                total = total_counts[battalion]
                if total < min_total:
                    min_total = total
                    best_battalions = [battalion]
                elif total == min_total:
                    best_battalions.append(battalion)
            self.last_battalion = best_battalions[0]
            return best_battalions[0]
        
        # 如果只有一个当前类型最少的大队
        if min_type_battalions:
            self.last_battalion = min_type_battalions[0]
            return min_type_battalions[0]
        
        # 如果没有找到合适的大队，返回None
        return None

    def initialize_year_schedule(self, start_date):
        """初始化年度排班"""
        current_date = start_date
        year_end = datetime(start_date.year, 12, 31)
        
        # 清除现有排班
        self.db.delete_schedule_range(
            start_date.strftime('%Y-%m-%d'),
            year_end.strftime('%Y-%m-%d')
        )
        
        # 重置大队号为1，从头开始排班
        self.last_battalion = 1
        
        # 生成初始排班
        while current_date <= year_end:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 获取值班类型
            duty_type = self.get_duty_type(date_str, True, '日常')
            
            # 按顺序选择大队
            available_battalions = list(range(1, 13))
            battalion = self.get_next_battalion(available_battalions, date_str, duty_type)
            
            # 如果找不到合适的大队（会造成连续值班），尝试调整前一天的排班
            if battalion is None:
                # 获取前一天的日期
                prev_date = (current_date - timedelta(days=1)).strftime('%Y-%m-%d')
                # 删除前一天的排班
                self.db.delete_schedule(prev_date)
                # 重新排前一天的班
                prev_duty_type = self.get_duty_type(prev_date, True, '日常')
                prev_battalion = self.get_next_battalion(available_battalions, prev_date, prev_duty_type)
                if prev_battalion:
                    self.db.save_schedule(prev_date, prev_battalion, prev_duty_type, '日常', completed=0)
                # 再次尝试为当前日期排班
                battalion = self.get_next_battalion(available_battalions, date_str, duty_type)
            
            if battalion:
                # 添加completed=0表示未值班
                self.db.save_schedule(date_str, battalion, duty_type, '日常', completed=0)
            
            current_date += timedelta(days=1)
        
        return "年度排班初始化完成！"

    def add_special_duty(self, start_date, end_date, mode):
        """添加特殊勤务排班并重新平衡"""
        current_date = start_date
        year_end = datetime(start_date.year, 12, 31)
        
        # 设置A岗和B岗数量
        if mode == '一级勤务':
            a_post_count, b_post_count = 3, 6
        elif mode == '二级勤务':
            a_post_count, b_post_count = 2, 4
        else:  # 三级勤务
            a_post_count, b_post_count = 1, 2
        
        # 删除特殊勤务期间的原有排班（保留已完成值班）
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            existing_schedules = self.db.get_schedule(date_str)
            
            # 保留已完成值班的记录
            completed_schedules = [schedule for schedule in existing_schedules if schedule[3] == 1]
            
            # 如果有已完成值班，跳过这一天
            if completed_schedules:
                current_date += timedelta(days=1)
                continue
                
            # 删除未完成值班的记录
            self.db.delete_schedule(date_str)
            current_date += timedelta(days=1)
        
        # 重置current_date用于添加特殊勤务
        current_date = start_date
        
        # 添加特殊勤务排班
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            existing_schedules = self.db.get_schedule(date_str)
            
            # 如果这一天有已完成值班，跳过
            if any(schedule[3] == 1 for schedule in existing_schedules):
                current_date += timedelta(days=1)
                continue
                
            available_battalions = list(range(1, 13))
            assigned_battalions = []
            
            # 优先分配A岗
            a_posts_assigned = 0
            for _ in range(a_post_count):
                # 过滤掉已分配的大队
                available = [b for b in available_battalions if b not in assigned_battalions]
                if not available:
                    break
                
                # 尝试所有可用大队，直到找到一个合适的
                battalion = None
                for b in available:
                    if not self.check_consecutive_duty(b, date_str):
                        battalion = b
                        break
                
                if battalion is not None:
                    duty_type = self.get_duty_type(date_str, True, mode)
                    self.db.save_schedule(date_str, battalion, duty_type, mode, completed=0)
                    assigned_battalions.append(battalion)
                    a_posts_assigned += 1
            
            # 分配B岗
            b_posts_assigned = 0
            for _ in range(b_post_count):
                # 过滤掉已分配的大队
                available = [b for b in available_battalions if b not in assigned_battalions]
                if not available:
                    break
                
                # 尝试所有可用大队，直到找到一个合适的
                battalion = None
                for b in available:
                    if not self.check_consecutive_duty(b, date_str):
                        battalion = b
                        break
                
                if battalion is not None:
                    duty_type = self.get_duty_type(date_str, False, mode)
                    self.db.save_schedule(date_str, battalion, duty_type, mode, completed=0)
                    assigned_battalions.append(battalion)
                    b_posts_assigned += 1
            
            # 如果当天没有成功分配任何岗位，至少安排一个值班
            if not assigned_battalions:
                duty_type = self.get_duty_type(date_str, True, mode)  # 默认安排A岗
                # 尝试所有可用大队，直到找到一个可以值班的
                for battalion in available_battalions:
                    if not self.check_consecutive_duty(battalion, date_str):
                        self.db.save_schedule(date_str, battalion, duty_type, mode, completed=0)
                        assigned_battalions.append(battalion)
                        break
                # 如果所有大队都会造成连续值班，也要安排一个
                if not assigned_battalions:
                    battalion = available_battalions[0]
                    self.db.save_schedule(date_str, battalion, duty_type, mode, completed=0)
            
            # 如果分配的岗位数量少于预期，记录警告信息
            if a_posts_assigned < a_post_count or b_posts_assigned < b_post_count:
                print(f"警告：{date_str} 未能分配所有岗位（已分配：A岗 {a_posts_assigned}/{a_post_count}，B岗 {b_posts_assigned}/{b_post_count}）")
            
            current_date += timedelta(days=1)
        
        # 重新平衡后续日常排班
        next_date = end_date + timedelta(days=1)
        if next_date <= year_end:
            self.rebalance_remaining_schedule(next_date, year_end)
        
        return f"{mode}排班添加成功，后续排班已重新平衡！"

    def rebalance_remaining_schedule(self, start_date, end_date):
        """重新平衡指定日期范围内的日常排班"""
        current_date = start_date
        
        # 删除这段时间内的日常排班（保留特殊勤务和已完成值班）
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            existing_schedules = self.db.get_schedule(date_str)
            
            # 如果当天没有特殊勤务且没有已完成值班，则删除排班
            if not any(schedule[2] != '日常' or schedule[3] == 1 for schedule in existing_schedules):
                self.db.delete_schedule(date_str)
            
            current_date += timedelta(days=1)
        
        # 重新生成日常排班
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            existing_schedules = self.db.get_schedule(date_str)
            
            # 如果当天没有任何排班（包括特殊勤务和已完成值班），生成日常排班
            if not existing_schedules:
                duty_type = self.get_duty_type(date_str, True, '日常')
                available_battalions = list(range(1, 13))
                
                # 使用get_next_battalion来选择大队
                battalion = self.get_next_battalion(available_battalions, date_str, duty_type)
                
                if battalion:
                    # 添加completed=0表示未值班
                    self.db.save_schedule(date_str, battalion, duty_type, '日常', completed=0)
            
            current_date += timedelta(days=1)
        
        return "排班重新平衡完成"

    def update_schedule(self, date, battalion, duty_type, mode):
        """更新排班信息（用于手动修改）"""
        self.db.save_schedule(date, battalion, duty_type, mode)
        return "排班更新成功！"

class ScheduleApp(tk.Tk):
    def __init__(self):
        super().__init__()
        
        self.title("应急处突预备队排班系统")
        self.geometry("1400x800")
        
        self.system = ScheduleSystem()
        
        self.create_widgets()
        
    def create_widgets(self):
        # 创建左中右三栏
        left_frame = ttk.Frame(self)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        middle_frame = ttk.Frame(self)
        middle_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        right_frame = ttk.Frame(self)
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧年度排班初始化面板
        ttk.Label(left_frame, text="年度排班初始化", font=('Arial', 12, 'bold')).pack(pady=5)
        ttk.Label(left_frame, text="起始日期选择").pack(pady=5)
        self.regular_date_entry = DateEntry(left_frame)
        self.regular_date_entry.pack(pady=5)
        
        ttk.Button(left_frame, text="初始化年度排班", 
                  command=lambda: self.generate_schedule('日常')).pack(pady=10)
        
        ttk.Label(left_frame, text="说明：初始化年度排班后，\n可以在右侧添加特殊勤务。\n系统会自动调整后续排班\n以保持各类型值班的平衡。", 
                 justify=tk.LEFT).pack(pady=20)
        
        # 中间特殊勤务面板
        ttk.Label(middle_frame, text="特殊勤务添加", font=('Arial', 12, 'bold')).pack(pady=5)
        
        # 特殊勤务模式选择
        ttk.Label(middle_frame, text="勤务等级").pack(pady=5)
        self.mode_var = tk.StringVar(value='一级勤务')
        mode_combo = ttk.Combobox(middle_frame, textvariable=self.mode_var, 
                                 values=['一级勤务', '二级勤务', '三级勤务'])
        mode_combo.pack(pady=5)
        
        # 特殊勤务开始日期选择
        ttk.Label(middle_frame, text="开始日期").pack(pady=5)
        self.special_start_date_entry = DateEntry(middle_frame)
        self.special_start_date_entry.pack(pady=5)
        
        # 特殊勤务结束日期选择
        ttk.Label(middle_frame, text="结束日期").pack(pady=5)
        self.special_end_date_entry = DateEntry(middle_frame)
        self.special_end_date_entry.pack(pady=5)
        
        ttk.Button(middle_frame, text="添加特殊勤务", 
                  command=lambda: self.generate_schedule(self.mode_var.get())).pack(pady=10)
        
        # 功能按钮
        ttk.Button(middle_frame, text="显示统计信息", command=self.show_statistics).pack(pady=5)
        ttk.Button(middle_frame, text="显示值班类型说明", command=self.show_duty_types).pack(pady=5)
        
        # 右侧显示区域（日历形式）
        self.create_calendar_view(right_frame)
    
    def create_calendar_view(self, parent):
        # 日历视图标题
        self.calendar_title = ttk.Label(parent, text="排班日历", font=('Arial', 12, 'bold'))
        self.calendar_title.pack(pady=5)
        
        # 创建容器框架
        container = ttk.Frame(parent)
        container.pack(fill=tk.BOTH, expand=True)
        
        # 创建水平和垂直滚动条
        v_scrollbar = ttk.Scrollbar(container, orient=tk.VERTICAL)
        h_scrollbar = ttk.Scrollbar(container, orient=tk.HORIZONTAL)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建画布
        self.canvas = tk.Canvas(container, 
                              yscrollcommand=v_scrollbar.set,
                              xscrollcommand=h_scrollbar.set)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        v_scrollbar.config(command=self.canvas.yview)
        h_scrollbar.config(command=self.canvas.xview)
        
        # 创建内部框架
        self.calendar_inner = ttk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window((0, 0), 
                                                     window=self.calendar_inner, 
                                                     anchor='nw')
        
        # 绑定事件
        self.calendar_inner.bind('<Configure>', self._on_frame_configure)
        self.canvas.bind('<Configure>', self._on_canvas_configure)
        
        # 绑定鼠标滚轮事件
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)
    
    def _on_frame_configure(self, event=None):
        """更新画布的滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def _on_canvas_configure(self, event):
        """当画布大小改变时，调整内部窗口大小"""
        width = event.width
        self.canvas.itemconfig(self.canvas_window, width=width)
    
    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def update_calendar_view(self, start_date, end_date, schedules):
        # 清除现有日历内容
        for widget in self.calendar_inner.winfo_children():
            widget.destroy()
        
        # 将排班数据转换为字典格式，方便查询
        schedule_dict = {}
        for schedule in schedules:
            date = schedule[0]
            if date not in schedule_dict:
                schedule_dict[date] = []
            schedule_dict[date].append({
                'battalion': schedule[1],
                'duty_type': schedule[2],
                'mode': schedule[3],
                'completed': schedule[4]
            })
        
        # 创建日历表头
        headers = ['日期', '星期', '值班大队', '值班类型', '岗位类型', '勤务模式', '已值班']
        for col, header in enumerate(headers):
            ttk.Label(self.calendar_inner, text=header, font=('Arial', 10, 'bold')).grid(
                row=0, column=col, padx=5, pady=5)
        
        # 值班类型与岗位类型的对应关系
        post_type_map = {
            1: "A岗",  # 法定节假日A岗（白天在岗、全天巡逻）
            2: "A岗",  # 法定节假日A岗（白天在岗、晚上巡逻）
            3: "B岗",  # 法定节假日B岗（晚上不巡逻）
            4: "A岗",  # 普通周末A岗（白天在岗、晚上巡逻）
            5: "B岗",  # 普通周末B岗（晚上不巡逻）
            6: "C岗",  # 普通周末C岗（晚上巡逻）
            7: "A岗",  # 工作日A岗（晚上巡逻）
            8: "B岗",  # 工作日B岗（晚上不巡逻）
        }
        
        # 用于存储所有变量的字典
        self.schedule_vars = {}
        
        # 填充日历内容
        current_date = start_date
        row = 1
        weekdays = ['一', '二', '三', '四', '五', '六', '日']
        
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            weekday = weekdays[current_date.weekday()]
            
            if date_str in schedule_dict:
                for schedule in schedule_dict[date_str]:
                    # 日期
                    ttk.Label(self.calendar_inner, text=date_str).grid(
                        row=row, column=0, padx=5, pady=2)
                    # 星期
                    ttk.Label(self.calendar_inner, text=weekday).grid(
                        row=row, column=1, padx=5, pady=2)
                    
                    # 大队（可编辑）
                    battalion_var = tk.StringVar(value=str(schedule['battalion']))
                    battalion_combo = ttk.Combobox(self.calendar_inner, textvariable=battalion_var,
                                                 values=[str(i) for i in range(1, 13)],
                                                 width=5)
                    battalion_combo.grid(row=row, column=2, padx=5, pady=2)
                    
                    # 值班类型（可编辑）
                    duty_type_var = tk.StringVar(value=str(schedule['duty_type']))
                    duty_type_combo = ttk.Combobox(self.calendar_inner, textvariable=duty_type_var,
                                                 values=[str(i) for i in range(1, 9)],
                                                 width=5)
                    duty_type_combo.grid(row=row, column=3, padx=5, pady=2)
                    
                    # 岗位类型（根据值班类型自动显示）
                    post_type = post_type_map.get(schedule['duty_type'], "--")
                    ttk.Label(self.calendar_inner, text=post_type).grid(
                        row=row, column=4, padx=5, pady=2)
                    
                    # 勤务模式
                    ttk.Label(self.calendar_inner, text=schedule['mode']).grid(
                        row=row, column=5, padx=5, pady=2)
                    
                    # 已值班选择
                    completed_var = tk.BooleanVar(value=bool(schedule['completed']))
                    completed_check = ttk.Checkbutton(self.calendar_inner, variable=completed_var)
                    completed_check.grid(row=row, column=6, padx=5, pady=2)
                    
                    # 存储变量
                    self.schedule_vars[row] = {
                        'date': date_str,
                        'battalion': battalion_var,
                        'duty_type': duty_type_var,
                        'mode': schedule['mode'],
                        'completed': completed_var,
                        'original_completed': bool(schedule['completed'])  # 存储原始值班状态
                    }
                    
                    # 绑定值班类型变更事件，以更新岗位类型显示
                    def update_post_type(event, row=row):
                        try:
                            duty_type = int(event.widget.get())
                            post_type = post_type_map.get(duty_type, "--")
                            for widget in self.calendar_inner.grid_slaves(row=row, column=4):
                                widget.configure(text=post_type)
                        except ValueError:
                            pass
                    
                    duty_type_combo.bind('<<ComboboxSelected>>', update_post_type)
                    
                    row += 1
            else:
                # 显示无排班的日期
                ttk.Label(self.calendar_inner, text=date_str).grid(
                    row=row, column=0, padx=5, pady=2)
                ttk.Label(self.calendar_inner, text=weekday).grid(
                    row=row, column=1, padx=5, pady=2)
                ttk.Label(self.calendar_inner, text="--").grid(
                    row=row, column=2, padx=5, pady=2)
                ttk.Label(self.calendar_inner, text="无排班").grid(
                    row=row, column=3, padx=5, pady=2)
                ttk.Label(self.calendar_inner, text="--").grid(
                    row=row, column=4, padx=5, pady=2)
                ttk.Label(self.calendar_inner, text="--").grid(
                    row=row, column=5, padx=5, pady=2)
                ttk.Label(self.calendar_inner, text="--").grid(
                    row=row, column=6, padx=5, pady=2)
                row += 1
            
            current_date += timedelta(days=1)
        
        # 添加总的保存按钮
        save_frame = ttk.Frame(self.calendar_inner)
        save_frame.grid(row=row, column=0, columnspan=7, pady=10)
        
        ttk.Button(save_frame, text="保存所有修改",
                  command=self.save_all_changes).pack(pady=5)
        
        # 更新画布滚动区域
        self.calendar_inner.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox('all'))

    def save_all_changes(self):
        """保存所有修改并重新平衡后续排班"""
        try:
            # 记录哪些日期的值班状态发生了变化
            changed_dates = []
            earliest_change_date = None
            
            # 保存所有修改
            for row, vars in self.schedule_vars.items():
                date = vars['date']
                battalion = int(vars['battalion'].get())
                duty_type = int(vars['duty_type'].get())
                mode = vars['mode']
                completed = int(vars['completed'].get())
                
                # 检查值班状态是否发生变化
                if completed != vars['original_completed']:
                    changed_dates.append(date)
                    if earliest_change_date is None or date < earliest_change_date:
                        earliest_change_date = date
                
                # 保存修改
                self.system.db.save_schedule(date, battalion, duty_type, mode, completed)
            
            # 如果有值班状态发生变化，重新平衡后续排班
            if changed_dates:
                # 获取最早变化日期之后的第一天
                next_date = datetime.strptime(earliest_change_date, '%Y-%m-%d') + timedelta(days=1)
                year_end = datetime(next_date.year, 12, 31)
                
                # 重新平衡后续排班
                self.system.rebalance_remaining_schedule(next_date, year_end)
                
                # 获取所有日期并找出最早和最晚的日期
                all_dates = [vars['date'] for vars in self.schedule_vars.values()]
                start_date = datetime.strptime(min(all_dates), '%Y-%m-%d')
                end_date = datetime.strptime(max(all_dates), '%Y-%m-%d')
                
                # 刷新显示
                schedules = self.system.db.get_schedule_range(
                    start_date.strftime('%Y-%m-%d'),
                    end_date.strftime('%Y-%m-%d')
                )
                self.update_calendar_view(start_date, end_date, schedules)
                
                messagebox.showinfo("成功", "修改已保存，后续排班已重新平衡！")
            else:
                messagebox.showinfo("成功", "修改已保存！")
            
        except ValueError as e:
            messagebox.showerror("错误", f"保存失败：{str(e)}\n请确保输入的大队号和值班类型是有效的数字。")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败：{str(e)}")

    def generate_schedule(self, mode):
        try:
            if mode == '日常':
                start_date = self.regular_date_entry.get_date()
                if not start_date:
                    messagebox.showerror("错误", "请输入有效的日期")
                    return
                
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
                year_start = datetime(start_date_obj.year, 1, 1)
                year_end = datetime(start_date_obj.year, 12, 31)
                
                # 生成排班
                result = self.system.initialize_year_schedule(start_date_obj)
            else:
                start_date = self.special_start_date_entry.get_date()
                end_date = self.special_end_date_entry.get_date()
                if not start_date or not end_date:
                    messagebox.showerror("错误", "请输入有效的开始和结束日期")
                    return
                
                try:
                    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
                    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
                except ValueError:
                    messagebox.showerror("错误", "日期格式无效，请使用YYYY-MM-DD格式")
                    return
                
                year_start = datetime(start_date_obj.year, 1, 1)
                year_end = datetime(start_date_obj.year, 12, 31)
                
                if end_date_obj < start_date_obj:
                    messagebox.showerror("错误", "结束日期不能早于开始日期")
                    return
                
                # 生成特殊勤务排班
                result = self.system.add_special_duty(start_date_obj, end_date_obj, mode)
            
            # 获取全年的排班数据
            schedules = self.system.db.get_schedule_range(
                year_start.strftime('%Y-%m-%d'),
                year_end.strftime('%Y-%m-%d')
            )
            
            # 更新日历视图
            self.update_calendar_view(year_start, year_end, schedules)
            
            messagebox.showinfo("成功", result)
            
        except ValueError as e:
            messagebox.showerror("错误", f"日期格式错误：{str(e)}")
        except Exception as e:
            messagebox.showerror("错误", str(e))
    
    def show_statistics(self):
        stats = self.system.db.get_battalion_stats()
        if not stats.empty:
            # 创建新窗口显示统计信息
            stats_window = tk.Toplevel(self)
            stats_window.title("值班统计信息")
            stats_window.geometry("1000x800")
            
            # 创建笔记本控件
            notebook = ttk.Notebook(stats_window)
            notebook.pack(fill=tk.BOTH, expand=True)
            
            # 创建当前统计页面
            current_frame = ttk.Frame(notebook)
            notebook.add(current_frame, text="截止今日统计（实际次数）")
            
            # 创建年底统计页面
            year_end_frame = ttk.Frame(notebook)
            notebook.add(year_end_frame, text="截止年底统计（含平衡预测）")
            
            # 获取北京时间的今天凌晨
            beijing_tz = datetime(2025, 1, 8)  # 固定为2025年1月8日
            today = beijing_tz.replace(hour=0, minute=0, second=0, microsecond=0)
            year_end = datetime(today.year, 12, 31)
            
            # 生成当前统计（只统计已完成的值班）
            current_stats = self.system.db.get_battalion_stats(
                None, 
                today.strftime('%Y-%m-%d'),
                completed_only=True  # 只统计已完成的值班
            )
            
            # 创建完整的统计数据框架（所有大队和类型的组合）
            battalions = range(1, 13)
            duty_types = range(1, 9)
            
            # 创建空的统计表
            empty_stats = pd.DataFrame(
                index=pd.Index(battalions, name='battalion'),
                columns=pd.Index(duty_types, name='duty_type')
            ).fillna(0)
            
            # 将实际统计数据填充到空表中
            if not current_stats.empty:
                current_pivot = current_stats.pivot(
                    index='battalion',
                    columns='duty_type',
                    values='count'
                ).fillna(0)
                
                # 更新空表中的实际值
                for battalion in battalions:
                    for duty_type in duty_types:
                        if battalion in current_pivot.index and duty_type in current_pivot.columns:
                            empty_stats.loc[battalion, duty_type] = current_pivot.loc[battalion, duty_type]
            
            # 生成年底统计（考虑平衡）
            year_end_stats = self.system.db.get_battalion_stats(
                None,
                year_end.strftime('%Y-%m-%d'),
                completed_only=False  # 年底统计包含所有排班
            )
            year_end_pivot = year_end_stats.pivot(
                index='battalion',
                columns='duty_type',
                values='count'
            ).fillna(0)
            
            # 计算年底预期平均值
            total_days = (year_end - today).days
            remaining_days = total_days * 0.8  # 考虑80%的日常排班天数
            
            # 添加平衡预测信息
            balance_info = "\n\n平衡预测信息：\n"
            balance_info += "=" * 50 + "\n"
            balance_info += f"剩余天数：{total_days}天\n"
            balance_info += "各类型值班平均次数预测：\n"
            
            # 计算每种类型的平均值和最大偏差
            max_deviations = {}
            for duty_type in range(1, 9):
                if duty_type in year_end_pivot.columns:
                    type_counts = year_end_pivot[duty_type]
                    total = type_counts.sum()
                    avg = total / 12  # 总是除以12个大队
                    max_dev = max(abs(type_counts - avg))
                    balance_info += f"类型{duty_type}：平均{avg:.1f}次，最大偏差{max_dev:.1f}次\n"
                    max_deviations[duty_type] = max_dev
            
            balance_info += "\n系统会自动调整后续排班以确保每个类型的最大偏差不超过1天\n"
            
            # 显示当前统计
            self._create_stats_display(current_frame, empty_stats, "截止今日（仅统计已完成值班）", "")
            
            # 显示年底统计
            self._create_stats_display(year_end_frame, year_end_pivot, "截止年底", balance_info)
        else:
            messagebox.showinfo("提示", "暂无统计数据")
    
    def _create_stats_display(self, parent, pivot_stats, title, additional_info=""):
        """创建统计信息显示"""
        # 创建文本框和滚动条
        scrollbar = ttk.Scrollbar(parent)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        text_widget = tk.Text(parent, wrap=tk.WORD, yscrollcommand=scrollbar.set)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=text_widget.yview)
        
        # 格式化统计数据
        pivot_stats.columns = [f'类型{i}' for i in pivot_stats.columns]
        pivot_stats.index = [f'第{i}大队' for i in pivot_stats.index]
        
        # 添加总计行和平均值行
        pivot_stats.loc['总计'] = pivot_stats.sum()
        
        # 计算平均值（总次数除以12个大队）
        averages = pivot_stats.loc['总计'] / 12
        pivot_stats.loc['平均'] = averages
        
        # 计算每个大队与平均值的偏差
        avg_row = pivot_stats.loc['平均']
        deviation_stats = pivot_stats.drop(['总计', '平均'])
        deviation_stats = deviation_stats.sub(avg_row)
        
        # 计算每个大队的总值班次数
        total_duties = pivot_stats.drop(['总计', '平均']).sum(axis=1)
        avg_total = total_duties.mean()
        total_deviation = total_duties - avg_total
        
        # 生成统计文本
        stats_text = f"{title}值班统计信息：\n"
        stats_text += "=" * 50 + "\n"
        stats_text += pivot_stats.to_string()
        stats_text += "\n\n各大队总值班次数统计：\n"
        stats_text += "-" * 50 + "\n"
        stats_text += "大队      总次数    与平均值偏差\n"
        for idx in total_duties.index:
            stats_text += f"{idx:<8} {total_duties[idx]:>8.0f} {total_deviation[idx]:>12.1f}\n"
        stats_text += "-" * 50 + "\n"
        stats_text += f"平均值：{avg_total:.1f}\n"
        stats_text += "\n各大队与平均值的偏差：\n"
        stats_text += "-" * 50 + "\n"
        stats_text += deviation_stats.to_string()
        stats_text += additional_info
        stats_text += "\n\n各类型值班说明：\n"
        stats_text += "-" * 50 + "\n"
        stats_text += """1: 法定节假日A岗（白天在岗、全天巡逻）
2: 法定节假日A岗（白天在岗、晚上巡逻）
3: 法定节假日B岗（晚上不巡逻）
4: 普通周末A岗（白天在岗、晚上巡逻）
5: 普通周末B岗（晚上不巡逻）
6: 普通周末C岗（晚上巡逻）
7: 工作日A岗（晚上巡逻）
8: 工作日B岗（晚上不巡逻）"""
        
        text_widget.delete(1.0, tk.END)
        text_widget.insert(tk.END, stats_text)
        text_widget.config(state=tk.DISABLED)  # 设置为只读
    
    def show_duty_types(self):
        # 创建新窗口显示值班类型说明
        types_window = tk.Toplevel(self)
        types_window.title("值班类型说明")
        types_window.geometry("600x400")
        
        text_widget = tk.Text(types_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        duty_types_text = """值班类型说明：
1: 法定节假日A岗（白天在岗、全天巡逻）
2: 法定节假日A岗（白天在岗、晚上巡逻）
3: 法定节假日B岗（晚上不巡逻）
4: 普通周末A岗（白天在岗、晚上巡逻）
5: 普通周末B岗（晚上不巡逻）
6: 普通周末C岗（晚上巡逻）
7: 工作日A岗（晚上巡逻）
8: 工作日B岗（晚上不巡逻）"""
        
        text_widget.delete(1.0, tk.END)
        text_widget.insert(tk.END, duty_types_text)

if __name__ == "__main__":
    app = ScheduleApp()
    app.mainloop() 