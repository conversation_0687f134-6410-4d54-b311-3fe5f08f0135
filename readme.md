# 新闻收集工具

这是一个用于收集和整理热门新闻的Python工具，支持获取全球新闻和中国国内新闻及热搜内容。

## 功能特点

- 自动获取Hacker News的热门新闻（全球新闻）
- 自动获取中国国内主要新闻源的新闻
- 自动获取微博、百度、抖音等平台的热搜内容
- 按热度排序所有内容
- 按日期过滤新闻（只保留指定日期发布的新闻）
- 去除重复内容
- 支持自定义内容数量
- 生成Markdown格式的报告
- 命令行参数支持

## 安装依赖

```bash
pip install requests feedparser beautifulsoup4
```

## 使用方法

### 获取全球新闻

#### 基本使用

获取今天的热门新闻：

```bash
python news_collector.py
```

#### 使用批处理脚本（Windows）

双击运行 `get_today_news.bat` 文件，或在命令行中执行：

```bash
get_today_news.bat
```

#### 指定日期

获取指定日期的热门新闻：

```bash
python news_collector.py --date 2025-07-26
```

#### 指定新闻数量

获取指定数量的新闻（默认20条）：

```bash
python news_collector.py --count 30
```

#### 组合使用

```bash
python news_collector.py --date 2025-07-26 --count 25
```

### 获取中国国内新闻

#### 基本使用

获取今天的中国国内热门新闻：

```bash
python china_news_collector.py
```

#### 使用批处理脚本（Windows）

双击运行 `get_today_china_news.bat` 文件，或在命令行中执行：

```bash
get_today_china_news.bat
```

#### 指定日期和数量

```bash
python china_news_collector.py --date 2025-07-26 --count 30
```

### 获取中国热门内容（包含新闻和热搜）

#### 基本使用

获取今天的中国热门内容（包含新闻、微博热搜、百度热搜、抖音热搜）：

```bash
python china_hot_news_collector.py
```

#### 使用批处理脚本（Windows）

双击运行 `get_today_china_hot_news.bat` 文件，或在命令行中执行：

```bash
get_today_china_hot_news.bat
```

#### 指定数量

```bash
python china_hot_news_collector.py --count 30
```

## 参数说明

- `--date` 或 `-d`：指定日期，格式为 YYYY-MM-DD，默认为今天
- `--count` 或 `-c`：内容数量，默认20条
- `--output` 或 `-o`：输出文件名，默认自动生成

## 输出格式

工具会生成Markdown格式的报告，包含：

1. 内容标题（带链接）
2. 发布时间
3. 内容来源
4. 热度分数（热搜内容）

## 文件说明

- `news_collector.py`：全球新闻收集器
- `china_news_collector.py`：中国国内新闻收集器
- `china_hot_news_collector.py`：中国热门内容收集器（包含新闻和热搜）
- `get_today_news.bat`：Windows批处理脚本，用于快速获取今日全球新闻
- `get_today_china_news.bat`：Windows批处理脚本，用于快速获取今日中国新闻
- `get_today_china_hot_news.bat`：Windows批处理脚本，用于快速获取今日中国热门内容
- `top_news_YYYY-MM-DD.md`：生成的全球新闻报告文件
- `china_top_news_YYYY-MM-DD.md`：生成的中国国内新闻报告文件
- `china_hot_news_YYYY-MM-DD.md`：生成的中国热门内容报告文件

## 示例输出

### 全球新闻
```markdown
# 2025-07-26 热门新闻 Top 20

1. [新闻标题](https://example.com)
   发布时间: 2025-07-26 10:08:09 UTC | 热度分数: 169 | 来源: Hacker News
```

### 中国国内新闻
```markdown
# 2025-07-26 中国国内热门新闻 Top 20

1. [中国新闻标题](https://example.com)
   发布时间: 2025-07-26 10:08:09 UTC | 来源: 新浪新闻
```

### 中国热门内容（包含热搜）
```markdown
# 2025-07-26 中国热门内容 Top 30

## 包含新闻、微博热搜、百度热搜、抖音热搜等内容

1. [热搜话题](https://example.com)
   发布时间: 2025-07-26 10:08:09 UTC | 来源: 抖音热搜 (热度: 1,234,567)
```

## 注意事项

1. 工具会自动过滤出指定日期发布的内容
2. 如果指定日期没有足够的内容，会显示最新的热门内容
3. 为了避免API限制，程序在获取每条内容之间有短暂延迟
4. 生成的文件会保存在当前目录下
5. 部分热搜内容可能需要通过第三方API获取，具体效果可能因网络状况而异

## 扩展计划

未来可能会添加以下功能：

1. 支持更多新闻源和热搜平台
2. 添加邮件通知功能
3. 定时任务支持
4. Web界面展示
5. 内容分类功能
6. 关键词过滤功能