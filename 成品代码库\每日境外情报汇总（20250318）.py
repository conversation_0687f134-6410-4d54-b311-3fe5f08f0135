import sys
import os

# 设置Qt插件路径
if hasattr(sys, 'frozen'):
    # 如果是打包后的exe运行
    qt_plugin_path = os.path.join(sys._MEIPASS, 'PyQt5', 'Qt', 'plugins')
else:
    # 如果是Python脚本运行
    qt_plugin_path = os.path.join(os.path.dirname(sys.executable), 'Lib', 'site-packages', 'PyQt5', 'Qt5', 'plugins')
os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = qt_plugin_path

import json
import requests
import datetime
import time
import threading
import configparser
import re  # 添加正则表达式模块用于处理头像URL
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QCalendarWidget, QTextEdit, QTabWidget, 
                            QScrollArea, QFileDialog, QMessageBox, QProgressBar, QLineEdit,
                            QGroupBox, QFormLayout, QCheckBox, QComboBox, QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal, QSize, QUrl
from PyQt5.QtGui import QIcon, QFont, QPixmap, QTextDocument
from PyQt5.QtPrintSupport import QPrinter
import socks
import socket
import urllib.request
import urllib3
import tweepy
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import markdown
import webbrowser
from twitter_scraper import TwitterScraper
import subprocess

# 导入生成PDF和Word文档所需的库
import pdfkit
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import docx2pdf
import shutil

# 尝试导入WeasyPrint，但如果失败也不影响主程序
USE_WEASYPRINT = False
try:
    from weasyprint import HTML
    USE_WEASYPRINT = True
except ImportError:
    print("注意: WeasyPrint库不可用，将使用pdfkit作为PDF生成方案")
except Exception as e:
    print(f"WeasyPrint初始化错误: {str(e)}，将使用pdfkit作为PDF生成方案")

# 标记使用内置PDF生成功能
USE_INTERNAL_PDF = True

class ProxySettings:
    def __init__(self):
        self.enabled = False
        self.host = ""
        self.port = 0
        self.username = ""
        self.password = ""
        self.proxy_type = "SOCKS5"  # SOCKS5, SOCKS4, HTTP
        self.verify_ssl = False  # 是否验证SSL证书

    def apply_proxy(self):
        if not self.enabled:
            # 重置代理设置
            socket.socket = socket._socketobject
            return

        if self.proxy_type == "SOCKS5":
            proxy_type = socks.SOCKS5
        elif self.proxy_type == "SOCKS4":
            proxy_type = socks.SOCKS4
        else:
            proxy_type = socks.HTTP

        # 应用代理设置
        socks.set_default_proxy(
            proxy_type=proxy_type,
            addr=self.host,
            port=self.port,
            username=self.username if self.username else None,
            password=self.password if self.password else None
        )
        socket.socket = socks.socksocket

    def to_dict(self):
        return {
            "enabled": self.enabled,
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "password": self.password,
            "proxy_type": self.proxy_type,
            "verify_ssl": self.verify_ssl
        }

    def from_dict(self, data):
        self.enabled = data.get("enabled", False)
        self.host = data.get("host", "")
        self.port = data.get("port", 0)
        self.username = data.get("username", "")
        self.password = data.get("password", "")
        self.proxy_type = data.get("proxy_type", "SOCKS5")
        self.verify_ssl = data.get("verify_ssl", False)


class TwitterAPI:
    def __init__(self, proxy_settings=None):
        self.proxy_settings = proxy_settings
        self.api = None
        self.authenticated = False
        
        # X API凭证
        self.consumer_key = ""
        self.consumer_secret = ""
        self.access_token = ""
        self.access_token_secret = ""
    
    def setup_from_config(self, config):
        self.consumer_key = config.get("twitter_api", "consumer_key", fallback="")
        self.consumer_secret = config.get("twitter_api", "consumer_secret", fallback="")
        self.access_token = config.get("twitter_api", "access_token", fallback="")
        self.access_token_secret = config.get("twitter_api", "access_token_secret", fallback="")
    
    def authenticate(self):
        try:
            # 需要添加详细的错误处理
            auth = tweepy.OAuth1UserHandler(
                consumer_key=self.consumer_key,
                consumer_secret=self.consumer_secret,
                access_token=self.access_token,
                access_token_secret=self.access_token_secret
            )
            # 建议增加超时设置
            self.api = tweepy.API(auth, wait_on_rate_limit=True, timeout=15)
            self.authenticated = True
            return True
        except Exception as e:
            print(f"Twitter API认证失败: {str(e)}")
            self.authenticated = False
            return False
    
    def get_user_tweets(self, username, count=10, since_date=None):
        if not self.authenticated or not self.api:
            return []
        
        try:
            tweets = []
            # 使用用户时间线端点
            timeline = self.api.user_timeline(screen_name=username, count=count, tweet_mode="extended")
            
            for tweet in timeline:
                created_at = tweet.created_at
                
                # 过滤日期
                if since_date and created_at.date() < since_date:
                    continue
                
                # 提取推文内容
                tweet_data = {
                    "id": tweet.id,
                    "text": tweet.full_text,
                    "created_at": created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "user": {
                        "name": tweet.user.name,
                        "screen_name": tweet.user.screen_name,
                        "profile_image_url": tweet.user.profile_image_url
                    },
                    "media": [],
                    "urls": []
                }
                
                # 提取媒体
                if hasattr(tweet, "extended_entities") and "media" in tweet.extended_entities:
                    for media in tweet.extended_entities["media"]:
                        media_data = {
                            "type": media["type"],
                            "url": media["media_url_https"],
                            "display_url": media["display_url"]
                        }
                        
                        if media["type"] == "video":
                            # 获取视频URL(通常是最高质量的那个)
                            variants = media["video_info"]["variants"]
                            variants = [v for v in variants if "bitrate" in v]
                            if variants:
                                best_variant = max(variants, key=lambda x: x["bitrate"])
                                media_data["video_url"] = best_variant["url"]
                        
                        tweet_data["media"].append(media_data)
                
                # 提取URL
                if hasattr(tweet, "entities") and "urls" in tweet.entities:
                    for url_entity in tweet.entities["urls"]:
                        tweet_data["urls"].append({
                            "url": url_entity["url"],
                            "expanded_url": url_entity["expanded_url"],
                            "display_url": url_entity["display_url"]
                        })
                
                tweets.append(tweet_data)
                
            return tweets
        except Exception as e:
            print(f"获取推文失败: {str(e)}")
            return []


class FetchTweetsThread(QThread):
    update_progress = pyqtSignal(int, int)  # (current, total)
    update_status = pyqtSignal(str)
    fetch_complete = pyqtSignal(dict)
    fetch_error = pyqtSignal(str)
    
    def __init__(self, twitter_api, twitter_scraper, usernames, selected_date, count=50, use_api=True):
        super().__init__()
        self.twitter_api = twitter_api
        self.twitter_scraper = twitter_scraper
        self.usernames = usernames
        self.selected_date = selected_date
        self.count = count
        self.use_api = use_api  # 是否使用API，False则使用网页爬取
        
    def run(self):
        try:
            tweets_data = {}
            total = len(self.usernames)
            
            for i, username in enumerate(self.usernames):
                self.update_status.emit(f"正在获取 @{username} 的推文...")
                self.update_progress.emit(i, total)
                
                if self.use_api and self.twitter_api and self.twitter_api.authenticated:
                    # 使用API获取推文
                    tweets = self.twitter_api.get_user_tweets(
                        username=username,
                        count=self.count,
                        since_date=self.selected_date
                    )
                elif self.twitter_scraper and self.twitter_scraper.authenticated:
                    # 使用网页爬取推文
                    tweets = self.twitter_scraper.get_user_tweets(
                        username=username,
                        count=self.count,
                        since_date=self.selected_date
                    )
                else:
                    tweets = []
                
                if tweets:
                    tweets_data[username] = tweets
                    self.update_status.emit(f"已获取 @{username} 的 {len(tweets)} 条推文")
                else:
                    self.update_status.emit(f"未能获取 @{username} 的推文")
            
            self.update_progress.emit(total, total)
            self.update_status.emit("推文获取完成")
            self.fetch_complete.emit(tweets_data)
            
        except Exception as e:
            import traceback
            traceback.print_exc()  # 添加堆栈跟踪
            self.fetch_error.emit(f"获取推文时出错: {str(e)}")


class DownloadMediaThread(QThread):
    update_progress = pyqtSignal(int, int)  # (current, total)
    update_status = pyqtSignal(str)
    download_complete = pyqtSignal(dict)
    download_error = pyqtSignal(str)
    
    def __init__(self, tweets_data, download_folder, proxy_settings=None, download_mode="images_only"):
        super().__init__()
        self.tweets_data = tweets_data
        self.download_folder = download_folder
        self.proxy_settings = proxy_settings
        self.download_mode = download_mode
        
    def run(self):
        try:
            # 应用代理设置
            if self.proxy_settings:
                self.proxy_settings.apply_proxy()
            
            all_media = []
            total_media = 0
            
            # 计算总媒体数量和用户数量
            users_count = len(self.tweets_data)
            for username, tweets in self.tweets_data.items():
                for tweet in tweets:
                    total_media += len(tweet["media"])
            
            # 加上用户头像的数量
            total_media += users_count
            
            # 创建下载目录
            os.makedirs(self.download_folder, exist_ok=True)
            
            current = 0
            downloaded_media = {}
            downloaded_avatars = {}  # 用于存储下载的头像路径
            
            # 创建一个会话对象来复用连接
            session = requests.Session()
            
            # 配置代理（如果启用）
            if self.proxy_settings and self.proxy_settings.enabled:
                proxy_url = None
                if self.proxy_settings.proxy_type == "SOCKS5":
                    proxy_url = f"socks5://{self.proxy_settings.host}:{self.proxy_settings.port}"
                elif self.proxy_settings.proxy_type == "SOCKS4":
                    proxy_url = f"socks4://{self.proxy_settings.host}:{self.proxy_settings.port}"
                else:  # HTTP
                    proxy_url = f"http://{self.proxy_settings.host}:{self.proxy_settings.port}"
                
                # 添加用户名密码如果有的话
                if self.proxy_settings.username and self.proxy_settings.password:
                    proxy_url = proxy_url.replace("://", f"://{self.proxy_settings.username}:{self.proxy_settings.password}@")
                
                session.proxies = {
                    "http": proxy_url,
                    "https": proxy_url
                }
            
            # 设置SSL验证选项
            session.verify = self.proxy_settings.verify_ssl if self.proxy_settings else False
            
            # 先下载用户头像
            self.update_status.emit("开始下载用户头像...")
            for username, tweets in self.tweets_data.items():
                if len(tweets) == 0:
                    continue
                    
                user_folder = os.path.join(self.download_folder, username)
                os.makedirs(user_folder, exist_ok=True)
                
                # 获取用户信息 (从第一条推文中提取)
                user_info = tweets[0]["user"]
                avatar_url = user_info["profile_image_url"]
                
                # 移除尺寸参数，获取原始尺寸头像
                avatar_url = re.sub(r'_normal(\.[a-zA-Z0-9]+)$', r'\1', avatar_url)
                
                # 下载头像
                try:
                    self.update_status.emit(f"正在下载 @{username} 的头像")
                    
                    avatar_filename = f"avatar_{username}.jpg"
                    avatar_path = os.path.join(user_folder, avatar_filename)
                    
                    # 尝试3次下载
                    max_retries = 3
                    for attempt in range(max_retries):
                        try:
                            response = session.get(avatar_url, timeout=30)
                            if response.status_code == 200:
                                with open(avatar_path, "wb") as f:
                                    f.write(response.content)
                                downloaded_avatars[username] = avatar_path
                                break
                            else:
                                print(f"下载头像失败，状态码: {response.status_code}")
                                if attempt < max_retries - 1:
                                    time.sleep(1)
                        except Exception as e:
                            print(f"尝试下载头像时出错 (尝试 {attempt+1}/{max_retries}): {str(e)}")
                            if attempt < max_retries - 1:
                                time.sleep(1)
                    
                    # 如果所有尝试都失败，使用默认头像
                    if username not in downloaded_avatars:
                        self.update_status.emit(f"无法下载 @{username} 的头像，将使用默认头像")
                        # 设置一个默认头像路径
                        default_avatar_path = os.path.join(self.download_folder, "default_avatar.jpg")
                        # 创建一个简单的默认头像
                        if not os.path.exists(default_avatar_path):
                            img = Image.new('RGB', (200, 200), color=(100, 149, 237))
                            d = ImageDraw.Draw(img)
                            try:
                                font = ImageFont.truetype("arial.ttf", 40)
                            except:
                                font = ImageFont.load_default()
                            d.text((70, 80), "@", fill=(255, 255, 255), font=font)
                            img.save(default_avatar_path)
                        downloaded_avatars[username] = default_avatar_path
                except Exception as e:
                    print(f"下载头像时出错: {str(e)}")
                    # 设置一个默认头像路径
                    default_avatar_path = os.path.join(self.download_folder, "default_avatar.jpg")
                    # 创建一个简单的默认头像
                    if not os.path.exists(default_avatar_path):
                        img = Image.new('RGB', (200, 200), color=(100, 149, 237))
                        d = ImageDraw.Draw(img)
                        try:
                            font = ImageFont.truetype("arial.ttf", 40)
                        except:
                            font = ImageFont.load_default()
                        d.text((70, 80), "@", fill=(255, 255, 255), font=font)
                        img.save(default_avatar_path)
                    downloaded_avatars[username] = default_avatar_path
                
                current += 1
                self.update_progress.emit(current, total_media)
                
            # 下载所有媒体
            for username, tweets in self.tweets_data.items():
                user_folder = os.path.join(self.download_folder, username)
                os.makedirs(user_folder, exist_ok=True)
                
                downloaded_media[username] = {}
                
                for tweet in tweets:
                    tweet_id = tweet["id"]
                    tweet_folder = os.path.join(user_folder, str(tweet_id))
                    os.makedirs(tweet_folder, exist_ok=True)
                    
                    downloaded_media[username][tweet_id] = []
                    
                    for media in tweet["media"]:
                        current += 1
                        self.update_progress.emit(current, total_media)
                        
                        media_type = media["type"]
                        media_url = media["url"]
                        
                        if media_type == "photo":
                            self.update_status.emit(f"正在下载图片: {os.path.basename(media_url)}")
                            
                            # 下载图片
                            file_name = f"{current}_{os.path.basename(media_url)}"
                            file_path = os.path.join(tweet_folder, file_name)
                            
                            # 尝试3次下载
                            max_retries = 3
                            for attempt in range(max_retries):
                                try:
                                    response = session.get(media_url, timeout=30)
                                    if response.status_code == 200:
                                        with open(file_path, "wb") as f:
                                            f.write(response.content)
                                        
                                        downloaded_media[username][tweet_id].append({
                                            "type": "photo",
                                            "original_url": media_url,
                                            "local_path": file_path
                                        })
                                        break
                                    else:
                                        print(f"下载图片失败，状态码: {response.status_code}")
                                        if attempt < max_retries - 1:
                                            time.sleep(1)
                                except Exception as e:
                                    print(f"尝试下载图片时出错 (尝试 {attempt+1}/{max_retries}): {str(e)}")
                                    if attempt < max_retries - 1:
                                        time.sleep(1)
                        
                        elif media_type == "video":
                            self.update_status.emit(f"正在下载视频截图...")
                            
                            if "video_url" in media:
                                video_url = media["video_url"]
                                
                                # 使用视频URL的一部分作为文件名
                                file_name = f"{current}_video_{os.path.basename(video_url).split('?')[0]}.jpg"
                                file_path = os.path.join(tweet_folder, file_name)
                                
                                # 将视频URL保存到文本文件
                                url_file_path = os.path.join(tweet_folder, f"{current}_video_url.txt")
                                with open(url_file_path, "w", encoding="utf-8") as f:
                                    f.write(video_url)
                                
                                # 创建一个视频占位图
                                img = Image.new('RGB', (640, 360), color=(0, 0, 0))
                                d = ImageDraw.Draw(img)
                                
                                # 尝试加载字体，如果失败则使用默认字体
                                try:
                                    font = ImageFont.truetype("arial.ttf", 20)
                                except:
                                    font = ImageFont.load_default()
                                
                                # 在图片上绘制文字
                                d.text((20, 20), "视频内容", fill=(255, 255, 255), font=font)
                                d.text((20, 50), f"URL: {video_url[:50]}...", fill=(255, 255, 255), font=font)
                                d.text((20, 80), "点击可在浏览器中查看", fill=(255, 255, 255), font=font)
                                
                                # 保存图片
                                img.save(file_path)
                                
                                media_info = {
                                    "type": "video",
                                    "original_url": video_url,
                                    "local_path": file_path,
                                    "url_file": url_file_path
                                }
                                
                                # 如果下载模式包括视频，则下载视频文件
                                if self.download_mode == "images_and_videos":
                                    # 视频文件路径
                                    video_file_name = f"{current}_video_{os.path.basename(video_url).split('?')[0]}.mp4"
                                    video_file_path = os.path.join(tweet_folder, video_file_name)
                                    
                                    # 下载视频文件
                                    video_downloaded = False
                                    max_retries = 3
                                    for attempt in range(max_retries):
                                        try:
                                            self.update_status.emit(f"正在下载视频文件 (尝试 {attempt+1}/{max_retries})...")
                                            response = session.get(video_url, timeout=180)  # 增加视频下载超时时间
                                            if response.status_code == 200:
                                                with open(video_file_path, "wb") as f:
                                                    f.write(response.content)
                                                video_downloaded = True
                                                break
                                            else:
                                                print(f"下载视频失败，状态码: {response.status_code}")
                                                if attempt < max_retries - 1:
                                                    time.sleep(2)
                                        except Exception as e:
                                            print(f"尝试下载视频时出错 (尝试 {attempt+1}/{max_retries}): {str(e)}")
                                            if attempt < max_retries - 1:
                                                time.sleep(2)
                                    
                                    # 如果视频下载成功，添加视频文件路径
                                    if video_downloaded:
                                        media_info["video_file_path"] = video_file_path
                                
                                downloaded_media[username][tweet_id].append(media_info)
            
            self.update_progress.emit(total_media, total_media)
            self.update_status.emit("下载完成")
            
            # 将头像信息添加到下载结果中
            result = {
                "media": downloaded_media,
                "avatars": downloaded_avatars
            }
            self.download_complete.emit(result)
        
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.download_error.emit(f"下载媒体失败: {str(e)}")


class GenerateReportThread(QThread):
    update_progress = pyqtSignal(int, int)
    update_status = pyqtSignal(str)
    report_complete = pyqtSignal(dict)  # 更改为发送报告路径字典
    report_error = pyqtSignal(str)
    
    def __init__(self, tweets_data, media_data, selected_date, output_folder, formats=None, username_to_nickname=None):
        super().__init__()
        self.tweets_data = tweets_data
        # 正确处理媒体数据结构
        if isinstance(media_data, dict) and "media" in media_data:
            self.media_data = media_data["media"]
            self.avatar_data = media_data["avatars"]
        else:
            # 兼容旧版本结构
            self.media_data = media_data
            self.avatar_data = {}
        self.selected_date = selected_date
        self.output_folder = output_folder
        self.formats = formats or ["html"]  # 默认至少输出HTML格式
        # 用于存储复制后的媒体文件路径
        self.copied_media = {}
        self.copied_avatars = {}
        # 用户名到昵称的映射
        self.username_to_nickname = username_to_nickname or {}
        
    def run(self):
        try:
            self.update_status.emit("正在生成报告...")
            
            # 创建输出目录
            os.makedirs(self.output_folder, exist_ok=True)
            
            # 创建媒体文件目录
            media_folder = os.path.join(self.output_folder, "media")
            os.makedirs(media_folder, exist_ok=True)
            
            # 复制媒体文件到报告目录
            self.copy_media_files(media_folder)
            
            # 报告文件基础名
            base_filename = f"情报简报_{self.selected_date}"
            
            # 生成报告
            report_files = {}
            
            # 始终生成HTML (作为其他格式的基础)
            html_file = os.path.join(self.output_folder, f"{base_filename}.html")
            html_content = self.generate_html_report()
            
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(html_content)
            
            report_files["html"] = html_file
            self.update_status.emit("HTML报告生成完成")
            
            # 生成PDF
            if "pdf" in self.formats:
                self.update_status.emit("正在生成PDF报告...")
                pdf_file = os.path.join(self.output_folder, f"{base_filename}.pdf")
                self.generate_pdf_report(html_content, pdf_file)
                report_files["pdf"] = pdf_file
                self.update_status.emit("PDF报告生成完成")
            
            # 生成Word文档
            if "word" in self.formats:
                self.update_status.emit("正在生成Word报告...")
                docx_file = os.path.join(self.output_folder, f"{base_filename}.docx")
                self.generate_word_report(docx_file)
                report_files["word"] = docx_file
                self.update_status.emit("Word报告生成完成")
            
            self.update_status.emit("所有报告生成完成")
            self.report_complete.emit(report_files)
        
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.report_error.emit(f"生成报告失败: {str(e)}")
    
    def copy_media_files(self, media_folder):
        """将所有媒体文件复制到报告目录"""
        self.update_status.emit("正在复制媒体文件...")
        
        # 计算总共需要复制的文件数
        total_files = 0
        
        # 头像数量
        total_files += len(self.avatar_data)
        
        # 媒体文件数量
        for username, user_media in self.media_data.items():
            for tweet_id, media_items in user_media.items():
                total_files += len(media_items)
        
        # 创建默认头像
        default_avatar_path = os.path.join(media_folder, "default_avatar.jpg")
        if not os.path.exists(default_avatar_path):
            img = Image.new('RGB', (200, 200), color=(100, 149, 237))
            d = ImageDraw.Draw(img)
            try:
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                font = ImageFont.load_default()
            d.text((70, 80), "@", fill=(255, 255, 255), font=font)
            img.save(default_avatar_path)
        
        # 复制头像
        current = 0
        for username, avatar_path in self.avatar_data.items():
            if os.path.exists(avatar_path):
                # 创建用户目录
                user_folder = os.path.join(media_folder, username)
                os.makedirs(user_folder, exist_ok=True)
                
                # 复制头像文件
                dest_path = os.path.join(user_folder, f"avatar_{username}.jpg")
                try:
                    import shutil
                    shutil.copy2(avatar_path, dest_path)
                    self.copied_avatars[username] = dest_path
                except Exception as e:
                    print(f"复制头像失败: {str(e)}")
                    self.copied_avatars[username] = default_avatar_path
            else:
                self.copied_avatars[username] = default_avatar_path
            
            current += 1
            self.update_progress.emit(current, total_files)
        
        # 复制媒体文件
        self.copied_media = {}
        for username, user_media in self.media_data.items():
            # 初始化用户的媒体字典
            self.copied_media[username] = {}
            
            for tweet_id, media_items in user_media.items():
                # 初始化推文的媒体列表
                self.copied_media[username][tweet_id] = []
                
                # 创建推文目录
                tweet_folder = os.path.join(media_folder, username, str(tweet_id))
                os.makedirs(tweet_folder, exist_ok=True)
                
                for media_item in media_items:
                    media_type = media_item["type"]
                    original_path = media_item["local_path"]
                    
                    if os.path.exists(original_path):
                        # 复制媒体文件
                        filename = os.path.basename(original_path)
                        dest_path = os.path.join(tweet_folder, filename)
                        
                        try:
                            import shutil
                            shutil.copy2(original_path, dest_path)
                            
                            copied_item = media_item.copy()
                            copied_item["local_path"] = dest_path
                            
                            # 如果是视频，复制URL文件
                            if media_type == "video" and "url_file" in media_item:
                                url_file = media_item["url_file"]
                                if os.path.exists(url_file):
                                    dest_url_file = os.path.join(tweet_folder, os.path.basename(url_file))
                                    shutil.copy2(url_file, dest_url_file)
                                    copied_item["url_file"] = dest_url_file
                            
                            self.copied_media[username][tweet_id].append(copied_item)
                        except Exception as e:
                            print(f"复制媒体文件失败: {str(e)}")
                    
                    current += 1
                    self.update_progress.emit(current, total_files)
        
        self.update_status.emit("媒体文件复制完成")
    
    def generate_html_report(self):
        # CSS样式
        css = """
        <style>
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                color: #333;
            }
            .container {
                max-width: 1000px;
                margin: 0 auto;
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            }
            h1 {
                color: #1d3557;
                text-align: center;
                border-bottom: 2px solid #1d3557;
                padding-bottom: 10px;
                margin-bottom: 30px;
            }
            h2 {
                color: #457b9d;
                border-left: 5px solid #457b9d;
                padding-left: 15px;
                margin-top: 30px;
            }
            h3 {
                color: #1d3557;
            }
            .tweet {
                border: 1px solid #ddd;
                padding: 20px;
                margin-bottom: 20px;
                border-radius: 8px;
                background-color: #fff;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            }
            .tweet-header {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }
            .profile-image {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                margin-right: 15px;
                object-fit: cover;
            }
            .user-info {
                flex: 1;
            }
            .user-name {
                font-weight: bold;
                margin: 0;
                color: #333;
            }
            .screen-name {
                color: #666;
                margin: 0;
            }
            .timestamp {
                color: #888;
                font-size: 0.9em;
            }
            .tweet-content {
                margin-bottom: 15px;
                white-space: pre-wrap;
            }
            .media-container {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 15px;
            }
            .media-item {
                position: relative;
                overflow: hidden;
                border-radius: 8px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }
            .media-item img {
                width: 100%;
                height: auto;
                transition: transform 0.3s;
            }
            .media-item:hover img {
                transform: scale(1.03);
            }
            .media-item.video {
                position: relative;
            }
            .media-item.video::after {
                content: '▶';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 40px;
                color: white;
                background: rgba(0, 0, 0, 0.5);
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .media-item a {
                display: block;
                cursor: pointer;
            }
            .media-group-1 .media-item {
                width: 100%;
            }
            .media-group-2 .media-item {
                width: calc(50% - 5px);
            }
            .media-group-3 .media-item, .media-group-4 .media-item {
                width: calc(50% - 5px);
            }
            .tweet-footer {
                margin-top: 15px;
                color: #666;
                font-size: 0.9em;
            }
            .tweet-url {
                color: #1da1f2;
                text-decoration: none;
            }
            .tweet-url:hover {
                text-decoration: underline;
            }
            .meta-info {
                margin-top: 5px;
                color: #888;
                font-size: 0.8em;
            }
            @media print {
                body {
                    background-color: white;
                }
                .container {
                    box-shadow: none;
                    max-width: 100%;
                }
                .tweet {
                    page-break-inside: avoid;
                    border: 1px solid #eee;
                    box-shadow: none;
                }
            }
        </style>
        """
        
        # HTML头部
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>情报简报 - {self.selected_date}</title>
            {css}
        </head>
        <body>
            <div class="container">
                <h1>境外情报简报 - {self.selected_date}</h1>
                <p>本简报汇总了以下X平台用户在{self.selected_date}发布的信息:</p>
                <ul>
                    <li>李老师不是你老师 (@whyyoutouzhele)</li>
                    <li>多伦多方脸 (@torontobigface)</li>
                    <li>蔡慎坤 (@cskun1989)</li>
                </ul>
        """
        
        # 添加各用户的推文
        usernames = list(self.tweets_data.keys())
        
        for username in usernames:
            display_name = self.username_to_nickname.get(username, username)
            html += f'<h2>{display_name} (@{username})</h2>'
            
            # 检查是否有该用户的推文
            if username not in self.tweets_data or not self.tweets_data[username]:
                html += f'<p>在{self.selected_date}没有找到该用户的推文。</p>'
                continue
            
            # 获取用户头像本地路径
            avatar_path = self.copied_avatars.get(username)
            if avatar_path and os.path.exists(avatar_path):
                avatar_rel_path = os.path.relpath(avatar_path, self.output_folder)
            else:
                # 如果没有本地头像，使用默认头像
                default_avatar_path = os.path.join(self.output_folder, "media", "default_avatar.jpg")
                avatar_rel_path = os.path.relpath(default_avatar_path, self.output_folder)
                
            # 遍历该用户的所有推文
            for tweet in self.tweets_data[username]:
                tweet_id = tweet["id"]
                tweet_text = tweet["text"]
                created_at = tweet["created_at"]
                user_info = tweet["user"]
                
                # 推文HTML
                html += f'''
                <div class="tweet">
                    <div class="tweet-header">
                        <img class="profile-image" src="{avatar_rel_path}" alt="Profile Image">
                        <div class="user-info">
                            <p class="user-name">{user_info['name']}</p>
                            <p class="screen-name">@{user_info['screen_name']}</p>
                        </div>
                        <div class="timestamp">{created_at}</div>
                    </div>
                    <div class="tweet-content">{tweet_text}</div>
                '''
                
                # 添加媒体内容
                has_media = False
                if username in self.copied_media and tweet_id in self.copied_media[username]:
                    media_items = self.copied_media[username][tweet_id]
                    if media_items:
                        has_media = True
                        media_count = len(media_items)
                        
                        html += f'<div class="media-container media-group-{media_count}">'
                        
                        for media in media_items:
                            media_type = media["type"]
                            local_path = media["local_path"]
                            
                            # 确保文件存在
                            if not os.path.exists(local_path):
                                print(f"警告: 媒体文件不存在: {local_path}")
                                continue
                                
                            # 转换为相对路径
                            rel_path = os.path.relpath(local_path, self.output_folder)
                            
                            if media_type == "photo":
                                html += f'''
                                <div class="media-item photo">
                                    <a href="{rel_path}" target="_blank">
                                        <img src="{rel_path}" alt="Media">
                                    </a>
                                </div>
                                '''
                            elif media_type == "video":
                                original_url = media["original_url"]
                                html += f'''
                                <div class="media-item video">
                                    <a href="{original_url}" target="_blank">
                                        <img src="{rel_path}" alt="Video Thumbnail">
                                    </a>
                                    <div class="meta-info">视频链接: <a href="{original_url}" target="_blank">点击查看</a></div>
                                </div>
                                '''
                        
                        html += '</div>'
                
                # 如果推文有媒体但未找到媒体文件
                if tweet["media"] and not has_media:
                    html += '<div class="meta-info">该推文包含媒体内容，但无法在本地找到媒体文件</div>'
                
                # 推文链接
                tweet_url = f"https://twitter.com/{username}/status/{tweet_id}"
                html += f'''
                    <div class="tweet-footer">
                        <a href="{tweet_url}" class="tweet-url" target="_blank">查看原始推文</a>
                    </div>
                </div>
                '''
        
        # HTML尾部
        html += """
                <div style="margin-top: 30px; text-align: center; color: #666; font-size: 0.9em;">
                    <p>报告生成时间: """ + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def generate_pdf_report(self, html_content, pdf_file):
        """从HTML内容生成PDF文件"""
        try:
            # 创建一个内嵌所有图片的HTML版本，避免外部引用问题
            self.update_status.emit("准备生成PDF (正在处理图片)...")
            embedded_html = self.generate_embedded_html_for_pdf()
            
            # 保存嵌入式HTML到临时文件
            temp_html_path = os.path.join(self.output_folder, "temp_for_pdf.html")
            with open(temp_html_path, "w", encoding="utf-8") as f:
                f.write(embedded_html)
            
            # 使用QPrinter生成PDF
            self.update_status.emit("使用QPrinter生成PDF...")
            
            try:
                # 使用QPrinter直接生成PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(pdf_file)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(15, 15, 15, 15, QPrinter.Millimeter)
                
                # 创建文档并加载HTML
                document = QTextDocument()
                document.setHtml(embedded_html)
                document.print_(printer)
                
                self.update_status.emit("PDF生成完成")
                
                # 清理临时文件
                if os.path.exists(temp_html_path):
                    os.remove(temp_html_path)
                    
                return True
            except Exception as print_error:
                self.update_status.emit(f"QPrinter生成PDF失败: {str(print_error)}")
                
                # 创建指导用户使用浏览器打印的HTML
                browser_print_html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>PDF生成失败 - 手动打印指南</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 50px; }}
                        h1 {{ color: #d32f2f; }}
                        h2 {{ color: #1976d2; }}
                        p {{ line-height: 1.6; }}
                        .steps {{ margin-left: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 5px; }}
                        .note {{ color: #d32f2f; font-weight: bold; }}
                    </style>
                </head>
                <body>
                    <h1>PDF自动生成失败</h1>
                    <p>自动PDF生成遇到问题，但您可以通过以下步骤手动将HTML报告转换为PDF:</p>
                    
                    <div class="steps">
                        <h2>方法1: 使用浏览器打印功能</h2>
                        <ol>
                            <li>打开已生成的HTML报告文件 (位于<code>{os.path.join(self.output_folder, f'情报简报_{self.selected_date}.html')}</code>)</li>
                            <li>在浏览器中按下Ctrl+P组合键(或命令+P，如果使用Mac)</li>
                            <li>在打印设置中选择"另存为PDF"</li>
                            <li>点击"保存"按钮</li>
                        </ol>
                    </div>
                    
                    <p class="note">注意: 自动生成失败原因: {str(print_error)}</p>
                </body>
                </html>
                """
                
                # 保存浏览器打印指南
                guide_path = os.path.join(self.output_folder, f"情报简报_{self.selected_date}_manual_pdf_guide.html")
                with open(guide_path, "w", encoding="utf-8") as f:
                    f.write(browser_print_html)
                
                # 创建一个简单的文本文件作为PDF
                with open(pdf_file, "w", encoding="utf-8") as f:
                    f.write(f"PDF生成失败。请使用浏览器打开HTML报告并打印为PDF。\n详细指南见: {guide_path}")
                
                return False
                
        except Exception as e:
            print(f"生成PDF报告失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
            # 创建一个简单的错误说明文件
            with open(pdf_file, "w", encoding="utf-8") as f:
                f.write(f"PDF生成失败。错误信息: {str(e)}\n\n")
                f.write("可能的解决方法:\n")
                f.write("1. 尝试使用浏览器打开HTML报告，然后使用浏览器的打印功能保存为PDF\n")
                f.write("2. 确保HTML报告中的所有资源（如图片）都可以正常访问\n")
            
            return False            
            return True
                
        except Exception as e:
            print(f"生成PDF报告失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
            # 创建一个简单的错误说明文件
            with open(pdf_file, "w", encoding="utf-8") as f:
                f.write(f"PDF生成失败。错误信息: {str(e)}\n\n")
                f.write("可能的解决方法:\n")
                f.write("1. 尝试使用浏览器打开HTML报告，然后使用浏览器的打印功能保存为PDF\n")
                f.write("2. 确保HTML报告中的所有资源（如图片）都可以正常访问\n")
            
            return False
    
    def generate_embedded_html_for_pdf(self):
        """生成一个包含所有嵌入式图片的HTML，适合PDF转换"""
        # CSS样式保持不变，但做一些针对PDF的优化
        css = """
        <style>
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background-color: white;
                color: #333;
            }
            .container {
                max-width: 100%;
                margin: 0 auto;
                padding: 30px;
            }
            h1 {
                color: #1d3557;
                text-align: center;
                border-bottom: 2px solid #1d3557;
                padding-bottom: 10px;
                margin-bottom: 30px;
            }
            h2 {
                color: #457b9d;
                border-left: 5px solid #457b9d;
                padding-left: 15px;
                margin-top: 30px;
            }
            h3 {
                color: #1d3557;
            }
            .tweet {
                border: 1px solid #ddd;
                padding: 20px;
                margin-bottom: 20px;
                border-radius: 8px;
                background-color: #fff;
                page-break-inside: avoid;
            }
            .tweet-header {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }
            .profile-image {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                margin-right: 15px;
            }
            .user-info {
                flex: 1;
            }
            .user-name {
                font-weight: bold;
                margin: 0;
                color: #333;
            }
            .screen-name {
                color: #666;
                margin: 0;
            }
            .timestamp {
                color: #888;
                font-size: 0.9em;
            }
            .tweet-content {
                margin-bottom: 15px;
                white-space: pre-wrap;
            }
            .media-container {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 15px;
            }
            .media-item {
                position: relative;
                overflow: hidden;
                border-radius: 8px;
            }
            .media-item img {
                width: 100%;
                max-width: 500px;
                height: auto;
            }
            .media-group-1 .media-item {
                width: 100%;
            }
            .media-group-2 .media-item {
                width: calc(50% - 5px);
            }
            .media-group-3 .media-item, .media-group-4 .media-item {
                width: calc(50% - 5px);
            }
            .tweet-footer {
                margin-top: 15px;
                color: #666;
                font-size: 0.9em;
            }
            .tweet-url {
                color: #1da1f2;
            }
            .meta-info {
                margin-top: 5px;
                color: #888;
                font-size: 0.8em;
            }
            @page {
                size: A4;
                margin: 2cm;
            }
            @media print {
                body {
                    background-color: white;
                }
                .container {
                    box-shadow: none;
                    max-width: 100%;
                }
                .tweet {
                    page-break-inside: avoid;
                    border: 1px solid #eee;
                    box-shadow: none;
                }
            }
        </style>
        """
        
        # HTML头部
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>情报简报 - {self.selected_date}</title>
            {css}
        </head>
        <body>
            <div class="container">
                <h1>境外情报简报 - {self.selected_date}</h1>
                <p>本简报汇总了以下X平台用户在{self.selected_date}发布的信息:</p>
                <ul>
                    <li>李老师不是你老师 (@whyyoutouzhele)</li>
                    <li>多伦多方脸 (@torontobigface)</li>
                    <li>蔡慎坤 (@cskun1989)</li>
                </ul>
        """
        
        # 添加各用户的推文
        usernames = list(self.tweets_data.keys())
        
        for username in usernames:
            display_name = self.username_to_nickname.get(username, username)
            html += f'<h2>{display_name} (@{username})</h2>'
            
            # 检查是否有该用户的推文
            if username not in self.tweets_data or not self.tweets_data[username]:
                html += f'<p>在{self.selected_date}没有找到该用户的推文。</p>'
                continue
            
            # 获取用户头像并转换为base64
            avatar_path = self.copied_avatars.get(username)
            avatar_base64 = ""
            
            if avatar_path and os.path.exists(avatar_path):
                try:
                    with open(avatar_path, "rb") as img_file:
                        import base64
                        avatar_base64 = base64.b64encode(img_file.read()).decode('utf-8')
                except Exception as e:
                    print(f"读取头像文件失败: {str(e)}")
                    avatar_base64 = ""
            
            if not avatar_base64:
                # 使用默认头像
                default_avatar_path = os.path.join(self.output_folder, "media", "default_avatar.jpg")
                if os.path.exists(default_avatar_path):
                    try:
                        with open(default_avatar_path, "rb") as img_file:
                            import base64
                            avatar_base64 = base64.b64encode(img_file.read()).decode('utf-8')
                    except:
                        avatar_base64 = ""
            
            # 遍历该用户的所有推文
            for tweet in self.tweets_data[username]:
                tweet_id = tweet["id"]
                tweet_text = tweet["text"]
                created_at = tweet["created_at"]
                user_info = tweet["user"]
                
                # 推文HTML
                html += f'''
                <div class="tweet">
                    <div class="tweet-header">
                '''
                
                # 添加头像（内嵌base64）
                if avatar_base64:
                    html += f'<img class="profile-image" src="data:image/jpeg;base64,{avatar_base64}" alt="Profile Image">'
                else:
                    html += '<div style="width:50px; height:50px; background-color:#1da1f2; border-radius:50%; margin-right:15px;"></div>'
                
                html += f'''
                        <div class="user-info">
                            <p class="user-name">{user_info['name']}</p>
                            <p class="screen-name">@{user_info['screen_name']}</p>
                        </div>
                        <div class="timestamp">{created_at}</div>
                    </div>
                    <div class="tweet-content">{tweet_text}</div>
                '''
                
                # 添加媒体内容（内嵌base64）
                has_media = False
                if username in self.copied_media and tweet_id in self.copied_media[username]:
                    media_items = self.copied_media[username][tweet_id]
                    if media_items:
                        has_media = True
                        media_count = len(media_items)
                        
                        html += f'<div class="media-container media-group-{media_count}">'
                        
                        for media in media_items:
                            media_type = media["type"]
                            local_path = media["local_path"]
                            
                            # 确保文件存在
                            if not os.path.exists(local_path):
                                print(f"警告: 媒体文件不存在: {local_path}")
                                continue
                                
                            # 转换图片为base64
                            try:
                                if media_type == "photo":
                                    with open(local_path, "rb") as img_file:
                                        import base64
                                        img_base64 = base64.b64encode(img_file.read()).decode('utf-8')
                                        
                                        html += f'''
                                        <div class="media-item photo">
                                            <img src="data:image/jpeg;base64,{img_base64}" alt="Media">
                                        </div>
                                        '''
                                elif media_type == "video":
                                    # 对于视频，我们只能内嵌缩略图
                                    with open(local_path, "rb") as img_file:
                                        import base64
                                        img_base64 = base64.b64encode(img_file.read()).decode('utf-8')
                                        
                                        original_url = media["original_url"]
                                        html += f'''
                                        <div class="media-item video">
                                            <img src="data:image/jpeg;base64,{img_base64}" alt="Video Thumbnail">
                                            <div class="meta-info">视频链接: {original_url}</div>
                                        </div>
                                        '''
                            except Exception as e:
                                print(f"转换媒体为base64失败: {str(e)}")
                                # 跳过这个媒体项
                                continue
                        
                        html += '</div>'
                
                # 如果推文有媒体但未找到媒体文件
                if tweet["media"] and not has_media:
                    html += '<div class="meta-info">该推文包含媒体内容，但无法在本地找到媒体文件</div>'
                
                # 推文链接
                tweet_url = f"https://twitter.com/{username}/status/{tweet_id}"
                html += f'''
                    <div class="tweet-footer">
                        <a href="{tweet_url}" class="tweet-url">查看原始推文</a>
                    </div>
                </div>
                '''
        
        # HTML尾部
        html += """
                <div style="margin-top: 30px; text-align: center; color: #666; font-size: 0.9em;">
                    <p>报告生成时间: """ + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def generate_word_report(self, docx_file):
        """生成Word格式的报告"""
        try:
            # 创建Word文档
            doc = Document()
            
            # 设置文档标题
            title = doc.add_heading(f'境外情报简报 - {self.selected_date}', level=0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加说明
            doc.add_paragraph('本简报汇总了以下X平台用户发布的信息:')
            users = doc.add_paragraph()
            users.add_run('李老师不是你老师 (@whyyoutouzhele)\n').bold = True
            users.add_run('多伦多方脸 (@torontobigface)\n').bold = True
            users.add_run('蔡慎坤 (@cskun1989)').bold = True
            
            # 添加各用户的推文
            usernames = list(self.tweets_data.keys())
            
            for username in usernames:
                display_name = self.username_to_nickname.get(username, username)
                
                # 添加用户标题
                doc.add_heading(f'{display_name} (@{username})', level=1)
                
                # 检查是否有该用户的推文
                if username not in self.tweets_data or not self.tweets_data[username]:
                    doc.add_paragraph(f'在{self.selected_date}没有找到该用户的推文。')
                    continue
                
                # 获取用户头像本地路径
                avatar_path = self.copied_avatars.get(username)
                if not avatar_path or not os.path.exists(avatar_path):
                    avatar_path = os.path.join(self.output_folder, "media", "default_avatar.jpg")
                
                # 遍历该用户的所有推文
                for tweet in self.tweets_data[username]:
                    tweet_id = tweet["id"]
                    tweet_text = tweet["text"]
                    created_at = tweet["created_at"]
                    user_info = tweet["user"]
                    
                    # 添加分隔线
                    doc.add_paragraph('_' * 50)
                    
                    # 添加推文标题
                    header = doc.add_paragraph()
                    try:
                        # 添加头像
                        if os.path.exists(avatar_path):
                            header.add_run().add_picture(avatar_path, width=Inches(0.5))
                    except Exception as e:
                        print(f"添加头像到Word文档失败: {str(e)}")
                    
                    # 添加用户信息和时间
                    info = doc.add_paragraph()
                    info.add_run(f"{user_info['name']} ").bold = True
                    info.add_run(f"@{user_info['screen_name']}")
                    info.add_run(f" · {created_at}")
                    
                    # 添加推文内容
                    doc.add_paragraph(tweet_text)
                    
                    # 添加媒体内容
                    has_media = False
                    if username in self.copied_media and tweet_id in self.copied_media[username]:
                        media_items = self.copied_media[username][tweet_id]
                        
                        for media in media_items:
                            media_type = media["type"]
                            local_path = media["local_path"]
                            
                            if not os.path.exists(local_path):
                                continue
                                
                            has_media = True
                            
                            if media_type == "photo":
                                # 添加图片
                                try:
                                    doc.add_picture(local_path, width=Inches(6))
                                except Exception as e:
                                    print(f"添加图片到Word文档失败: {str(e)}")
                            elif media_type == "video":
                                # 添加视频缩略图
                                try:
                                    doc.add_picture(local_path, width=Inches(6))
                                    p = doc.add_paragraph()
                                    p.add_run('视频链接: ').italic = True
                                    p.add_run(media["original_url"])
                                except Exception as e:
                                    print(f"添加视频缩略图到Word文档失败: {str(e)}")
                    
                    # 如果推文有媒体但未找到媒体文件
                    if tweet["media"] and not has_media:
                        p = doc.add_paragraph()
                        p.add_run('该推文包含媒体内容，但无法在本地找到媒体文件').italic = True
                    
                    # 添加推文链接
                    tweet_url = f"https://twitter.com/{username}/status/{tweet_id}"
                    link = doc.add_paragraph()
                    link.add_run('原始推文链接: ').italic = True
                    link.add_run(tweet_url)
            
            # 添加报告生成时间
            footer = doc.add_paragraph()
            footer.add_run('_' * 50)
            footer.add_run(f"\n报告生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}").italic = True
            
            # 保存文档
            doc.save(docx_file)
            
            return True
        except Exception as e:
            print(f"生成Word报告失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
            # 创建一个简单的Word文档说明文件，说明转换失败
            try:
                doc = Document()
                doc.add_heading("生成错误", 0)
                doc.add_paragraph(f"Word报告生成失败: {str(e)}")
                doc.save(docx_file)
            except:
                with open(docx_file, "w", encoding="utf-8") as f:
                    f.write("Word报告生成失败。\n错误信息: " + str(e))
            
            return False


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 设置主窗口属性
        self.setWindowTitle("每日境外情报汇总")
        self.resize(900, 700)
        
        # 设置应用图标
        self.setWindowIcon(QIcon("icon.png"))
        
        # 初始化变量
        self.proxy_settings = ProxySettings()
        self.twitter_api = TwitterAPI(self.proxy_settings)
        self.twitter_scraper = TwitterScraper(self.proxy_settings)
        self.tweets_data = {}
        self.media_data = {}
        self.current_report_html = ""
        self.current_report_path = ""
        self.current_report_folder = ""
        
        # 初始化空的用户名到昵称的映射
        self.username_to_nickname = {}
        
        # 初始化UI
        self.init_ui()
        
        # 加载配置
        self.load_config()
    
    def init_ui(self):
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 设置窗口标题和大小
        self.setWindowTitle("每日境外情报汇总")
        self.setMinimumSize(1000, 700)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 主页选项卡
        self.init_home_tab()
        
        # 设置选项卡
        self.init_settings_tab()
        
        # 报告选项卡
        self.init_report_tab()
        
        # 状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setAlignment(Qt.AlignCenter)
        self.progress_bar.setValue(0)
        self.progress_bar.setMaximumHeight(15)
        self.status_bar.addPermanentWidget(self.progress_bar, 1)
    
    def init_home_tab(self):
        home_tab = QWidget()
        home_layout = QVBoxLayout(home_tab)
        
        # 顶部区域
        top_layout = QHBoxLayout()
        
        # 日期选择器
        date_group = QGroupBox("选择日期")
        date_layout = QVBoxLayout(date_group)
        self.calendar = QCalendarWidget()
        self.calendar.setMaximumDate(QDate.currentDate())
        self.calendar.setSelectedDate(QDate.currentDate())
        date_layout.addWidget(self.calendar)
        
        top_layout.addWidget(date_group)
        
        # 用户选择
        users_group = QGroupBox("用户选择")
        users_layout = QVBoxLayout(users_group)
        
        # 创建一个用于显示已添加用户的滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        self.users_list_layout = QVBoxLayout(scroll_content)
        self.users_list_layout.setAlignment(Qt.AlignTop)
        scroll_area.setWidget(scroll_content)
        scroll_area.setMinimumHeight(150)
        
        users_layout.addWidget(scroll_area)
        
        # 初始化空的用户复选框字典
        self.user_checkboxes = {}
        
        # 自定义用户输入部分
        custom_user_group = QGroupBox("添加用户")
        custom_user_layout = QFormLayout(custom_user_group)
        
        self.custom_username_input = QLineEdit()
        self.custom_username_input.setPlaceholderText("用户名，如：whyyoutouzhele")
        
        self.custom_nickname_input = QLineEdit()
        self.custom_nickname_input.setPlaceholderText("昵称，如：李老师不是你老师")
        
        # 添加用户按钮
        buttons_layout = QHBoxLayout()
        
        add_custom_user_button = QPushButton("添加")
        add_custom_user_button.clicked.connect(self.add_custom_user)
        
        delete_user_button = QPushButton("删除选中")
        delete_user_button.clicked.connect(self.delete_selected_users)
        
        buttons_layout.addWidget(add_custom_user_button)
        buttons_layout.addWidget(delete_user_button)
        
        custom_user_layout.addRow("Twitter用户名:", self.custom_username_input)
        custom_user_layout.addRow("显示昵称:", self.custom_nickname_input)
        custom_user_layout.addRow("", buttons_layout)
        
        users_layout.addWidget(custom_user_group)
        
        # 添加说明信息
        note_label = QLabel("注意: 获取推文需要Twitter API访问凭证和代理设置。")
        note_label.setStyleSheet("color: #666;")
        users_layout.addWidget(note_label)
        
        # 添加下载模式选择
        download_mode_layout = QHBoxLayout()
        download_mode_label = QLabel("下载模式:")
        self.download_mode_combo = QComboBox()
        self.download_mode_combo.addItem("只下载图片", "images_only")
        self.download_mode_combo.addItem("下载图片和视频", "images_and_videos")
        download_mode_layout.addWidget(download_mode_label)
        download_mode_layout.addWidget(self.download_mode_combo)
        download_mode_layout.addStretch()
        users_layout.addLayout(download_mode_layout)
        
        # 添加操作按钮
        fetch_button = QPushButton("获取推文")
        fetch_button.clicked.connect(self.fetch_tweets)
        users_layout.addWidget(fetch_button)
        
        download_button = QPushButton("下载媒体")
        download_button.clicked.connect(self.download_media)
        download_button.setEnabled(False)
        self.download_button = download_button
        users_layout.addWidget(download_button)
        
        generate_button = QPushButton("生成报告")
        generate_button.clicked.connect(self.generate_report)
        generate_button.setEnabled(False)
        self.generate_button = generate_button
        users_layout.addWidget(generate_button)
        
        top_layout.addWidget(users_group)
        
        home_layout.addLayout(top_layout)
        
        # 推文预览区域
        tweets_group = QGroupBox("推文预览")
        tweets_layout = QVBoxLayout(tweets_group)
        
        self.tweets_preview = QTextEdit()
        self.tweets_preview.setReadOnly(True)
        tweets_layout.addWidget(self.tweets_preview)
        
        home_layout.addWidget(tweets_group)
        
        # 添加到选项卡
        self.tab_widget.addTab(home_tab, "主页")
    
    def init_settings_tab(self):
        settings_tab = QWidget()
        settings_layout = QVBoxLayout(settings_tab)
        
        # 代理设置
        proxy_group = QGroupBox("代理设置")
        proxy_layout = QFormLayout(proxy_group)
        
        # 启用代理
        self.proxy_enabled = QCheckBox("启用代理")
        proxy_layout.addRow(self.proxy_enabled)
        
        # 代理类型
        self.proxy_type = QComboBox()
        self.proxy_type.addItems(["SOCKS5", "SOCKS4", "HTTP"])
        proxy_layout.addRow("代理类型:", self.proxy_type)
        
        # 代理主机
        self.proxy_host = QLineEdit()
        proxy_layout.addRow("主机:", self.proxy_host)
        
        # 代理端口
        self.proxy_port = QLineEdit()
        self.proxy_port.setPlaceholderText("1080")
        proxy_layout.addRow("端口:", self.proxy_port)
        
        # 代理用户名
        self.proxy_username = QLineEdit()
        proxy_layout.addRow("用户名:", self.proxy_username)
        
        # 代理密码
        self.proxy_password = QLineEdit()
        self.proxy_password.setEchoMode(QLineEdit.Password)
        proxy_layout.addRow("密码:", self.proxy_password)
        
        # SSL验证选项
        self.proxy_verify_ssl = QCheckBox("验证SSL证书")
        self.proxy_verify_ssl.setChecked(False)
        self.proxy_verify_ssl.setToolTip("关闭此选项可能会解决SSL连接错误，但会降低安全性")
        proxy_layout.addRow(self.proxy_verify_ssl)
        
        settings_layout.addWidget(proxy_group)
        
        # Twitter API设置
        api_group = QGroupBox("Twitter API设置")
        api_layout = QFormLayout(api_group)
        
        # Consumer Key
        self.consumer_key = QLineEdit()
        api_layout.addRow("Consumer Key:", self.consumer_key)
        
        # Consumer Secret
        self.consumer_secret = QLineEdit()
        self.consumer_secret.setEchoMode(QLineEdit.Password)
        api_layout.addRow("Consumer Secret:", self.consumer_secret)
        
        # Access Token
        self.access_token = QLineEdit()
        api_layout.addRow("Access Token:", self.access_token)
        
        # Access Token Secret
        self.access_token_secret = QLineEdit()
        self.access_token_secret.setEchoMode(QLineEdit.Password)
        api_layout.addRow("Access Token Secret:", self.access_token_secret)
        
        settings_layout.addWidget(api_group)
        
        # Web爬取设置
        web_group = QGroupBox("Twitter Web设置 (备用方法)")
        web_layout = QFormLayout()
        
        self.authorization = QLineEdit()
        self.csrf_token = QLineEdit()
        self.cookies = QTextEdit()
        self.cookies.setMaximumHeight(100)
        
        web_layout.addRow("Authorization:", self.authorization)
        web_layout.addRow("X-CSRF-Token:", self.csrf_token)
        web_layout.addRow("Cookies:", self.cookies)
        
        self.use_api_method = QCheckBox("优先使用API (取消勾选则使用Web方式)")
        self.use_api_method.setChecked(True)
        
        web_group.setLayout(web_layout)
        
        # 连接设置
        connection_group = QGroupBox("连接设置")
        connection_layout = QFormLayout(connection_group)
        
        self.max_retries = QLineEdit()
        self.max_retries.setText("3")
        self.max_retries.setPlaceholderText("3")
        connection_layout.addRow("最大重试次数:", self.max_retries)
        
        self.connection_timeout = QLineEdit()
        self.connection_timeout.setText("30")
        self.connection_timeout.setPlaceholderText("30")
        connection_layout.addRow("连接超时(秒):", self.connection_timeout)
        
        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_config)
        
        settings_layout.addWidget(proxy_group)
        settings_layout.addWidget(api_group)
        settings_layout.addWidget(web_group)
        settings_layout.addWidget(connection_group)
        settings_layout.addWidget(self.use_api_method)
        settings_layout.addStretch()
        settings_layout.addWidget(save_btn)
        
        # 添加到选项卡
        self.tab_widget.addTab(settings_tab, "设置")
    
    def init_report_tab(self):
        """初始化报告选项卡"""
        report_tab = QWidget()
        report_layout = QVBoxLayout(report_tab)
        
        # 添加报告预览
        preview_label = QLabel("报告预览")
        preview_label.setFont(QFont('Microsoft YaHei', 12, QFont.Bold))
        
        self.report_preview = QTextEdit()
        self.report_preview.setReadOnly(True)
        
        # 添加报告格式选择
        format_group = QGroupBox("报告格式")
        format_layout = QHBoxLayout()
        
        self.format_html = QCheckBox("HTML")
        self.format_html.setChecked(True)  # 默认选中HTML
        self.format_pdf = QCheckBox("PDF")
        self.format_word = QCheckBox("Word")
        
        format_layout.addWidget(self.format_html)
        format_layout.addWidget(self.format_pdf)
        format_layout.addWidget(self.format_word)
        format_layout.addStretch()
        format_group.setLayout(format_layout)
        
        # 添加操作按钮
        button_layout = QHBoxLayout()
        
        self.open_report_button = QPushButton("打开报告")
        self.open_report_button.setEnabled(False)
        self.open_report_button.clicked.connect(self.open_report)
        
        self.open_folder_button = QPushButton("打开报告目录")
        self.open_folder_button.setEnabled(False)
        self.open_folder_button.clicked.connect(self.open_report_folder)
        
        button_layout.addWidget(self.open_report_button)
        button_layout.addWidget(self.open_folder_button)
        
        # 添加到布局
        report_layout.addWidget(preview_label)
        report_layout.addWidget(self.report_preview)
        report_layout.addWidget(format_group)
        report_layout.addLayout(button_layout)
        
        # 添加到选项卡
        self.tab_widget.addTab(report_tab, "报告")
        
        # 当前报告路径
        self.current_report_path = ""
        self.current_report_html = ""
        self.current_report_folder = ""
    
    def load_config(self):
        try:
            config = configparser.ConfigParser(interpolation=None)
            config.read("config.ini", encoding="utf-8")
            
            # 代理设置
            if config.has_section("proxy"):
                self.proxy_settings.enabled = config.getboolean("proxy", "enabled", fallback=False)
                self.proxy_settings.host = config.get("proxy", "host", fallback="")
                self.proxy_settings.port = config.getint("proxy", "port", fallback=0)
                self.proxy_settings.username = config.get("proxy", "username", fallback="")
                self.proxy_settings.password = config.get("proxy", "password", fallback="")
                self.proxy_settings.proxy_type = config.get("proxy", "proxy_type", fallback="SOCKS5")
                self.proxy_settings.verify_ssl = config.getboolean("proxy", "verify_ssl", fallback=False)
                
                # 更新UI
                self.proxy_enabled.setChecked(self.proxy_settings.enabled)
                self.proxy_host.setText(self.proxy_settings.host)
                self.proxy_port.setText(str(self.proxy_settings.port))
                self.proxy_username.setText(self.proxy_settings.username)
                self.proxy_password.setText(self.proxy_settings.password)
                self.proxy_type.setCurrentText(self.proxy_settings.proxy_type)
                self.proxy_verify_ssl.setChecked(self.proxy_settings.verify_ssl)
            
            # 连接设置
            if config.has_section("connection"):
                if hasattr(self, "max_retries"):
                    self.max_retries.setText(config.get("connection", "max_retries", fallback="3"))
                if hasattr(self, "connection_timeout"):
                    self.connection_timeout.setText(config.get("connection", "timeout", fallback="30"))
            
            # Twitter API设置
            self.twitter_api.setup_from_config(config)
            self.consumer_key.setText(self.twitter_api.consumer_key)
            self.consumer_secret.setText(self.twitter_api.consumer_secret)
            self.access_token.setText(self.twitter_api.access_token)
            self.access_token_secret.setText(self.twitter_api.access_token_secret)
            
            # Twitter网页爬虫设置
            if config.has_section("twitter_web"):
                self.twitter_scraper.setup_from_config(config)
                self.authorization.setText(self.twitter_scraper.authorization)
                self.csrf_token.setText(self.twitter_scraper.csrf_token)
                self.cookies.setText(self.twitter_scraper.cookies)
                
            # 选择的方法
            if config.has_section("settings"):
                self.use_api_method.setChecked(config.getboolean("settings", "use_api", fallback=True))
            
            # 用户设置
            if config.has_section("users"):
                users = config.get("users", "twitter_accounts", fallback="").split(",")
                users = [u.strip() for u in users if u.strip()]
                
                # 更新用户选择复选框
                for username, checkbox in self.user_checkboxes.items():
                    checkbox.setChecked(username in users)
            
            # 加载用户名到昵称的映射
            if config.has_section("nicknames"):
                # 清空现有的用户列表
                for username in list(self.user_checkboxes.keys()):
                    checkbox = self.user_checkboxes[username]
                    self.users_list_layout.removeWidget(checkbox)
                    checkbox.deleteLater()
                
                self.user_checkboxes = {}
                self.username_to_nickname = {}
                
                # 获取已保存的用户选择状态
                users = []
                if config.has_section("users"):
                    users = config.get("users", "twitter_accounts", fallback="").split(",")
                    users = [u.strip() for u in users if u.strip()]
                
                # 加载保存的昵称映射
                for username in config.options("nicknames"):
                    nickname = config.get("nicknames", username)
                    self.username_to_nickname[username] = nickname
                    
                    # 创建复选框并添加到用户列表
                    checkbox = QCheckBox(f"{nickname} (@{username})")
                    checkbox.setChecked(username in users)  # 根据users配置设置选中状态
                    self.user_checkboxes[username] = checkbox
                    self.users_list_layout.addWidget(checkbox)
            
            # 尝试认证
            self.twitter_api.authenticate()
            self.twitter_scraper.authenticate()
            
        except Exception as e:
            print(f"加载配置出错: {str(e)}")
            QMessageBox.warning(self, "配置加载错误", f"加载配置文件时出错: {str(e)}\n请检查配置文件格式是否正确。")
    
    def save_config(self):
        try:
            # 禁用interpolation可以防止%字符引起的问题
            config = configparser.ConfigParser(interpolation=None)
            
            # 代理设置
            if not config.has_section("proxy"):
                config.add_section("proxy")
                
            self.proxy_settings.enabled = self.proxy_enabled.isChecked()
            self.proxy_settings.host = self.proxy_host.text()
            self.proxy_settings.port = int(self.proxy_port.text()) if self.proxy_port.text().isdigit() else 0
            self.proxy_settings.username = self.proxy_username.text()
            self.proxy_settings.password = self.proxy_password.text()
            self.proxy_settings.proxy_type = self.proxy_type.currentText()
            self.proxy_settings.verify_ssl = self.proxy_verify_ssl.isChecked()
            
            config.set("proxy", "enabled", str(self.proxy_settings.enabled))
            config.set("proxy", "host", self.proxy_settings.host)
            config.set("proxy", "port", str(self.proxy_settings.port))
            config.set("proxy", "username", self.proxy_settings.username)
            config.set("proxy", "password", self.proxy_settings.password)
            config.set("proxy", "proxy_type", self.proxy_settings.proxy_type)
            config.set("proxy", "verify_ssl", str(self.proxy_settings.verify_ssl))
            
            # 连接设置
            if not config.has_section("connection"):
                config.add_section("connection")
            
            if hasattr(self, "max_retries"):
                config.set("connection", "max_retries", self.max_retries.text())
            if hasattr(self, "connection_timeout"):
                config.set("connection", "timeout", self.connection_timeout.text())
                
            # 更新Twitter爬虫设置
            if hasattr(self.twitter_scraper, "max_retries"):
                try:
                    self.twitter_scraper.max_retries = int(self.max_retries.text())
                except:
                    self.twitter_scraper.max_retries = 3
                    
            if hasattr(self.twitter_scraper, "timeout"):
                try:
                    self.twitter_scraper.timeout = int(self.connection_timeout.text())
                except:
                    self.twitter_scraper.timeout = 30
            
            # Twitter API设置
            if not config.has_section("twitter_api"):
                config.add_section("twitter_api")
                
            self.twitter_api.consumer_key = self.consumer_key.text()
            self.twitter_api.consumer_secret = self.consumer_secret.text()
            self.twitter_api.access_token = self.access_token.text()
            self.twitter_api.access_token_secret = self.access_token_secret.text()
            
            config.set("twitter_api", "consumer_key", self.twitter_api.consumer_key)
            config.set("twitter_api", "consumer_secret", self.twitter_api.consumer_secret)
            config.set("twitter_api", "access_token", self.twitter_api.access_token)
            config.set("twitter_api", "access_token_secret", self.twitter_api.access_token_secret)
            
            # Twitter Web设置
            if not config.has_section("twitter_web"):
                config.add_section("twitter_web")
                
            self.twitter_scraper.authorization = self.authorization.text()
            self.twitter_scraper.csrf_token = self.csrf_token.text()
            self.twitter_scraper.cookies = self.cookies.toPlainText()
            
            # 使用twitter_scraper的save_to_config方法，它会自动处理%字符
            self.twitter_scraper.save_to_config(config)
            
            # 使用方法选择
            if not config.has_section("settings"):
                config.add_section("settings")
            config.set("settings", "use_api", str(self.use_api_method.isChecked()))
            
            # 用户设置
            if not config.has_section("users"):
                config.add_section("users")
                
            # 获取选中的用户
            selected_users = [
                username for username, checkbox in self.user_checkboxes.items()
                if checkbox.isChecked()
            ]
            config.set("users", "twitter_accounts", ",".join(selected_users))
            
            # 保存用户名到昵称的映射
            if not config.has_section("nicknames"):
                config.add_section("nicknames")
                
            # 将用户名到昵称的映射保存到配置文件
            for username, nickname in self.username_to_nickname.items():
                config.set("nicknames", username, nickname)
            
            # 保存配置
            with open("config.ini", "w", encoding="utf-8") as f:
                config.write(f)
                
            # 重新认证
            self.twitter_api.authenticate()
            self.twitter_scraper.authenticate()
            
            QMessageBox.information(self, "保存成功", "配置已保存")
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置时出错: {str(e)}")
    
    def fetch_tweets(self):
        # 获取选中的用户
        selected_users = [
            username for username, checkbox in self.user_checkboxes.items()
            if checkbox.isChecked()
        ]
        
        if not selected_users:
            QMessageBox.warning(self, "错误", "请至少选择一个用户")
            return
        
        # 获取选择的日期并转换为date对象
        selected_date_str = self.calendar.selectedDate().toString("yyyy-MM-dd")
        selected_date = datetime.datetime.strptime(selected_date_str, "%Y-%m-%d").date()
        
        # 检查认证状态
        use_api = self.use_api_method.isChecked()
        if use_api and not self.twitter_api.authenticated:
            QMessageBox.warning(
                self, "错误", 
                "Twitter API尚未通过认证，请检查API密钥设置"
            )
            return
        elif not use_api and not self.twitter_scraper.authenticated:
            QMessageBox.warning(
                self, "错误", 
                "Twitter Web认证信息无效，请在设置中填写正确的授权信息"
            )
            return
        
        # 清空之前的数据
        self.tweets_data = {}
        self.media_data = {}
        self.current_report_html = ""
        self.current_report_path = ""
        self.tweets_preview.clear()
        
        # 创建并启动线程
        self.fetch_thread = FetchTweetsThread(
            twitter_api=self.twitter_api,
            twitter_scraper=self.twitter_scraper,
            usernames=selected_users,
            selected_date=selected_date,
            count=50,
            use_api=use_api
        )
        
        self.fetch_thread.update_progress.connect(self.update_progress)
        self.fetch_thread.update_status.connect(self.update_status)
        self.fetch_thread.fetch_complete.connect(self.on_fetch_complete)
        self.fetch_thread.fetch_error.connect(self.on_fetch_error)
        
        # 禁用按钮
        self.download_button.setEnabled(False)
        self.generate_button.setEnabled(False)
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 启动线程
        self.fetch_thread.start()
    
    def download_media(self):
        if not self.tweets_data:
            QMessageBox.warning(self, "错误", "请先获取推文")
            return
        
        # 获取当前选择的下载模式
        download_mode = self.download_mode_combo.currentData()
        
        # 选择保存目录
        download_folder = QFileDialog.getExistingDirectory(
            self,
            "选择保存目录",
            "",
            QFileDialog.ShowDirsOnly
        )
        
        if not download_folder:
            return
        
        # 创建并启动线程
        self.download_thread = DownloadMediaThread(
            tweets_data=self.tweets_data,
            download_folder=download_folder,
            proxy_settings=self.proxy_settings,
            download_mode=download_mode  # 传递下载模式
        )
        
        self.download_thread.update_progress.connect(self.update_progress)
        self.download_thread.update_status.connect(self.update_status)
        self.download_thread.download_complete.connect(self.on_download_complete)
        self.download_thread.download_error.connect(self.on_download_error)
        
        self.download_thread.start()
    
    def generate_report(self):
        if not self.tweets_data:
            QMessageBox.warning(self, "错误", "请先获取推文")
            return
        
        if not self.media_data:
            # 提醒用户他们没有下载媒体
            result = QMessageBox.question(
                self, 
                "确认", 
                "你还没有下载媒体文件，报告中将不会包含图片和视频。是否继续生成报告？",
                QMessageBox.Yes | QMessageBox.No
            )
            if result == QMessageBox.No:
                return
              
        # 检查是否至少选择了一种格式
        selected_formats = []
        if self.format_html.isChecked():
            selected_formats.append("html")
        if self.format_pdf.isChecked():
            selected_formats.append("pdf")
        if self.format_word.isChecked():
            selected_formats.append("word")
            
        if not selected_formats:
            QMessageBox.warning(self, "错误", "请至少选择一种报告格式")
            return
            
        # 选择保存目录
        output_folder = QFileDialog.getExistingDirectory(
            self,
            "选择报告保存目录",
            "",
            QFileDialog.ShowDirsOnly
        )
        
        if not output_folder:
            return
            
        self.current_report_folder = output_folder
        
        # 获取选择的日期
        selected_date_str = self.calendar.selectedDate().toString("yyyy-MM-dd")
        selected_date = datetime.datetime.strptime(selected_date_str, "%Y-%m-%d").date()
        
        # 创建并启动线程
        self.report_thread = GenerateReportThread(
            tweets_data=self.tweets_data,
            media_data=self.media_data,
            selected_date=selected_date,
            output_folder=output_folder,
            formats=selected_formats,
            username_to_nickname=self.username_to_nickname
        )
        
        self.report_thread.update_progress.connect(self.update_progress)
        self.report_thread.update_status.connect(self.update_status)
        self.report_thread.report_complete.connect(self.on_report_complete)
        self.report_thread.report_error.connect(self.on_report_error)
        
        self.report_thread.start()
    
    def update_progress(self, current, total):
        progress = int((current / total) * 100) if total > 0 else 0
        self.progress_bar.setValue(progress)
    
    def update_status(self, message):
        self.status_bar.showMessage(message)
    
    def on_fetch_complete(self, tweets_data):
        self.tweets_data = tweets_data
        self.download_button.setEnabled(True)
        
        # 更新预览
        preview_text = ""
        for username, tweets in tweets_data.items():
            # 使用昵称而不是用户名
            display_name = self.username_to_nickname.get(username, username)
            
            preview_text += f"{display_name} (@{username}) 的推文 ({len(tweets)}条):\n"
            preview_text += "-" * 50 + "\n"
            
            for tweet in tweets:
                preview_text += f"时间: {tweet['created_at']}\n"
                preview_text += f"内容: {tweet['text']}\n"
                preview_text += f"媒体: {len(tweet['media'])}个\n"
                preview_text += "-" * 50 + "\n"
        
        self.tweets_preview.setText(preview_text)
        
        QMessageBox.information(self, "完成", "推文获取完成")
    
    def on_fetch_error(self, error_message):
        QMessageBox.warning(self, "错误", error_message)
    
    def on_download_complete(self, media_data):
        self.media_data = media_data
        self.generate_button.setEnabled(True)
        QMessageBox.information(self, "完成", "媒体下载完成")
    
    def on_download_error(self, error_message):
        QMessageBox.warning(self, "错误", error_message)
    
    def on_report_complete(self, report_files):
        """报告生成完成的回调"""
        self.current_report_files = report_files
        
        # 更新报告预览 (如果有HTML)
        if "html" in report_files:
            html_file = report_files["html"]
            try:
                with open(html_file, "r", encoding="utf-8") as f:
                    self.current_report_html = f.read()
                    self.report_preview.setHtml(self.current_report_html)
            except Exception as e:
                print(f"读取HTML报告失败: {str(e)}")
        
        # 优先选择文档格式来打开
        if "pdf" in report_files:
            self.current_report_path = report_files["pdf"]
        elif "word" in report_files:
            self.current_report_path = report_files["word"]
        elif "html" in report_files:
            self.current_report_path = report_files["html"]
        
        # 启用打开按钮
        self.open_report_button.setEnabled(True)
        self.open_folder_button.setEnabled(True)
        
        # 显示生成结果提示
        formats_str = ", ".join(report_files.keys()).upper()
        QMessageBox.information(self, "完成", f"报告生成完成，格式: {formats_str}")
    
    def on_report_error(self, error_message):
        QMessageBox.critical(self, "报告生成错误", error_message)
    
    def open_report(self):
        # 打开最后生成的报告
        if self.current_report_path and os.path.exists(self.current_report_path):
            # 使用系统默认程序打开文件
            webbrowser.open(self.current_report_path)
        else:
            QMessageBox.warning(self, "错误", "没有可用的报告")
    
    def open_report_folder(self):
        # 打开报告所在文件夹
        if self.current_report_folder and os.path.exists(self.current_report_folder):
            # 使用系统资源管理器打开目录
            if sys.platform == 'win32':
                os.startfile(self.current_report_folder)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', self.current_report_folder])
            else:  # Linux
                subprocess.call(['xdg-open', self.current_report_folder])
        else:
            QMessageBox.warning(self, "错误", "没有可用的报告")
    
    def add_custom_user(self):
        username = self.custom_username_input.text().strip()
        nickname = self.custom_nickname_input.text().strip()
        
        if not username or not nickname:
            QMessageBox.warning(self, "输入错误", "用户名和昵称都不能为空")
            return
        
        # 检查用户名是否已存在
        if username in self.user_checkboxes:
            QMessageBox.warning(self, "用户已存在", f"用户 @{username} 已在列表中")
            return
        
        # 创建新的复选框并添加到用户列表中
        checkbox = QCheckBox(f"{nickname} (@{username})")
        checkbox.setChecked(True)
        self.user_checkboxes[username] = checkbox
        
        # 添加到用户名-昵称映射中
        self.username_to_nickname[username] = nickname
        
        # 添加到用户列表布局
        self.users_list_layout.addWidget(checkbox)
                
        # 清空输入框
        self.custom_username_input.clear()
        self.custom_nickname_input.clear()
        
        # 保存配置
        self.save_config()
        
        QMessageBox.information(self, "添加成功", f"已添加用户: {nickname} (@{username})")

    def delete_selected_users(self):
        # 获取所有选中要删除的用户
        users_to_delete = []
        
        for username, checkbox in self.user_checkboxes.items():
            if checkbox.isChecked():
                users_to_delete.append(username)
        
        if not users_to_delete:
            QMessageBox.warning(self, "提示", "请先选择要删除的用户")
            return
        
        # 确认删除
        result = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除选中的 {len(users_to_delete)} 个用户吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if result == QMessageBox.No:
            return
        
        # 执行删除
        for username in users_to_delete:
            # 从布局中移除复选框
            checkbox = self.user_checkboxes[username]
            self.users_list_layout.removeWidget(checkbox)
            # 清除复选框
            checkbox.deleteLater()
            # 从映射中移除
            self.user_checkboxes.pop(username)
            # 从昵称映射中移除
            if username in self.username_to_nickname:
                self.username_to_nickname.pop(username)
        
        # 保存配置
        self.save_config()
        
        QMessageBox.information(self, "删除成功", f"已删除 {len(users_to_delete)} 个用户")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle("Fusion")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())