app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 股票分析系统
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.2.6@2a4ffc697957afb7734e7a5cfb780d5c819d216cc6e56798bffb95d28a032867
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables:
  - description: ''
    id: c5450db2-7eed-4bc5-bc33-604cc84307bd
    name: apikey
    selector:
    - env
    - apikey
    value: xue1234
    value_type: string
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: if-else
      id: 1749172200501-source-1749176453865-target
      selected: false
      source: '1749172200501'
      sourceHandle: source
      target: '1749176453865'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 1749176453865-true-1749176531422-target
      selected: false
      source: '1749176453865'
      sourceHandle: 'true'
      target: '1749176531422'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: http-request
        targetType: code
      id: 1749176620821-source-1749176917139-target
      selected: false
      source: '1749176620821'
      sourceHandle: source
      target: '1749176917139'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: if-else
      id: 1749176917139-source-1749177019596-target
      selected: false
      source: '1749176917139'
      sourceHandle: source
      target: '1749177019596'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1749177019596-true-1749177149645-target
      selected: false
      source: '1749177019596'
      sourceHandle: 'true'
      target: '1749177149645'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1749177019596-25d620ae-2213-47b1-93be-870176116b8f-1749177151713-target
      selected: false
      source: '1749177019596'
      sourceHandle: 25d620ae-2213-47b1-93be-870176116b8f
      target: '1749177151713'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1749177019596-1c1672cc-0675-47e5-ba1c-46aeb727b581-1749177154055-target
      selected: false
      source: '1749177019596'
      sourceHandle: 1c1672cc-0675-47e5-ba1c-46aeb727b581
      target: '1749177154055'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1749177019596-false-1749177156539-target
      selected: false
      source: '1749177019596'
      sourceHandle: 'false'
      target: '1749177156539'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1749177149645-source-1749177325682-target
      selected: false
      source: '1749177149645'
      sourceHandle: source
      target: '1749177325682'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1749177151713-source-1749177549359-target
      selected: false
      source: '1749177151713'
      sourceHandle: source
      target: '1749177549359'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1749177154055-source-1749177564902-target
      selected: false
      source: '1749177154055'
      sourceHandle: source
      target: '1749177564902'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1749177156539-source-1749177606767-target
      selected: false
      source: '1749177156539'
      sourceHandle: source
      target: '1749177606767'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: http-request
      id: 1751187185699-source-1749176620821-target
      selected: false
      source: '1751187185699'
      sourceHandle: source
      target: '1749176620821'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: code
      id: 1749176453865-false-1751187185699-target
      selected: false
      source: '1749176453865'
      sourceHandle: 'false'
      target: '1751187185699'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 下拉选择股票代码
          max_length: 48
          options:
          - '000333'
          - '600967'
          - 0700.HK
          - TSLA
          - '510300'
          required: false
          type: select
          variable: stockcode1
        - label: 手动输入股票代码
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: stockcode2
        - label: 市场类型
          max_length: 48
          options:
          - A
          - HK
          - US
          - ETF
          required: true
          type: select
          variable: marketType
      height: 142
      id: '1749172200501'
      position:
        x: 30
        y: 276
      positionAbsolute:
        x: 30
        y: 276
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: 0fbf5054-8009-4544-a111-3ada34262580
            value: ''
            varType: string
            variable_selector:
            - '1749172200501'
            - stockcode1
          - comparison_operator: empty
            id: 2391bf47-a45a-46b2-90a9-bce3aa5b3a89
            value: ''
            varType: string
            variable_selector:
            - '1749172200501'
            - stockcode2
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支
        type: if-else
      height: 152
      id: '1749176453865'
      position:
        x: 334
        y: 276
      positionAbsolute:
        x: 334
        y: 276
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: 股票代码不能为空，请重新输入！
        desc: ''
        selected: false
        title: 错误回复
        type: answer
        variables: []
      height: 102
      id: '1749176531422'
      position:
        x: 638
        y: 276
      positionAbsolute:
        x: 638
        y: 276
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - id: key-value-368
            key: ''
            type: text
            value: "{\n  \"stock_code\":\"{{#1751187185699.stock_code#}}\",\n  \"\
              market_type\":\"{{#1749172200501.marketType#}}\"\n}"
          type: json
        desc: ''
        headers: 'Content-Type:application/json

          Authorization:bearer {{#env.apikey#}}'
        method: post
        params: ''
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 100
        selected: false
        ssl_verify: true
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: HTTP 请求
        type: http-request
        url: http://192.168.1.14:8020/analyze-stock/
        variables: []
      height: 140
      id: '1749176620821'
      position:
        x: 942
        y: 394
      positionAbsolute:
        x: 942
        y: 394
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\ndef main(arg1: str) -> str:\n    print(arg1)\n    data\
          \ = json.loads(arg1)\n    technical_summary = data[\"technical_summary\"\
          ]\n    recent_data = data['recent_data']\n    report = data['report']\n\
          \    return {\n        \"technical_summary\": json.dumps(technical_summary,\
          \ ensure_ascii=False, indent=2),\n        \"recent_data\": json.dumps(recent_data,\
          \ ensure_ascii=False, indent=2),\n        \"report\": json.dumps(report,\
          \ ensure_ascii=False, indent=2),\n    }"
        code_language: python3
        desc: ''
        outputs:
          recent_data:
            children: null
            type: string
          report:
            children: null
            type: string
          technical_summary:
            children: null
            type: string
        selected: false
        title: 代码执行
        type: code
        variables:
        - value_selector:
          - '1749176620821'
          - body
          variable: arg1
      height: 54
      id: '1749176917139'
      position:
        x: 1246
        y: 394
      positionAbsolute:
        x: 1246
        y: 394
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: 5a900b70-2d0a-4e2f-82d0-5f1000f90dcb
            value: A
            varType: string
            variable_selector:
            - '1749172200501'
            - marketType
          id: 'true'
          logical_operator: or
        - case_id: 25d620ae-2213-47b1-93be-870176116b8f
          conditions:
          - comparison_operator: contains
            id: 5542d6b4-e5f1-455e-bd63-031265172ca9
            value: HK
            varType: string
            variable_selector:
            - '1749172200501'
            - marketType
          id: 25d620ae-2213-47b1-93be-870176116b8f
          logical_operator: and
        - case_id: 1c1672cc-0675-47e5-ba1c-46aeb727b581
          conditions:
          - comparison_operator: contains
            id: c050eac6-ff9a-45aa-9b2d-bb98564104b9
            value: US
            varType: string
            variable_selector:
            - '1749172200501'
            - marketType
          id: 1c1672cc-0675-47e5-ba1c-46aeb727b581
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 2
        type: if-else
      height: 222
      id: '1749177019596'
      position:
        x: 1550
        y: 394
      positionAbsolute:
        x: 1550
        y: 394
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.5-flash-preview-05-20
          provider: langgenius/gemini/google
        prompt_template:
        - id: cfaf8943-84e3-4850-8aba-978dc751bc0c
          role: system
          text: '分析A股{{#1751187185699.stock_code#}}：

            技术指标概要：

            {{#1749176917139.technical_summary#}}

            近14日交易数据：

            {{#1749176917139.recent_data#}}


            请提供：

            1.趋势分析（包含支撑位和压力位）

            2.成交量分析及其含义

            3.风险评估（包含波动率分析）

            4.短期和中期目标价位

            5.关键技术位分析

            6.具体交易建议（包含止损位）

            请基于技术指标和A股市场特点进行分析，给出具体数据支

            持。

            7.根据{{#1749176917139.report#}}实时技术指标分析给出当前交易策略

            '
        selected: false
        title: A股分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1749177149645'
      position:
        x: 1854
        y: 394
      positionAbsolute:
        x: 1854
        y: 394
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.5-flash-preview-05-20
          provider: langgenius/gemini/google
        prompt_template:
        - id: 5cb1a691-ca71-452a-a29d-bed38460ae0d
          role: system
          text: '分析HK股{{#1751187185699.stock_code#}}：

            技术指标概要：

            {{#1749176917139.technical_summary#}}

            近14日交易数据：

            {{#1749176917139.recent_data#}}


            请提供：

            1.趋势分析（包含支撑位和压力位）

            2.成交量分析及其含义

            3.风险评估（包含波动率分析）

            4.短期和中期目标价位

            5.关键技术位分析

            6.具体交易建议（包含止损位）

            请基于技术指标和A股市场特点进行分析，给出具体数据支

            持。

            7.根据{{#1749176917139.report#}}实时技术指标分析给出当前交易策略

            '
        selected: false
        title: 港股分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1749177151713'
      position:
        x: 1854
        y: 548.5
      positionAbsolute:
        x: 1854
        y: 548.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.5-flash-preview-05-20
          provider: langgenius/gemini/google
        prompt_template:
        - id: d6013d6d-89e0-4a0a-a9f8-b3eb3d67d978
          role: system
          text: '分析US股{{#1751187185699.stock_code#}}：

            技术指标概要：

            {{#1749176917139.technical_summary#}}

            近14日交易数据：

            {{#1749176917139.recent_data#}}


            请提供：

            1.趋势分析（包含支撑位和压力位）

            2.成交量分析及其含义

            3.风险评估（包含波动率分析）

            4.短期和中期目标价位

            5.关键技术位分析

            6.具体交易建议（包含止损位）

            请基于技术指标和A股市场特点进行分析，给出具体数据支

            持。

            7.根据{{#1749176917139.report#}}实时技术指标分析给出当前交易策略

            '
        selected: false
        title: 美股分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1749177154055'
      position:
        x: 1854
        y: 693.5
      positionAbsolute:
        x: 1854
        y: 693.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.5-flash-preview-05-20
          provider: langgenius/gemini/google
        prompt_template:
        - id: a90d5f21-0d26-427f-a972-8a21fdb9eb95
          role: system
          text: '分析ETF股{{#1751187185699.stock_code#}}：

            技术指标概要：

            {{#1749176917139.technical_summary#}}

            近14日交易数据：

            {{#1749176917139.recent_data#}}


            请提供：

            1.趋势分析（包含支撑位和压力位）

            2.成交量分析及其含义

            3.风险评估（包含波动率分析）

            4.短期和中期目标价位

            5.关键技术位分析

            6.具体交易建议（包含止损位）

            请基于技术指标和A股市场特点进行分析，给出具体数据支

            持。

            7.根据{{#1749176917139.report#}}实时技术指标分析给出当前交易策略

            '
        selected: false
        title: 交易所交易基金分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1749177156539'
      position:
        x: 1854
        y: 838.5
      positionAbsolute:
        x: 1854
        y: 838.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1751187185699.stock_code#}}

          {{#1749177149645.text#}}'
        desc: ''
        selected: false
        title: 直接回复 2
        type: answer
        variables: []
      height: 124
      id: '1749177325682'
      position:
        x: 2158
        y: 394
      positionAbsolute:
        x: 2158
        y: 394
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1749177151713.text#}}'
        desc: ''
        selected: false
        title: 直接回复 3
        type: answer
        variables: []
      height: 105
      id: '1749177549359'
      position:
        x: 2158
        y: 558
      positionAbsolute:
        x: 2158
        y: 558
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1749177154055.text#}}'
        desc: ''
        selected: false
        title: 直接回复 4
        type: answer
        variables: []
      height: 105
      id: '1749177564902'
      position:
        x: 2158
        y: 703
      positionAbsolute:
        x: 2158
        y: 703
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1749177156539.text#}}'
        desc: ''
        selected: false
        title: 直接回复 5
        type: answer
        variables: []
      height: 105
      id: '1749177606767'
      position:
        x: 2158
        y: 848
      positionAbsolute:
        x: 2158
        y: 848
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(code1: str, code2: str) -> dict:\n    # 优先使用手动输入的\
          \ code2，如果它不为空白字符串\n    # .strip() 用于去除前后空格，防止用户误输入\n    final_code = \"\
          \"\n    if code2 and code2.strip():\n        final_code = code2.strip()\n\
          \    # 如果 code2 为空，则使用下拉选择的 code1\n    elif code1 and code1.strip():\n \
          \       final_code = code1.strip()\n\n    # 以字典形式返回最终的股票代码\n    return {\n\
          \        \"stock_code\": final_code\n    }"
        code_language: python3
        desc: ''
        outputs:
          stock_code:
            children: null
            type: string
        selected: false
        title: 选择股票代码
        type: code
        variables:
        - value_selector:
          - '1749172200501'
          - stockcode1
          variable: code1
        - value_selector:
          - '1749172200501'
          - stockcode2
          variable: code2
      height: 54
      id: '1751187185699'
      position:
        x: 638
        y: 418
      positionAbsolute:
        x: 638
        y: 418
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -352.8799999999999
      y: -154.86
      zoom: 1.008
