/**
 * Environment configuration unit tests
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { getEnvConfig, loadEnvironmentVariables, ENV_VARS } from '../../../src/config/environment.js';
import { McpError } from '@modelcontextprotocol/sdk/types.js';
import { ErrorCode } from '../../../src/errors/error-codes.js';
import { mockEnv } from '../../test-setup.js';

describe('Environment Configuration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
    // Clear environment variables that might interfere with tests
    delete process.env[ENV_VARS.N8N_API_URL];
    delete process.env[ENV_VARS.N8N_API_KEY];
    delete process.env[ENV_VARS.DEBUG];
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('loadEnvironmentVariables', () => {
    it('should load environment variables from .env file', () => {
      // This is mostly a coverage test, as we can't easily verify dotenv behavior
      expect(() => loadEnvironmentVariables()).not.toThrow();
    });
  });

  describe('getEnvConfig', () => {
    it('should return a valid config when all required variables are present', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_URL] = 'https://n8n.example.com/api/v1';
      process.env[ENV_VARS.N8N_API_KEY] = 'test-api-key';
      
      // Execute
      const config = getEnvConfig();
      
      // Assert
      expect(config).toEqual({
        n8nApiUrl: 'https://n8n.example.com/api/v1',
        n8nApiKey: 'test-api-key',
        debug: false,
      });
    });

    it('should set debug to true when DEBUG=true', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_URL] = 'https://n8n.example.com/api/v1';
      process.env[ENV_VARS.N8N_API_KEY] = 'test-api-key';
      process.env[ENV_VARS.DEBUG] = 'true';
      
      // Execute
      const config = getEnvConfig();
      
      // Assert
      expect(config.debug).toBe(true);
    });

    it('should handle uppercase true for DEBUG', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_URL] = 'https://n8n.example.com/api/v1';
      process.env[ENV_VARS.N8N_API_KEY] = 'test-api-key';
      process.env[ENV_VARS.DEBUG] = 'TRUE';
      
      // Execute
      const config = getEnvConfig();
      
      // Assert
      expect(config.debug).toBe(true);
    });

    it('should set debug to false for non-true values', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_URL] = 'https://n8n.example.com/api/v1';
      process.env[ENV_VARS.N8N_API_KEY] = 'test-api-key';
      process.env[ENV_VARS.DEBUG] = 'yes';
      
      // Execute
      const config = getEnvConfig();
      
      // Assert
      expect(config.debug).toBe(false);
    });

    it('should throw an error when N8N_API_URL is missing', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_KEY] = 'test-api-key';
      
      // Execute & Assert
      expect(() => getEnvConfig()).toThrow(
        new McpError(
          ErrorCode.InitializationError,
          `Missing required environment variable: ${ENV_VARS.N8N_API_URL}`
        )
      );
    });

    it('should throw an error when N8N_API_KEY is missing', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_URL] = 'https://n8n.example.com/api/v1';
      
      // Execute & Assert
      expect(() => getEnvConfig()).toThrow(
        new McpError(
          ErrorCode.InitializationError,
          `Missing required environment variable: ${ENV_VARS.N8N_API_KEY}`
        )
      );
    });

    it('should throw an error when N8N_API_URL is not a valid URL', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_URL] = 'invalid-url';
      process.env[ENV_VARS.N8N_API_KEY] = 'test-api-key';
      
      // Execute & Assert
      expect(() => getEnvConfig()).toThrow(
        new McpError(
          ErrorCode.InitializationError,
          `Invalid URL format for ${ENV_VARS.N8N_API_URL}: invalid-url`
        )
      );
    });

    it('should allow localhost URLs', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_URL] = 'http://localhost:5678/api/v1';
      process.env[ENV_VARS.N8N_API_KEY] = 'test-api-key';
      
      // Execute
      const config = getEnvConfig();
      
      // Assert
      expect(config.n8nApiUrl).toBe('http://localhost:5678/api/v1');
    });

    it('should accept URLs with trailing slashes', () => {
      // Setup
      process.env[ENV_VARS.N8N_API_URL] = 'https://n8n.example.com/api/v1/';
      process.env[ENV_VARS.N8N_API_KEY] = 'test-api-key';
      
      // Execute
      const config = getEnvConfig();
      
      // Assert
      expect(config.n8nApiUrl).toBe('https://n8n.example.com/api/v1/');
    });
  });

  describe('with mockEnv helper', () => {
    // Using the mockEnv helper from test-setup
    mockEnv({
      [ENV_VARS.N8N_API_URL]: 'https://n8n.example.com/api/v1',
      [ENV_VARS.N8N_API_KEY]: 'test-api-key',
    });

    it('should use the mocked environment variables', () => {
      const config = getEnvConfig();
      expect(config.n8nApiUrl).toBe('https://n8n.example.com/api/v1');
      expect(config.n8nApiKey).toBe('test-api-key');
    });
  });
});
