import sys
import os
import PySide2

# 设置Qt插件路径
if hasattr(sys, 'frozen'):
    os.environ['PATH'] = sys._MEIPASS + ";" + os.environ['PATH']
dirname = os.path.dirname(PySide2.__file__)
plugin_path = os.path.join(dirname, 'plugins', 'platforms')
os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = plugin_path

import pandas as pd
from datetime import datetime
from PySide2.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QPushButton, QFileDialog, QComboBox, QLabel, 
                            QMessageBox, QHBoxLayout, QCheckBox, QGroupBox)
from PySide2.QtCore import Qt

class IDCardProcessor:
    def __init__(self):
        try:
            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 构建完整的文件路径
            file_path = os.path.join(current_dir, '行政区域代码与省市县.xlsx')
            # 加载行政区域代码数据
            self.area_data = pd.read_excel(file_path)
            # 确保行政区划代码列的类型是字符串
            self.area_data['行政区划代码'] = self.area_data['行政区划代码'].astype(str)
        except Exception as e:
            print(f"加载行政区域代码数据失败: {e}")
            raise
    
    def validate_id_card(self, id_card):
        """验证身份证号码是否合法
        返回值：
        - "正确": 身份证号码合法
        - "位数不对": 身份证号码位数不是18位
        - "错误": 身份证号码不合法（校验码错误或包含非法字符）
        """
        # 检查长度
        if len(id_card) != 18:
            return "位数不对"
        
        # 验证前17位是否都是数字
        if not id_card[:17].isdigit():
            return "错误"
        
        # 验证最后一位校验码
        factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        checksum = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum = 0
        for i in range(17):
            sum += int(id_card[i]) * factors[i]
        
        if checksum[sum % 11] != id_card[-1].upper():
            return "错误"
            
        return "正确"

    def get_age(self, id_card, calc_date=None):
        """计算年龄
        当天日期减1天再减去出生日期，计算周岁年龄
        例如：2000年1月1日出生，2024年1月1日为23岁，2024年1月2日才满24岁
        
        参数：
        - id_card: 身份证号码
        - calc_date: 计算日期，可以是datetime对象或字符串格式的日期
        """
        birth_year = int(id_card[6:10])
        birth_month = int(id_card[10:12])
        birth_day = int(id_card[12:14])
        birth_date = datetime(birth_year, birth_month, birth_day)
        
        # 处理计算日期
        if calc_date is None:
            # 使用当前日期
            calc_date = datetime.now()
        elif isinstance(calc_date, str):
            # 处理字符串格式的日期
            try:
                # 尝试处理datetime格式 "2024-09-16 00:00:52"
                calc_date = datetime.strptime(calc_date.split()[0], '%Y-%m-%d')
            except:
                try:
                    # 尝试处理date格式 "2024-09-16"
                    calc_date = datetime.strptime(calc_date, '%Y-%m-%d')
                except:
                    # 如果都失败，使用当前日期
                    calc_date = datetime.now()
        
        # 计算日期减1天
        from datetime import timedelta
        yesterday = calc_date - timedelta(days=1)
        
        # 计算年龄
        age = yesterday.year - birth_year
        
        # 如果减去1天后的日期小于生日，年龄减1
        if (yesterday.month, yesterday.day) < (birth_month, birth_day):
            age -= 1
            
        return age

    def get_gender(self, id_card):
        """获取性别"""
        return "女" if int(id_card[-2]) % 2 == 0 else "男"

    def get_birth_date(self, id_card):
        """获取出生日期"""
        birth_date = f"{id_card[6:10]}-{id_card[10:12]}-{id_card[12:14]}"
        return birth_date

    def get_area(self, id_card):
        """获取户籍所在地"""
        try:
            area_code = str(id_card[:6])  # 确保是字符串类型
            # 打印调试信息
            print(f"查找区域代码: {area_code}")
            print(f"可用的区域代码: {self.area_data['行政区划代码'].unique()[:5]}")  # 显示前5个代码作为示例
            
            result = self.area_data[self.area_data['行政区划代码'] == area_code]
            if not result.empty:
                return result.iloc[0]['省市县']
            return f"未找到对应地区(代码:{area_code})"
        except Exception as e:
            print(f"查询户籍所在地失败: {e}")
            return "查询失败"

    def clean_id_card(self, id_card):
        """清理身份证号码数据
        - 去除空格
        - 去除换行符
        - 去除特殊符号（如-、_、.等）
        - 去除前后的引号
        """
        if not isinstance(id_card, str):
            return str(id_card)
        
        # 去除空格、换行符和常见的分隔符
        id_card = id_card.strip()  # 去除前后的空格和换行符
        id_card = id_card.replace(' ', '')  # 去除空格
        id_card = id_card.replace('\n', '')  # 去除换行符
        id_card = id_card.replace('\r', '')  # 去除回车符
        id_card = id_card.replace('-', '')   # 去除横线
        id_card = id_card.replace('_', '')   # 去除下划线
        id_card = id_card.replace('.', '')   # 去除点
        id_card = id_card.replace('，', '')  # 去除中文逗号
        id_card = id_card.replace(',', '')   # 去除英文逗号
        
        # 去除可能存的引号
        id_card = id_card.replace('"', '')
        id_card = id_card.replace("'", '')
        id_card = id_card.replace('"', '')
        id_card = id_card.replace('"', '')
        
        return id_card

    def mask_id_card(self, id_card):
        """将身份证号码中间部分变成星号
        保留前6位和后4位，中间8位变成星号
        例如：330382199901010014 -> 330382********0014
        """
        id_card = self.clean_id_card(id_card)
        if len(id_card) != 18:
            return id_card
        return id_card[:6] + '*' * 8 + id_card[-4:]

class PhoneProcessor:
    def __init__(self):
        try:
            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 构建完整的文件路径
            file_path = os.path.join(current_dir, '手机归属地.csv')
            
            # 尝试不同的编码方式读取文件
            encodings = ['gbk', 'gb2312', 'utf-8']
            for encoding in encodings:
                try:
                    self.phone_data = pd.read_csv(file_path, encoding=encoding)
                    print(f"成功使用 {encoding} 编码读取手机归属地数据")
                    # 确保号段列的类型是整数
                    self.phone_data['号段'] = self.phone_data['号段'].astype(int)
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    print(f"使用 {encoding} 编码读取文件时发生错误: {e}")
            
            if not hasattr(self, 'phone_data'):
                raise Exception("无法使用任何编码方式读取文件")
                
        except Exception as e:
            print(f"加载手机归属地数据失败: {e}")
            raise
    
    def get_phone_info(self, phone):
        """获取手机号码信息"""
        try:
            if len(phone) < 7:
                return None
                
            prefix = int(phone[:7])  # 确保是整数类型
            # 打印调试信息
            print(f"查找号段: {prefix}")
            print(f"可用的号段: {self.phone_data['号段'].unique()[:5]}")  # 显示前5个号段作为示例
            
            result = self.phone_data[self.phone_data['号段'] == prefix]
            
            if not result.empty:
                return {
                    'location': result.iloc[0]['归属地'],
                    'operator': result.iloc[0]['运营商']
                }
            return None
        except Exception as e:
            print(f"查询手机号码信息失败: {e}")
            return None

    def clean_phone_number(self, phone):
        """清理手机号码数据
        - 去除空格
        - 去除换行符
        - 去除特殊符号（如-、_、.等）
        - 去除前后的引号
        """
        if not isinstance(phone, str):
            return str(phone)
        
        # 去除空格、换行符和常见的分隔符
        phone = phone.strip()  # 去除前后的空格和换行符
        phone = phone.replace(' ', '')  # 去除空格
        phone = phone.replace('\n', '')  # 去除换行符
        phone = phone.replace('\r', '')  # 去除回车符
        phone = phone.replace('-', '')   # 去除横线
        phone = phone.replace('_', '')   # 去除下划线
        phone = phone.replace('.', '')   # 去除点
        phone = phone.replace('，', '')  # 去除中文逗号
        phone = phone.replace(',', '')   # 去除英文逗号
        
        # 去除可能存在的引号
        phone = phone.replace('"', '')
        phone = phone.replace("'", '')
        phone = phone.replace('"', '')
        phone = phone.replace('"', '')
        
        return phone

    def mask_phone_number(self, phone):
        """将手机号码中间部分变成星号
        保留前3位和后4位，中间4位变成星号
        例如：13912345678 -> 139****5678
        """
        phone = self.clean_phone_number(phone)
        if len(phone) != 11:
            return phone
        return phone[:3] + '*' * 4 + phone[-4:]

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.id_processor = IDCardProcessor()
        self.phone_processor = PhoneProcessor()
        self.df = None

    def initUI(self):
        self.setWindowTitle('身份证号码与手号码处理工具')
        self.setGeometry(100, 100, 1000, 700)  # 调整窗口大小
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                font-size: 14px;
                color: #333333;
                padding: 5px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QComboBox {
                padding: 5px;
                border: 1px solid #BBBBBB;
                border-radius: 4px;
                background-color: white;
                min-height: 25px;
            }
            QCheckBox {
                font-size: 14px;
                padding: 5px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

        # 创建主窗口部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel('身份证号码与手机号码处理工具', self)
        title_label.setStyleSheet("""
            font-size: 24px;
            color: #1565C0;
            font-weight: bold;
            padding: 20px;
            qproperty-alignment: AlignCenter;
        """)
        main_layout.addWidget(title_label)

        # 工具选择部分
        tool_group = QGroupBox("工具选择")
        tool_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
                border: 2px solid #BBBBBB;
                border-radius: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        tool_layout = QHBoxLayout()
        self.tool_combo = QComboBox(self)
        self.tool_combo.addItems(["请选择工具类型", "身份证号码工具", "手机号码工具"])
        self.tool_combo.currentTextChanged.connect(self.on_tool_changed)
        tool_layout.addWidget(QLabel("选择工具类型："))
        tool_layout.addWidget(self.tool_combo)
        tool_group.setLayout(tool_layout)
        main_layout.addWidget(tool_group)

        # 文件选择部分
        file_group = QGroupBox("文件选择")
        file_group.setStyleSheet(tool_group.styleSheet())
        file_layout = QHBoxLayout()
        self.file_btn = QPushButton('选择文件', self)
        self.file_label = QLabel('未选择文件', self)
        file_layout.addWidget(self.file_btn)
        file_layout.addWidget(self.file_label)
        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)

        # 列选择部分
        column_group = QGroupBox("数据列选择")
        column_group.setStyleSheet(tool_group.styleSheet())
        column_layout = QVBoxLayout()
        
        # 添加数据列选择
        id_card_layout = QHBoxLayout()
        self.column_label = QLabel("数据列：")
        id_card_layout.addWidget(self.column_label)
        self.column_combo = QComboBox(self)
        id_card_layout.addWidget(self.column_combo)
        column_layout.addLayout(id_card_layout)
        
        # 添加日期列选择（初始隐藏）
        self.date_widget = QWidget()  # 创建一个容器widget
        self.date_layout = QHBoxLayout(self.date_widget)  # 将布局添加到widget
        self.date_layout.addWidget(QLabel("用于计算年龄的日期列（可选）："))
        self.date_column_combo = QComboBox(self)
        self.date_column_combo.addItem("使用当前日期")
        self.date_layout.addWidget(self.date_column_combo)
        column_layout.addWidget(self.date_widget)  # 将widget添加到主布局
        
        column_group.setLayout(column_layout)
        main_layout.addWidget(column_group)

        # 功能选择部分
        self.function_group = QGroupBox("功能选择")
        self.function_group.setStyleSheet(tool_group.styleSheet())
        self.function_layout = QVBoxLayout()
        self.function_group.setLayout(self.function_layout)
        main_layout.addWidget(self.function_group)

        # 按钮部分
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        
        self.process_btn = QPushButton('处理', self)
        self.export_btn = QPushButton('导出结果', self)
        
        button_layout.addStretch()
        button_layout.addWidget(self.process_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)

        # 版权信息
        copyright_label = QLabel('版权所有：台州市公安局 解晟', self)
        copyright_label.setStyleSheet("""
            color: #666666;
            font-size: 12px;
            padding: 10px;
            qproperty-alignment: AlignCenter;
        """)
        main_layout.addWidget(copyright_label)

        # 连接信号
        self.file_btn.clicked.connect(self.select_file)
        self.process_btn.clicked.connect(self.process_data)
        self.export_btn.clicked.connect(self.export_results)

        # 初始化界面状态
        self.date_widget.setVisible(False)  # 隐藏日期选择widget
        self.function_group.setVisible(False)

    def on_tool_changed(self, text):
        """处理工具类型选择变化"""
        # 清空功能选择
        for i in reversed(range(self.function_layout.count())): 
            self.function_layout.itemAt(i).widget().setParent(None)
        
        # 根据选择的工具类型显示相应的功能
        if text == "身份证号码工具":
            self.column_label.setText("身份证号码列：")
            self.date_widget.setVisible(True)  # 显示日期选择widget
            functions = [
                '验证身份证号码',
                '计算年龄',
                '提取性别',
                '提取出生日期',
                '匹配户籍地',
                '身份证号码脱敏（中间变星号）'
            ]
            self.function_group.setVisible(True)
        elif text == "手机号码工具":
            self.column_label.setText("手机号码列：")
            self.date_widget.setVisible(False)  # 隐藏日期选择widget
            functions = [
                '手机号码归属地匹配（约需1-2分钟，请耐心等待）',
                '手机号码脱敏（中间变星号）'
            ]
            self.function_group.setVisible(True)
        else:
            self.function_group.setVisible(False)
            return

        # 创建新的复选框
        self.checkboxes = []
        for func in functions:
            checkbox = QCheckBox(func, self)
            self.checkboxes.append(checkbox)
            self.function_layout.addWidget(checkbox)

    def select_file(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "选择文件",
            "",
            "Excel Files (*.xlsx *.xls);;CSV Files (*.csv)"
        )
        
        if file_name:
            try:
                if file_name.endswith('.csv'):
                    self.df = pd.read_csv(file_name)
                else:
                    self.df = pd.read_excel(file_name)
                
                self.file_label.setText(file_name.split('/')[-1])
                
                # 更新两个下拉框的选项
                self.column_combo.clear()
                self.date_column_combo.clear()
                self.date_column_combo.addItem("使用当前日期")  # 默认选项
                
                # 添加所有列到两个下拉框
                self.column_combo.addItems(self.df.columns)
                self.date_column_combo.addItems(self.df.columns)
                
            except Exception as e:
                QMessageBox.critical(self, '错误', f'无法读取文件：{str(e)}')

    def process_data(self):
        if self.df is None or self.column_combo.currentText() == '':
            QMessageBox.warning(self, '警告', '请先选择文件的数据列')
            return

        column = self.column_combo.currentText()
        data = self.df[column].astype(str)
        # 保存原始数据的副本，用于后续处理
        original_data = data.copy()

        # 获取当前选择的工具类型
        current_tool = self.tool_combo.currentText()
        
        # 处理数据
        results = {}
        
        if current_tool == "身份证号码工具":
            # 获取选择的日期列
            date_column = self.date_column_combo.currentText()
            if date_column and date_column != "使用当前日期":
                calc_dates = self.df[date_column]
            else:
                calc_dates = None

            # 处理身份证号码相关功能
            for checkbox in self.checkboxes:
                if not checkbox.isChecked():
                    continue
                    
                if checkbox.text() == '验证身份证号码':
                    results['身份证号码是否合法'] = original_data.apply(
                        lambda x: self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)))
                elif checkbox.text() == '计算年龄':
                    if calc_dates is not None:
                        # 使用指定日期计算年龄
                        results['年龄'] = pd.DataFrame({
                            'id_card': original_data,
                            'calc_date': calc_dates
                        }).apply(
                            lambda x: self.id_processor.get_age(
                                self.id_processor.clean_id_card(x['id_card']), 
                                x['calc_date']
                            ) if self.id_processor.validate_id_card(self.id_processor.clean_id_card(x['id_card'])) != "位数不对" else None,
                            axis=1
                        )
                    else:
                        # 使用当前日期计算年龄
                        results['年龄'] = original_data.apply(
                            lambda x: self.id_processor.get_age(self.id_processor.clean_id_card(x)) 
                            if self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)) != "位数不对" else None)
                elif checkbox.text() == '提取性别':
                    results['性别'] = original_data.apply(
                        lambda x: self.id_processor.get_gender(self.id_processor.clean_id_card(x)) 
                        if self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)) != "位数不对" else None)
                elif checkbox.text() == '提取出生日期':
                    results['出生日期'] = original_data.apply(
                        lambda x: self.id_processor.get_birth_date(self.id_processor.clean_id_card(x)) 
                        if self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)) != "位数不对" else None)
                elif checkbox.text() == '匹配户籍地':
                    results['户籍所在地'] = original_data.apply(
                        lambda x: self.id_processor.get_area(self.id_processor.clean_id_card(x)) 
                        if self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)) != "位数不对" else None)
                elif checkbox.text() == '身份证号码脱敏（中间变星号）':
                    # 直接修改原始列
                    self.df[column] = original_data.apply(lambda x: self.id_processor.mask_id_card(x))
        
        elif current_tool == "手机号码工具":
            # 处理手机号码相关功能
            for checkbox in self.checkboxes:
                if checkbox.isChecked():
                    if checkbox.text().startswith('手机号码归属地匹配'):
                        clean_phone_data = original_data.apply(self.phone_processor.clean_phone_number)
                        phone_info = clean_phone_data.apply(self.phone_processor.get_phone_info)
                        results['手机号码归属地'] = phone_info.apply(lambda x: x['location'] if x else None)
                        results['运营商'] = phone_info.apply(lambda x: x['operator'] if x else None)
                    elif checkbox.text() == '手机号码脱敏（中间变星号）':
                        # 直接修改原始列
                        self.df[column] = original_data.apply(lambda x: self.phone_processor.mask_phone_number(x))

        # 将其他结果添加到DataFrame中
        for key, value in results.items():
            self.df[key] = value

        QMessageBox.information(self, '成功', '数据处理完成！')

    def export_results(self):
        if self.df is None:
            QMessageBox.warning(self, '警告', '没有可导出的数据！')
            return

        file_name, _ = QFileDialog.getSaveFileName(
            self,
            "保存文件",
            "",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )
        
        if file_name:
            try:
                if file_name.endswith('.csv'):
                    self.df.to_csv(file_name, index=False, encoding='utf-8-sig')
                else:
                    self.df.to_excel(file_name, index=False)
                QMessageBox.information(self, '成功', '文件导出成功！')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导出文件失败：{str(e)}')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_()) 