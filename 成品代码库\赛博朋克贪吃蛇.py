import tkinter as tk
import random
import time

class SnakeGame:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("赛博朋克贪吃蛇")
        self.root.resizable(False, False)
        
        # 游戏参数
        self.width = 600
        self.height = 400
        self.grid_size = 20
        self.speed = 150  # 初始速度（毫秒）
        self.min_speed = 50  # 最快速度
        
        # 创建画布
        self.canvas = tk.Canvas(
            self.root,
            width=self.width,
            height=self.height,
            bg='black',
            highlightthickness=0
        )
        self.canvas.pack(padx=10, pady=10)
        
        # 初始化游戏状态
        self.snake = [(100, 100), (80, 100), (60, 100)]
        self.direction = 'Right'
        self.next_direction = 'Right'
        self.food = None
        self.score = 0
        self.game_started = False
        self.paused = False
        
        # 创建分数显示
        self.score_var = tk.StringVar(value="分数: 0")
        self.score_label = tk.Label(
            self.root,
            textvariable=self.score_var,
            fg='#00ff00',
            bg='black',
            font=('Courier', 16)
        )
        self.score_label.pack()
        
        # 绑定按键事件
        self.root.bind('<Left>', lambda e: self.change_direction('Left'))
        self.root.bind('<Right>', lambda e: self.change_direction('Right'))
        self.root.bind('<Up>', lambda e: self.change_direction('Up'))
        self.root.bind('<Down>', lambda e: self.change_direction('Down'))
        self.root.bind('<space>', self.toggle_pause)
        
        # 显示开始提示
        self.show_welcome_message()
        
    def show_welcome_message(self):
        self.canvas.create_text(
            self.width/2,
            self.height/2,
            text="按任意方向键开始游戏\n空格键暂停",
            fill='#00ff00',
            font=('Courier', 20),
            justify='center',
            tags='welcome'
        )
    
    def change_direction(self, new_direction):
        if not self.game_started:
            self.game_started = True
            self.canvas.delete('welcome')
            self.create_food()
            self.update()
            return
            
        opposites = {'Left': 'Right', 'Right': 'Left', 'Up': 'Down', 'Down': 'Up'}
        if new_direction != opposites.get(self.direction):
            self.next_direction = new_direction
    
    def toggle_pause(self, event=None):
        if self.game_started:
            self.paused = not self.paused
            if not self.paused:
                self.update()
    
    def create_food(self):
        while True:
            x = random.randint(1, (self.width-self.grid_size)//self.grid_size) * self.grid_size
            y = random.randint(1, (self.height-self.grid_size)//self.grid_size) * self.grid_size
            if (x, y) not in self.snake:
                self.food = (x, y)
                # 创建霓虹食物
                self.canvas.create_oval(
                    x, y,
                    x + self.grid_size, y + self.grid_size,
                    fill='#ff1493',  # 霓虹粉
                    outline='white',
                    width=2,
                    tags='food'
                )
                break
    
    def draw_snake(self):
        self.canvas.delete('snake')
        for i, (x, y) in enumerate(self.snake):
            # 创建霓虹蛇身
            color = '#00ffff' if i == 0 else '#0080ff'  # 头部使用不同颜色
            self.canvas.create_rectangle(
                x, y,
                x + self.grid_size, y + self.grid_size,
                fill=color,
                outline='white',
                width=1,
                tags='snake'
            )
    
    def move_snake(self):
        self.direction = self.next_direction
        head = self.snake[0]
        if self.direction == 'Left':
            new_head = (head[0] - self.grid_size, head[1])
        elif self.direction == 'Right':
            new_head = (head[0] + self.grid_size, head[1])
        elif self.direction == 'Up':
            new_head = (head[0], head[1] - self.grid_size)
        else:  # Down
            new_head = (head[0], head[1] + self.grid_size)
        
        # 检查碰撞
        if (new_head[0] < 0 or new_head[0] >= self.width or
            new_head[1] < 0 or new_head[1] >= self.height or
            new_head in self.snake):
            self.game_over()
            return False
        
        self.snake.insert(0, new_head)
        
        # 检查是否吃到食物
        if new_head == self.food:
            self.canvas.delete('food')
            self.score += 10
            self.score_var.set(f"分数: {self.score}")
            self.create_food()
            # 加快游戏速度
            self.speed = max(self.min_speed, self.speed - 5)
        else:
            self.snake.pop()
        
        return True
    
    def game_over(self):
        self.canvas.create_text(
            self.width/2,
            self.height/2,
            text=f"游戏结束!\n最终得分: {self.score}\n按R键重新开始",
            fill='#ff0000',
            font=('Courier', 20),
            justify='center',
            tags='gameover'
        )
        self.root.bind('r', self.reset_game)
        self.game_started = False
    
    def reset_game(self, event=None):
        self.snake = [(100, 100), (80, 100), (60, 100)]
        self.direction = 'Right'
        self.next_direction = 'Right'
        self.score = 0
        self.speed = 150
        self.score_var.set("分数: 0")
        self.canvas.delete('all')
        self.game_started = False
        self.paused = False
        self.show_welcome_message()
    
    def update(self):
        if self.game_started and not self.paused:
            if self.move_snake():
                self.draw_snake()
                self.root.after(self.speed, self.update)
    
    def run(self):
        self.root.mainloop()

if __name__ == '__main__':
    game = SnakeGame()
    game.run()