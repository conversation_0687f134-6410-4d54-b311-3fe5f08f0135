import requests
import json
from datetime import datetime, timezone, timedelta
import time
import argparse
from bs4 import BeautifulSoup
import feedparser

class ChinaHotNewsCollector:
    def __init__(self):
        # 中国主要新闻源RSS地址
        self.rss_sources = [
            "http://rss.sina.com.cn/news/china/focus15.xml",  # 新浪国内焦点
            "http://rss.sina.com.cn/news/china/hotnews.xml",   # 新浪国内热门
            "http://rss.sina.com.cn/news/china/politics.xml", # 新浪时政
            "http://news.163.com/special/00011K6L/rss_newstop.xml", # 网易头条
            "http://news.163.com/special/00011K6L/rss_guonei.xml",  # 网易国内
        ]
        
    def get_rss_news(self, limit=50):
        """
        从RSS源获取新闻
        """
        print("正在从RSS源获取中国国内新闻...")
        news_list = []
        
        for rss_url in self.rss_sources:
            try:
                print(f"正在获取 {rss_url}...")
                feed = feedparser.parse(rss_url)
                
                for entry in feed.entries[:limit//len(self.rss_sources)]:
                    # 解析发布时间
                    publish_time = None
                    if hasattr(entry, 'published_parsed'):
                        publish_time = datetime(*entry.published_parsed[:6], tzinfo=timezone.utc)
                    elif hasattr(entry, 'updated_parsed'):
                        publish_time = datetime(*entry.updated_parsed[:6], tzinfo=timezone.utc)
                    else:
                        publish_time = datetime.now(timezone.utc)
                    
                    news_item = {
                        'title': entry.title,
                        'url': entry.link,
                        'time': publish_time,
                        'source': self._get_source_name(rss_url),
                        'type': 'news',
                        'hot_score': 0  # RSS新闻没有热度分数
                    }
                    news_list.append(news_item)
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"获取RSS源 {rss_url} 失败: {e}")
                continue
        
        return news_list
    
    def get_weibo_hot_search(self, limit=20):
        """
        获取微博热搜
        """
        print("正在获取微博热搜...")
        hot_search_list = []
        
        try:
            # 微博热搜API
            url = "https://weibo.com/ajax/side/hotSearch"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://weibo.com/'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            data = response.json()
            
            if 'data' in data and 'realtime' in data['data']:
                for i, item in enumerate(data['data']['realtime'][:limit]):
                    hot_score = item.get('hot', 0)
                    word = item.get('word', '')
                    
                    hot_search_list.append({
                        'title': word,
                        'url': f"https://s.weibo.com/weibo?q={word}",
                        'time': datetime.now(timezone.utc),
                        'source': '微博热搜',
                        'type': 'hot_search',
                        'hot_score': hot_score
                    })
            
        except Exception as e:
            print(f"获取微博热搜失败: {e}")
            # 如果API失败，使用备用方案
            hot_search_list = self._get_weibo_hot_search_backup(limit)
            
        return hot_search_list
    
    def _get_weibo_hot_search_backup(self, limit=20):
        """
        微博热搜备用获取方案
        """
        print("使用微博热搜备用方案...")
        hot_search_list = []
        
        try:
            # 尝试从第三方API获取
            url = "https://tenapi.cn/resou/"  # 示例API，实际可能需要其他API
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            data = response.json()
            
            if 'data' in data:
                for i, item in enumerate(data['data'][:limit]):
                    hot_score = int(item.get('hot', 0))
                    word = item.get('name', item.get('title', ''))
                    
                    hot_search_list.append({
                        'title': word,
                        'url': f"https://s.weibo.com/weibo?q={word}",
                        'time': datetime.now(timezone.utc),
                        'source': '微博热搜',
                        'type': 'hot_search',
                        'hot_score': hot_score
                    })
                    
        except Exception as e:
            print(f"微博热搜备用方案也失败了: {e}")
            
        return hot_search_list
    
    def get_baidu_hot_search(self, limit=20):
        """
        获取百度热搜
        """
        print("正在获取百度热搜...")
        hot_search_list = []
        
        try:
            # 百度热搜API
            url = "https://top.baidu.com/board?tab=realtime"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试解析百度热搜页面
            items = soup.find_all('div', class_='category-wrap_iQLoo')[:limit]
            
            for i, item in enumerate(items):
                title_elem = item.find('div', class_='c-single-text-ellipsis')
                if title_elem:
                    title = title_elem.get_text().strip()
                    hot_search_list.append({
                        'title': title,
                        'url': f"https://www.baidu.com/s?wd={title}",
                        'time': datetime.now(timezone.utc),
                        'source': '百度热搜',
                        'type': 'hot_search',
                        'hot_score': (limit - i) * 10000  # 根据排名给热度分数
                    })
                    
        except Exception as e:
            print(f"获取百度热搜失败: {e}")
            # 如果解析失败，使用备用方案
            hot_search_list = self._get_baidu_hot_search_backup(limit)
            
        return hot_search_list
    
    def _get_baidu_hot_search_backup(self, limit=20):
        """
        百度热搜备用获取方案
        """
        print("使用百度热搜备用方案...")
        hot_search_list = []
        
        try:
            # 尝试从第三方API获取
            url = "https://api.vvhan.com/api/hotlist?type=baidu"  # 示例API
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            data = response.json()
            
            if 'data' in data:
                for i, item in enumerate(data['data'][:limit]):
                    title = item.get('title', '')
                    hot_score = int(item.get('hot', 0))
                    
                    hot_search_list.append({
                        'title': title,
                        'url': f"https://www.baidu.com/s?wd={title}",
                        'time': datetime.now(timezone.utc),
                        'source': '百度热搜',
                        'type': 'hot_search',
                        'hot_score': hot_score
                    })
                    
        except Exception as e:
            print(f"百度热搜备用方案也失败了: {e}")
            
        return hot_search_list
    
    def get_douyin_hot_search(self, limit=20):
        """
        获取抖音热搜
        """
        print("正在获取抖音热搜...")
        hot_search_list = []
        
        try:
            # 抖音热搜API（需要处理反爬虫）
            url = "https://www.iesdouyin.com/web/api/v2/hotsearch/billboard/word/"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.douyin.com/'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            data = response.json()
            
            if 'word_list' in data:
                for i, item in enumerate(data['word_list'][:limit]):
                    word = item.get('word', '')
                    hot_score = item.get('hot_value', 0)
                    
                    hot_search_list.append({
                        'title': word,
                        'url': f"https://www.douyin.com/search/{word}",
                        'time': datetime.now(timezone.utc),
                        'source': '抖音热搜',
                        'type': 'hot_search',
                        'hot_score': hot_score
                    })
                    
        except Exception as e:
            print(f"获取抖音热搜失败: {e}")
            # 如果API失败，使用备用方案
            hot_search_list = self._get_douyin_hot_search_backup(limit)
            
        return hot_search_list
    
    def _get_douyin_hot_search_backup(self, limit=20):
        """
        抖音热搜备用获取方案
        """
        print("使用抖音热搜备用方案...")
        hot_search_list = []
        
        try:
            # 尝试从第三方API获取
            url = "https://api.vvhan.com/api/hotlist?type=douyin"  # 示例API
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            data = response.json()
            
            if 'data' in data:
                for i, item in enumerate(data['data'][:limit]):
                    title = item.get('title', '')
                    hot_score = int(item.get('hot', 0))
                    
                    hot_search_list.append({
                        'title': title,
                        'url': f"https://www.douyin.com/search/{title}",
                        'time': datetime.now(timezone.utc),
                        'source': '抖音热搜',
                        'type': 'hot_search',
                        'hot_score': hot_score
                    })
                    
        except Exception as e:
            print(f"抖音热搜备用方案也失败了: {e}")
            
        return hot_search_list
    
    def _get_source_name(self, rss_url):
        """
        根据RSS URL获取新闻源名称
        """
        if 'sina.com.cn' in rss_url:
            return '新浪新闻'
        elif '163.com' in rss_url:
            return '网易新闻'
        else:
            return '未知来源'
    
    def filter_news_by_date(self, news_list, target_date=None):
        """
        根据日期过滤新闻
        """
        if target_date is None:
            # 如果没有指定日期，使用今天
            target_date = datetime.now(timezone.utc).date()
        
        print(f"过滤日期: {target_date}")
        
        filtered_news = []
        
        for news in news_list:
            # 检查新闻发布时间是否为目标日期
            if news['time'].date() == target_date:
                filtered_news.append(news)
        
        return filtered_news
    
    def deduplicate_news(self, news_list):
        """
        去除重复新闻
        """
        seen_titles = set()
        unique_news = []
        
        for news in news_list:
            title = news['title']
            if title not in seen_titles:
                seen_titles.add(title)
                unique_news.append(news)
        
        return unique_news
    
    def format_news_list(self, news_list, max_count=20):
        """
        格式化新闻列表输出，按热度排序
        """
        if not news_list:
            return "未找到符合条件的内容。"
        
        # 按热度分数排序（热搜）或按时间排序（新闻）
        # 对于没有热度分数的新闻，我们给一个默认值
        for news in news_list:
            if news['hot_score'] == 0:
                # 根据新闻源给一个默认热度分数
                if '新浪' in news['source'] or '网易' in news['source']:
                    news['hot_score'] = 50000
                else:
                    news['hot_score'] = 10000
        
        # 按热度分数排序
        news_list.sort(key=lambda x: x['hot_score'], reverse=True)
        
        # 只取前max_count条
        news_list = news_list[:max_count]
        
        result = f"# {datetime.now(timezone.utc).date()} 中国热门内容 Top {len(news_list)}\n\n"
        result += "## 包含新闻、微博热搜、百度热搜、抖音热搜等内容\n\n"
        
        for i, news in enumerate(news_list, 1):
            # 格式化时间
            time_str = news['time'].strftime("%Y-%m-%d %H:%M:%S")
            
            # 热度显示
            hot_score_str = f" (热度: {news['hot_score']:,})" if news['hot_score'] > 0 else ""
            
            result += f"{i}. [{news['title']}]({news['url']})\n"
            result += f"   发布时间: {time_str} UTC | 来源: {news['source']}{hot_score_str}\n\n"
        
        return result
    
    def save_to_file(self, content, filename):
        """
        保存内容到文件
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"结果已保存到 {filename}")
        except Exception as e:
            print(f"保存文件失败: {e}")
    
    def collect_news(self, date=None, max_count=20, save_file=True):
        """
        收集新闻主函数
        """
        all_news = []
        
        # 获取RSS新闻
        rss_news = self.get_rss_news(limit=100)
        print(f"从RSS源获取到{len(rss_news)}条新闻")
        all_news.extend(rss_news)
        
        # 获取微博热搜
        weibo_hot = self.get_weibo_hot_search(limit=20)
        print(f"获取到{len(weibo_hot)}条微博热搜")
        all_news.extend(weibo_hot)
        
        # 获取百度热搜
        baidu_hot = self.get_baidu_hot_search(limit=20)
        print(f"获取到{len(baidu_hot)}条百度热搜")
        all_news.extend(baidu_hot)
        
        # 获取抖音热搜
        douyin_hot = self.get_douyin_hot_search(limit=20)
        print(f"获取到{len(douyin_hot)}条抖音热搜")
        all_news.extend(douyin_hot)
        
        if not all_news:
            print("未能获取到任何内容")
            return None
        
        print(f"总共获取到{len(all_news)}条内容")
        
        # 去重
        all_news = self.deduplicate_news(all_news)
        print(f"去重后剩余{len(all_news)}条内容")
        
        # 解析日期参数
        target_date = None
        if date:
            try:
                target_date = datetime.strptime(date, "%Y-%m-%d").date()
            except ValueError:
                print("日期格式错误，使用今天日期")
                target_date = datetime.now(timezone.utc).date()
        
        # 过滤指定日期的内容
        print("正在过滤内容...")
        if target_date:
            filtered_news = self.filter_news_by_date(all_news, target_date)
            if not filtered_news:
                print(f"指定日期 {target_date} 没有找到内容")
                # 如果没有指定日期的内容，显示最新的前max_count条
                filtered_news = all_news[:max_count]
                print("显示最新的内容:")
            else:
                print(f"找到{len(filtered_news)}条{target_date}的内容")
        else:
            filtered_news = all_news
            print(f"显示所有{len(filtered_news)}条内容")
        
        # 格式化输出
        result = self.format_news_list(filtered_news, max_count)
        
        # 保存到文件
        if save_file:
            filename = f"china_hot_news_{datetime.now(timezone.utc).date()}.md"
            self.save_to_file(result, filename)
        
        return result

def main():
    parser = argparse.ArgumentParser(description='收集中国热门新闻和热搜内容')
    parser.add_argument('--date', '-d', help='指定日期 (格式: YYYY-MM-DD)，默认为今天')
    parser.add_argument('--count', '-c', type=int, default=20, help='内容数量，默认20条')
    parser.add_argument('--output', '-o', help='输出文件名')
    
    args = parser.parse_args()
    
    collector = ChinaHotNewsCollector()
    result = collector.collect_news(date=args.date, max_count=args.count)
    
    if result:
        print("\n" + "="*50)
        print(result)
        print("="*50)

if __name__ == "__main__":
    main()