WITH 身份证校验 AS (
    SELECT 
        待匹配.*,
        CASE 
            WHEN LENGTH(身份证号码) != 18 THEN '校验错误'
            WHEN SUBSTRING(身份证号码, 18, 1) != (
                CASE (
                    (
                        CAST(SUBSTRING(身份证号码, 1, 1) AS UNSIGNED) * 7 +
                        CAST(SUBSTRING(身份证号码, 2, 1) AS UNSIGNED) * 9 +
                        CAST(SUBSTRING(身份证号码, 3, 1) AS UNSIGNED) * 10 +
                        CAST(SUBSTRING(身份证号码, 4, 1) AS UNSIGNED) * 5 +
                        CAST(SUBSTRING(身份证号码, 5, 1) AS UNSIGNED) * 8 +
                        CAST(SUBSTRING(身份证号码, 6, 1) AS UNSIGNED) * 4 +
                        CAST(SUBSTRING(身份证号码, 7, 1) AS UNSIGNED) * 2 +
                        CAST(SUBSTRING(身份证号码, 8, 1) AS UNSIGNED) * 1 +
                        CAST(SUBSTRING(身份证号码, 9, 1) AS UNSIGNED) * 6 +
                        CAST(SUBSTRING(身份证号码, 10, 1) AS UNSIGNED) * 3 +
                        CAST(SUBSTRING(身份证号码, 11, 1) AS UNSIGNED) * 7 +
                        CAST(SUBSTRING(身份证号码, 12, 1) AS UNSIGNED) * 9 +
                        CAST(SUBSTRING(身份证号码, 13, 1) AS UNSIGNED) * 10 +
                        CAST(SUBSTRING(身份证号码, 14, 1) AS UNSIGNED) * 5 +
                        CAST(SUBSTRING(身份证号码, 15, 1) AS UNSIGNED) * 8 +
                        CAST(SUBSTRING(身份证号码, 16, 1) AS UNSIGNED) * 4 +
                        CAST(SUBSTRING(身份证号码, 17, 1) AS UNSIGNED) * 2
                    ) % 11
                )
                WHEN 0 THEN '1'
                WHEN 1 THEN '0'
                WHEN 2 THEN 'X'
                WHEN 3 THEN '9'
                WHEN 4 THEN '8'
                WHEN 5 THEN '7'
                WHEN 6 THEN '6'
                WHEN 7 THEN '5'
                WHEN 8 THEN '4'
                WHEN 9 THEN '3'
                WHEN 10 THEN '2'
                END
            ) THEN '校验错误'
            ELSE '校验正确'
        END AS 校验结果,
        FLOOR(DATEDIFF(
            DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY),
            STR_TO_DATE(CONCAT(
                SUBSTRING(身份证号码, 7, 4), '-',
                SUBSTRING(身份证号码, 11, 2), '-',
                SUBSTRING(身份证号码, 13, 2)
            ), '%Y-%m-%d')
        ) / 365) AS 年龄
    FROM 待匹配
)
SELECT 
    a.*,
    a.年龄,
    a.校验结果,
    b.省市县
FROM 身份证校验 a
LEFT JOIN 行政区划代码表 b ON LEFT(a.身份证号码, 6) = b.行政区划代码
ORDER BY a.身份证号码; 