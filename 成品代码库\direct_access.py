from playwright.sync_api import sync_playwright
import time

def main():
    with sync_playwright() as p:
        browser = p.chromium.launch(
            headless=False,
            args=['--disable-web-security']
        )
        
        try:
            # 创建新的上下文
            context = browser.new_context(
                viewport={'width': 1280, 'height': 800},
                ignore_https_errors=True
            )
            
            # 创建新页面
            page = context.new_page()
            
            # 设置认证cookies
            context.add_cookies([
                {
                    "name": "token",
                    "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyODEwMjEsInVzZXJfbmFtZSI6IjFUTHhUQmpnQnMiLCJpc3MiOiJDaGF0U2hhcmUiLCJleHAiOjE3MzU2NDc2ODR9.CMuskngUP2fmLHFInw7CJ6PI9ApWgRF0ox7tKWkMx10",
                    "domain": ".chatshare.biz",
                    "path": "/"
                },
                {
                    "name": "authorization",
                    "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyODEwMjEsInVzZXJfbmFtZSI6IjFUTHhUQmpnQnMiLCJpc3MiOiJDaGF0U2hhcmUiLCJleHAiOjE3MzU2NDc2ODR9.CMuskngUP2fmLHFInw7CJ6PI9ApWgRF0ox7tKWkMx10",
                    "domain": ".chatshare.biz",
                    "path": "/"
                },
                {
                    "name": "gfsessionid",
                    "value": "6b84f888-f080-4774-87c0-bc93ef03efc3",
                    "domain": ".chatshare.biz",
                    "path": "/"
                }
            ])
            
            # 直接访问目标页面
            print("正在访问目标页面...")
            page.goto("https://gpt-node5.chatshare.biz/", wait_until="domcontentloaded")
            
            # 等待页面加载
            time.sleep(5)
            
            # 检查是否成功访问
            current_url = page.url
            print("当前URL:", current_url)
            
            if "gpt-node5.chatshare.biz" in current_url and "/login" not in current_url:
                print("成功访问目标页面！")
            else:
                print("访问失败，可能需要更新认证信息")
                page.screenshot(path="direct_access_error.png")
                print("已保存错误截图到 direct_access_error.png")
            
            # 等待用户查看
            input("按回车键退出...")
            
        except Exception as e:
            print(f"发生错误: {str(e)}")
        finally:
            browser.close()

if __name__ == "__main__":
    main() 