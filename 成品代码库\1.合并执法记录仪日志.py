import pandas as pd
import warnings

# 忽略openpyxl的UserWarning
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

# 指定文件名列表
file_names = ["1.xlsx", "2.xlsx", "3.xlsx", "4.xlsx", "5.xlsx", "6.xlsx", "7.xlsx", "8.xlsx"]

# 读取所有指定的Excel文件并合并
df_list = [pd.read_excel(file) for file in file_names]
combined_df = pd.concat(df_list)

# 按“日志时间”列排序
combined_df = combined_df.sort_values(by="日志时间")

# 重置索引
combined_df.reset_index(drop=True, inplace=True)

# 输出为CSV文件
combined_df.to_csv("合并后的执法记录仪日志.csv", index=False, encoding='utf-8-sig')

print("合并并排序后的文件已保存为 合并后的执法记录仪日志.csv")
