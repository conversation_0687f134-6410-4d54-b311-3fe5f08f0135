import pandas as pd
import warnings

# 忽略openpyxl的UserWarning
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

# 指定文件名列表
file_names = ["1.xlsx", "2.xlsx", "3.xlsx", "4.xlsx", "5.xlsx", "6.xlsx", "7.xlsx", "8.xlsx"]

# 读取所有指定的Excel文件并合并
df_list = [pd.read_excel(file) for file in file_names]
combined_df = pd.concat(df_list)

# 按“日志时间”列排序
combined_df = combined_df.sort_values(by="日志时间")

# 重置索引
combined_df.reset_index(drop=True, inplace=True)

# 输出为CSV文件
combined_df.to_csv("合并后的执法记录仪日志.csv", index=False, encoding='utf-8-sig')

print("合并并排序后的文件已保存为 合并后的执法记录仪日志.csv")

# 读取CSV文件
file_path = '合并后的执法记录仪日志.csv'
df = pd.read_csv(file_path)

# 替换“操作部门”列中的值
replace_dict = {
    'cmpcs': '玉环楚门派出所',
    'gjpcs': '玉环干江派出所',
    'kmpcs': '玉环坎门派出所',
    'qgpcs': '玉环清港派出所',
    'smpcs': '玉环沙门派出所',
    'xcpcs': '玉环新城派出所',
    'ycpcs': '玉环玉城派出所',
    '陈家俊': '临海杜桥派出所',
    '峰江': '路桥峰江派出所',
    '洪家': '椒江洪家派出所',
    '前所': '椒江前所派出所',
    '桐屿': '路桥桐屿派出所',
    '叶子青': '三门沙柳派出所',
    '杨涛': '温岭石桥头派出所',
    '章安': '椒江章安派出所'
}

for key, value in replace_dict.items():
    df.loc[df['操作用户名'].str.contains(key, na=False), '操作部门'] = value

# 删除“操作用户名”列中含有“指”字的所有行
df = df[~df['操作用户名'].str.contains('指', na=False)]

# 过滤“操作部门”列中含有“派出所”的内容
df_filtered = df[df['操作部门'].str.contains('派出所', na=False)]

# 保存结果到新的Excel文件
output_file_path = '新的执法记录仪日志.xlsx'
df_filtered.to_excel(output_file_path, index=False)

print(f"处理完成，结果已保存到 {output_file_path}")

# 读取新的Excel文件
df = pd.read_excel(output_file_path)

# 将“日志时间”列转换为日期时间格式
df['日志时间'] = pd.to_datetime(df['日志时间'])

# 按“操作用户名”和“操作对象”排序，然后按“日志时间”排序
df = df.sort_values(by=['操作用户名', '操作对象', '日志时间'])

# 删除在1分钟内重复的记录
df['时间差'] = df.groupby(['操作用户名', '操作对象'])['日志时间'].diff().dt.total_seconds()
df_filtered = df[(df['时间差'].isna()) | (df['时间差'] > 60)]

# 删除辅助列“时间差”
df_filtered = df_filtered.drop(columns=['时间差'])

# 保存为新的Excel文件
output_file_path = '新的执法记录仪日志每分钟1条.xlsx'
df_filtered.to_excel(output_file_path, index=False)

print(f"处理完成，新文件已保存为 {output_file_path}")

# 读取新的Excel文件
file_path = '新的执法记录仪日志每分钟1条.xlsx'
df = pd.read_excel(file_path)

# 定义县市区和派出所的映射关系
mapping = {
    '椒江': ['海门', '府前', '白云', '葭沚', '洪家', '下陈', '前所', '章安', '车站', '港区', '大陈'],
    '黄岩': ['院桥', '城南', '城西', '澄江', '新前', '城东', '宁溪', '北洋', '城北', '江口', '头陀'],
    '路桥': ['路桥', '路南', '路北', '螺洋', '桐屿', '峰江', '新桥', '横街', '蓬街', '金清'],
    '临海': ['古城', '大田', '括苍', '尤溪', '大洋', '涌泉', '白水洋', '汛桥', '邵家渡', '桃渚', '河头', '东塍', '小芝', '永丰', '杜桥', '江南', '沿江', '上盘'],
    '温岭': ['太平', '城东', '城西', '城北', '横峰', '泽国', '大溪', '松门', '箬横', '新河', '石塘', '滨海', '温峤', '城南', '石桥头', '坞根', '车站', '经济开发区'],
    '玉环': ['玉城', '坎门', '楚门', '大麦屿', '清港', '沙门', '新城', '干江', '龙溪'],
    '天台': ['城东', '城西', '城南', '平桥', '白鹤', '坦头', '三合', '街头', '石梁', '国清'],
    '仙居': ['福应', '安洲', '横溪', '白塔', '下各', '朱溪', '官路', '南峰', '田市'],
    '三门': ['沙柳', '健跳', '海游', '亭旁', '珠岙', '花桥', '浦坝港', '海润'],
    '台州湾新区': ['月湖', '三甲', '海虹']
}

# 定义特殊处理规则
special_cases = {
    '牧屿': '温岭牧屿警务区',
    '葭芷': '椒江葭沚派出所'
}

# 规范化派出所名称
def normalize_name(name):
    for county, stations in mapping.items():
        for station in stations:
            if station in name:
                return f"{county}{station}派出所"
    for special, replacement in special_cases.items():
        if special in name:
            return replacement
    return name

# 应用规范化函数
df['操作部门'] = df['操作部门'].apply(normalize_name)

# 保存结果到新的Excel文件
output_file_path = '派出所名称治理后的执法记录仪日志.xlsx'
df.to_excel(output_file_path, index=False)

print(f"规范化后的文件已保存为 {output_file_path}")

# 读取Excel文件
df1 = pd.read_excel('派出所名称治理后的执法记录仪日志.xlsx')
df2 = pd.read_excel('签收时间与执法记录仪日志时间一致且管辖单位为派出所且非自接警或其他警情.xlsx')
df_jq = pd.read_excel('签收时间与执法记录仪日志时间一致且管辖单位为派出所且非自接警或其他警情.xlsx')

# 将“接警单编号”列设置为文本格式
df2['接警单编号'] = df2['接警单编号'].astype(str)

# 左连接两个表
merged_df = pd.merge(df2, df1, left_on='管辖单位', right_on='操作部门', how='left')

# 将“签收时间”和“日志时间”列转换为日期时间格式
merged_df['签收时间'] = pd.to_datetime(merged_df['签收时间'])
merged_df['日志时间'] = pd.to_datetime(merged_df['日志时间'])

# 计算“日志时间”减去“签收时间”的差值
merged_df['时间差'] = merged_df['日志时间'] - merged_df['签收时间']

# 筛选时间差在0分0秒到15分0秒之间的记录
filtered_df = merged_df[(merged_df['时间差'] >= pd.Timedelta(minutes=0)) & (merged_df['时间差'] <= pd.Timedelta(minutes=15))].copy()

# 使用.loc明确指定行和列，保留“接警单编号”列为文本格式
filtered_df.loc[:, '接警单编号'] = filtered_df['接警单编号'].astype(str)

# 保存筛选结果到新的Excel文件
filtered_df.to_excel('已在15分钟内盯警.xlsx', index=False)

# 读取已在15分钟内盯警
df_filtered = pd.read_excel('已在15分钟内盯警.xlsx')

# 将“接警单编号”列设置为文本格式
df_filtered['接警单编号'] = df_filtered['接警单编号'].astype(str)
df_jq['接警单编号'] = df_jq['接警单编号'].astype(str)

# 找出“警情.xlsx”中“接警单编号”列中不在“已在15分钟内盯警.xlsx”中的内容
result_df = df_jq[~df_jq['接警单编号'].isin(df_filtered['接警单编号'])]

# 保留“警情.xlsx”中的字段并生成新表格
result_df.to_excel('最终结果-未盯警的警情.xlsx', index=False)

print("任务完成，结果已保存到'最终结-未盯警的警情.xlsx'")