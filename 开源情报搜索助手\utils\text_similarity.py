#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文本相似度工具模块 - 负责计算文本相似度和热点信息整合
"""

import jieba
import logging
import re
from collections import Counter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('TextSimilarity')

# 停用词列表
STOP_WORDS = set([
    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', 
    '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', 
    '着', '没有', '看', '好', '自己', '这', '那', '啊', '吧', '还',
    '最', '但', '可以', '这个', '什么', '没', '为', '来', '它', '吗',
    '被', '所以', '能', '等', '已经', '与', '以', '太', '非常', '比较', 
    '这样', '那样', '只是', '一样', '如果', '因为', '所', '那些', '几',
    '给', '她', '他', '应该', '之', '些', '让', '呢', '得', '首页', '问',
    '热搜', '热榜', '#', '【', '】', '[', ']', '！', '？', '，', '。',
    '：', '；', '"', '"', ''', ''', '（', '）', '、', '《', '》', '・',
    '…', '「', '」', '『', '』', '〈', '〉', '〖', '〗', '〔', '〕', '—'
])

class TextSimilarity:
    """文本相似度计算类"""
    
    @staticmethod
    def preprocess_text(text):
        """
        文本预处理
        
        Args:
            text: 原始文本
            
        Returns:
            处理后的词语列表
        """
        # 移除URL
        text = re.sub(r'https?://\S+|www\.\S+', '', text)
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', text)
        
        # 移除多余的空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 使用jieba分词
        words = list(jieba.cut(text))
        
        # 过滤停用词和空字符
        words = [word for word in words if word not in STOP_WORDS and word.strip()]
        
        return words

    @staticmethod
    def jaccard_similarity(set1, set2):
        """
        计算Jaccard相似度
        
        Args:
            set1: 第一个集合
            set2: 第二个集合
            
        Returns:
            Jaccard相似度 (0-1)
        """
        if not set1 or not set2:
            return 0.0
            
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        if union == 0:
            return 0.0
            
        return intersection / union

    @staticmethod
    def cosine_similarity(vec1, vec2):
        """
        计算余弦相似度
        
        Args:
            vec1: 第一个向量（词频Counter）
            vec2: 第二个向量（词频Counter）
            
        Returns:
            余弦相似度 (0-1)
        """
        # 计算点积
        intersection = set(vec1.keys()) & set(vec2.keys())
        dot_product = sum([vec1[x] * vec2[x] for x in intersection])
        
        # 计算范数
        norm1 = sum([vec1[x] ** 2 for x in vec1.keys()]) ** 0.5
        norm2 = sum([vec2[x] ** 2 for x in vec2.keys()]) ** 0.5
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        return dot_product / (norm1 * norm2)

    @classmethod
    def calculate_similarity(cls, text1, text2, method='combined'):
        """
        计算两段文本的相似度
        
        Args:
            text1: 第一段文本
            text2: 第二段文本
            method: 相似度计算方法，可选值: 'jaccard', 'cosine', 'combined'
            
        Returns:
            相似度得分 (0-1)
        """
        # 文本预处理
        words1 = cls.preprocess_text(text1)
        words2 = cls.preprocess_text(text2)
        
        if not words1 or not words2:
            return 0.0
        
        if method == 'jaccard':
            # 使用Jaccard相似度
            set1 = set(words1)
            set2 = set(words2)
            return cls.jaccard_similarity(set1, set2)
            
        elif method == 'cosine':
            # 使用余弦相似度
            vec1 = Counter(words1)
            vec2 = Counter(words2)
            return cls.cosine_similarity(vec1, vec2)
            
        else:  # 'combined'
            # 结合Jaccard和余弦相似度
            set1 = set(words1)
            set2 = set(words2)
            jaccard_score = cls.jaccard_similarity(set1, set2)
            
            vec1 = Counter(words1)
            vec2 = Counter(words2)
            cosine_score = cls.cosine_similarity(vec1, vec2)
            
            # 加权平均
            return 0.4 * jaccard_score + 0.6 * cosine_score


class HotNewsCluster:
    """热点新闻聚类工具"""
    
    def __init__(self, similarity_threshold=0.35):
        """
        初始化聚类工具
        
        Args:
            similarity_threshold: 相似度阈值，高于此阈值的热点将被认为是相似的
        """
        self.similarity_threshold = similarity_threshold
        self.text_similarity = TextSimilarity()
    
    def cluster_hot_news(self, hot_items):
        """
        聚类热点新闻
        
        Args:
            hot_items: 热点信息列表，每个热点是一个字典
            
        Returns:
            聚类后的热点列表
        """
        if not hot_items:
            return []
            
        clusters = []  # 聚类结果
        
        # 处理每个热点项
        for item in hot_items:
            title = item['title']
            
            # 检查是否与现有簇匹配
            matched = False
            for cluster in clusters:
                # 计算与簇中第一个项的相似度
                first_item_title = cluster[0]['title']
                similarity = self.text_similarity.calculate_similarity(title, first_item_title)
                
                if similarity >= self.similarity_threshold:
                    # 找到匹配的簇，添加到该簇
                    cluster.append(item)
                    matched = True
                    break
            
            if not matched:
                # 没有匹配的簇，创建新簇
                clusters.append([item])
        
        # 对聚类结果进行排序
        sorted_clusters = sorted(clusters, key=lambda x: self._calculate_cluster_importance(x), reverse=True)
        
        # 提取每个簇的代表项
        result = []
        for cluster in sorted_clusters:
            representative = self._select_representative(cluster)
            result.append(representative)
            
        return result
    
    def _calculate_cluster_importance(self, cluster):
        """
        计算簇的重要性
        
        计算方法:
        1. 簇的大小（热点数量）
        2. 簇中热点的来源多样性
        3. 热度值（如果可用）
        
        Args:
            cluster: 热点簇
            
        Returns:
            重要性得分
        """
        # 簇的大小
        size_score = len(cluster) 
        
        # 来源多样性
        sources = set(item['source'] for item in cluster)
        diversity_score = len(sources)
        
        # 热度值（将字符串热度转为数值）
        try:
            # 尝试计算平均热度
            heat_values = []
            for item in cluster:
                heat = item.get('hot', '0')
                # 提取数字部分
                heat_num = re.sub(r'[^\d.]', '', str(heat))
                if heat_num:
                    heat_values.append(float(heat_num))
            
            heat_score = sum(heat_values) / len(heat_values) if heat_values else 0
        except (ValueError, ZeroDivisionError):
            heat_score = 0
        
        # 聚合热点加权（当簇大小大于1时大幅提高重要性）
        aggregation_bonus = 0
        if len(cluster) > 1:
            # 根据簇大小和来源多样性指数增加权重
            aggregation_bonus = 10000 + (size_score * diversity_score * 1000)
            
            # 如果来源多样性高（来自3个以上不同源），额外加分
            if diversity_score >= 3:
                aggregation_bonus *= 1.5
        
        # 综合得分 (加权)
        return aggregation_bonus + size_score * 2 + diversity_score * 3 + heat_score * 0.0001
    
    def _select_representative(self, cluster):
        """
        从簇中选择代表性热点
        
        策略:
        1. 优先选择来源多样性高的热点
        2. 优先选择标题长度适中的热点
        3. 合并来源列表
        
        Args:
            cluster: 热点簇
            
        Returns:
            代表性热点
        """
        if len(cluster) == 1:
            # 单项簇，直接返回
            return cluster[0]
        
        # 寻找最佳代表
        scores = []
        for i, item in enumerate(cluster):
            # 标题长度得分 (理想长度15-25个字符)
            title_len = len(item['title'])
            if 15 <= title_len <= 25:
                title_score = 10
            else:
                title_score = 10 - min(abs(title_len - 20), 10) 
            
            # 来源优先级 (主流媒体优先)
            source_priority = {
                "百度热榜": 5,
                "微博热搜": 4,
                "今日头条热榜": 3,
                "知乎热榜": 3,
            }
            source_score = source_priority.get(item['source'], 2)
            
            # 热度得分
            try:
                heat = item.get('hot', '0')
                heat_num = re.sub(r'[^\d.]', '', str(heat))
                heat_score = float(heat_num) * 0.0001 if heat_num else 0
            except ValueError:
                heat_score = 0
            
            # 总分
            total_score = title_score + source_score + heat_score
            scores.append((i, total_score))
        
        # 选择得分最高的
        scores.sort(key=lambda x: x[1], reverse=True)
        best_index = scores[0][0]
        representative = dict(cluster[best_index])
        
        # 合并所有来源
        sources = [item['source'] for item in cluster]
        source_count = Counter(sources)
        source_str = ", ".join(f"{s}({c})" for s, c in source_count.most_common())
        
        representative['source'] = source_str
        representative['cluster_size'] = len(cluster)
        
        return representative


if __name__ == "__main__":
    # 测试代码
    similarity = TextSimilarity()
    
    text1 = "国务院任免国家工作人员：免去李尚福的国务委员、国防部部长职务"
    text2 = "国务院任免国家工作人员 解除李尚福国防部部长职务"
    text3 = "美国宣布新一轮对俄制裁措施，涉及400多个实体和个人"
    
    print(f"文本1: {text1}")
    print(f"文本2: {text2}")
    print(f"文本3: {text3}")
    
    print(f"文本1和文本2的相似度: {similarity.calculate_similarity(text1, text2)}")
    print(f"文本1和文本3的相似度: {similarity.calculate_similarity(text1, text3)}")
    print(f"文本2和文本3的相似度: {similarity.calculate_similarity(text2, text3)}")
    
    # 测试热点聚类
    hot_items = [
        {'title': '国务院任免国家工作人员：免去李尚福的国务委员、国防部部长职务', 'source': '百度热榜', 'hot': '5000000'},
        {'title': '国务院任免国家工作人员 解除李尚福国防部部长职务', 'source': '微博热搜', 'hot': '4500000'},
        {'title': '中央军委纪委对李尚福涉嫌严重违纪违法进行审查调查', 'source': '今日头条热榜', 'hot': '3800000'},
        {'title': '美国宣布新一轮对俄制裁措施，涉及400多个实体和个人', 'source': '百度热榜', 'hot': '2000000'},
        {'title': '拜登政府宣布对俄罗斯实施新制裁', 'source': '微博热搜', 'hot': '1800000'},
        {'title': '俄罗斯外交部：将对美国新制裁进行回应', 'source': '微博热搜', 'hot': '1500000'},
    ]
    
    cluster_tool = HotNewsCluster(similarity_threshold=0.35)
    clustered_items = cluster_tool.cluster_hot_news(hot_items)
    
    print("\n聚类结果:")
    for idx, item in enumerate(clustered_items, 1):
        print(f"{idx}. {item['title']} - {item['source']} (簇大小: {item.get('cluster_size', 1)})") 