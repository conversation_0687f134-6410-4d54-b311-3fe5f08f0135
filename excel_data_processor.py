import matplotlib
matplotlib.use('Agg')  # 设置后端为Agg
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import traceback
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ExcelDataProcessor:
    def __init__(self, file_path):
        self.file_path = file_path
        self.main_data = None
        self.age_ranges = None
        self.district_map = None
        self.industry_map = None
        self.employment = None
        self.output_dir = 'output'
        
    def load_data(self):
        """加载所有工作表数据"""
        try:
            print("正在加载数据...")
            # 读取主表（跳过说明文字行）
            self.main_data = pd.read_excel(self.file_path, sheet_name='主表', skiprows=2)
            
            # 设置正确的列名
            self.main_data.columns = [
                '证件号码', '第17位身份证号', '户籍地址', 
                '性别', '周岁', '年龄段',
                '所属地市', '所在单位', '行业类别'
            ]
            
            # 读取其他表格
            self.age_ranges = pd.read_excel(self.file_path, sheet_name='年龄段区间表')
            self.district_map = pd.read_excel(self.file_path, sheet_name='县市区对应表')
            self.industry_map = pd.read_excel(self.file_path, sheet_name='行业对应表')
            self.employment = pd.read_excel(self.file_path, sheet_name='人员从业表')
            
            # 获取正确的顺序
            self.gender_order = ['男', '女']
            self.age_range_order = ['18周岁-34周岁', '35周岁-54周岁', '55周岁以上']
            self.district_order = self.district_map['地市'].unique()
            self.industry_order = self.industry_map['行业类别'].unique()
            
            print("数据加载完成！")
            print(f"主表记录数：{len(self.main_data)}")
        except Exception as e:
            print(f"数据加载出错：{str(e)}")
            print(traceback.format_exc())
            raise
        
    def process_gender(self):
        """根据身份证第17位判断性别"""
        try:
            print("正在处理性别信息...")
            # 确保第17位身份证号为数字
            self.main_data['第17位身份证号'] = pd.to_numeric(self.main_data['第17位身份证号'], errors='coerce')
            
            # 处理性别
            self.main_data['性别'] = self.main_data['第17位身份证号'].apply(
                lambda x: '男' if pd.notna(x) and int(x) % 2 == 1 else '女'
            )
            
            # 按预定义顺序统计性别分布
            gender_stats = pd.Series({gender: len(self.main_data[self.main_data['性别'] == gender]) 
                                    for gender in self.gender_order}, name='count')
            print("\n性别分布情况：")
            print(gender_stats)
            
            # 计算比例
            gender_props = pd.Series({gender: f"{len(self.main_data[self.main_data['性别'] == gender]) / len(self.main_data) * 100:.1f}%" 
                                    for gender in self.gender_order}, name='proportion')
            print("\n性别比例：")
            print(gender_props)
            
            return gender_stats
        except Exception as e:
            print(f"性别处理出错：{str(e)}")
            print(traceback.format_exc())
            raise
        
    def calculate_age(self):
        """计算周岁和年龄段"""
        try:
            print("正在计算年龄信息...")
            def extract_birth_date(id_number):
                try:
                    if pd.isna(id_number) or len(str(id_number)) < 18:
                        return None
                    birth_str = str(id_number)[6:14]
                    return datetime.strptime(birth_str, '%Y%m%d')
                except Exception as e:
                    print(f"出生日期提取错误，身份证号：{id_number}, 错误：{str(e)}")
                    return None
                
            def calculate_years(birth_date):
                if birth_date is None:
                    return None
                today = datetime.now()
                age = today.year - birth_date.year
                if (today.month, today.day) < (birth_date.month, birth_date.day):
                    age -= 1
                return age
                
            # 计算周岁
            self.main_data['出生日期'] = self.main_data['证件号码'].apply(extract_birth_date)
            self.main_data['周岁'] = self.main_data['出生日期'].apply(calculate_years)
            
            # 匹配年龄段
            def get_age_range(age):
                if pd.isna(age):
                    return '未知'
                if age < 18:
                    return '小于18周岁'
                elif 18 <= age <= 34:
                    return '18周岁-34周岁'
                elif 35 <= age <= 54:
                    return '35周岁-54周岁'
                else:
                    return '55周岁以上'
                
            self.main_data['年龄段'] = self.main_data['周岁'].apply(get_age_range)
            
            # 按预定义顺序统计年龄分布
            age_stats = pd.Series({age_range: len(self.main_data[self.main_data['年龄段'] == age_range])
                                 for age_range in self.age_range_order}, name='count')
            print("\n年龄段分布情况：")
            print(age_stats)
            
            # 计算比例
            age_props = pd.Series({age_range: f"{len(self.main_data[self.main_data['年龄段'] == age_range]) / len(self.main_data) * 100:.1f}%"
                                 for age_range in self.age_range_order}, name='proportion')
            print("\n年龄段比例：")
            print(age_props)
            
            return age_stats
        except Exception as e:
            print(f"年龄计算出错：{str(e)}")
            print(traceback.format_exc())
            raise
        
    def process_district(self):
        """处理地市信息"""
        try:
            print("正在处理地市信息...")
            def find_district(address):
                if pd.isna(address):
                    return '未知'
                for _, row in self.district_map.iterrows():
                    if str(row['县市区']).strip() in str(address):
                        return row['地市']
                return '未知'
                
            self.main_data['所属地市'] = self.main_data['户籍地址'].apply(find_district)
            
            # 按预定义顺序统计地市分布
            district_stats = pd.Series({district: len(self.main_data[self.main_data['所属地市'] == district])
                                     for district in self.district_order}, name='count')
            print("\n地市分布情况：")
            print(district_stats)
            
            # 计算比例
            district_props = pd.Series({district: f"{len(self.main_data[self.main_data['所属地市'] == district]) / len(self.main_data) * 100:.1f}%"
                                     for district in self.district_order}, name='proportion')
            print("\n地市分布比例：")
            print(district_props)
            
            return district_stats
        except Exception as e:
            print(f"地市处理出错：{str(e)}")
            print(traceback.format_exc())
            raise
        
    def process_employment(self):
        """处理单位和行业信息"""
        try:
            print("正在处理单位和行业信息...")
            # 合并人员从业表获取单位信息
            self.main_data = pd.merge(
                self.main_data,
                self.employment[['证件号', '所在单位']],
                left_on='证件号码',
                right_on='证件号',
                how='left'
            )
            self.main_data['所在单位'] = self.main_data['所在单位_y']
            self.main_data.drop(['证件号', '所在单位_y'], axis=1, inplace=True)
            
            # 确定行业类别
            def determine_industry(unit_name):
                if pd.isna(unit_name):
                    return '未知'
                for _, row in self.industry_map.iterrows():
                    if str(row['关键字']).strip() in str(unit_name):
                        return row['行业类别']
                return '其他'
                
            self.main_data['行业类别'] = self.main_data['所在单位'].apply(determine_industry)
            
            # 按预定义顺序统计行业分布
            industry_stats = pd.Series({industry: len(self.main_data[self.main_data['行业类别'] == industry])
                                     for industry in self.industry_order}, name='count')
            print("\n行业类别分布情况：")
            print(industry_stats)
            
            # 计算比例
            industry_props = pd.Series({industry: f"{len(self.main_data[self.main_data['行业类别'] == industry]) / len(self.main_data) * 100:.1f}%"
                                     for industry in self.industry_order}, name='proportion')
            print("\n行业类别分布比例：")
            print(industry_props)
            
            return industry_stats
        except Exception as e:
            print(f"单位和行业处理出错：{str(e)}")
            print(traceback.format_exc())
            raise
        
    def create_statistics(self):
        """创建统计表和图表"""
        try:
            print("正在生成统计信息...")
            # 创建输出目录
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)
            
            # 创建地市-行业类别统计表
            district_industry_stats = pd.crosstab(
                self.main_data['所属地市'],
                self.main_data['行业类别'],
                margins=True
            )
            
            # 重新排序行和列（保持All在最后）
            district_industry_stats = district_industry_stats.reindex(
                index=list(self.district_order) + ['All'],
                columns=list(self.industry_order) + ['All']
            )
            
            print("\n地市-行业类别统计表：")
            print(district_industry_stats)
            
            # 保存统计表
            district_industry_stats.to_excel(
                os.path.join(self.output_dir, '地市-行业类别统计表.xlsx')
            )
            
            try:
                print("正在生成性别比例饼图...")
                # 创建性别比例饼图
                plt.figure(figsize=(10, 6))
                gender_counts = pd.Series({gender: len(self.main_data[self.main_data['性别'] == gender])
                                        for gender in self.gender_order})
                plt.pie(
                    gender_counts,
                    labels=[f"{k}\n{v}人\n{v/len(self.main_data)*100:.1f}%" for k, v in gender_counts.items()],
                    autopct='',
                    colors=['#FF9999', '#66B2FF']
                )
                plt.title('性别比例分布')
                plt.savefig(os.path.join(self.output_dir, '性别比例.png'), bbox_inches='tight', dpi=300)
                plt.close()
                print("性别比例饼图生成完成！")
            except Exception as e:
                print(f"性别比例饼图生成出错：{str(e)}")
                print(traceback.format_exc())
            
            try:
                print("正在生成年龄段分布饼图...")
                # 创建年龄段分布饼图
                plt.figure(figsize=(12, 8))
                age_counts = pd.Series({age_range: len(self.main_data[self.main_data['年龄段'] == age_range])
                                     for age_range in self.age_range_order})
                plt.pie(
                    age_counts,
                    labels=[f"{k}\n{v}人\n{v/len(self.main_data)*100:.1f}%" for k, v in age_counts.items()],
                    autopct='',
                    colors=['#FF9999', '#66B2FF', '#99FF99', '#FFCC99']
                )
                plt.title('年龄段分布')
                plt.savefig(os.path.join(self.output_dir, '年龄段分布.png'), bbox_inches='tight', dpi=300)
                plt.close()
                print("年龄段分布饼图生成完成！")
            except Exception as e:
                print(f"年龄段分布饼图生成出错：{str(e)}")
                print(traceback.format_exc())
            
            return district_industry_stats
        except Exception as e:
            print(f"统计信息生成出错：{str(e)}")
            print(traceback.format_exc())
            raise
        
    def generate_report(self, stats):
        """生成分析报告"""
        try:
            print("正在生成分析报告...")
            report = []
            report.append("群体基本情况分析报告")
            report.append("=" * 50)
            
            # 总体情况
            total_count = len(self.main_data)
            report.append(f"\n1. 总体情况")
            report.append(f"总人数：{total_count}人")
            
            # 性别构成
            report.append(f"\n2. 性别构成")
            for gender in self.gender_order:
                count = len(self.main_data[self.main_data['性别'] == gender])
                percentage = count / total_count * 100
                report.append(f"{gender}：{count}人，占比{percentage:.1f}%")
                
            # 年龄分布
            report.append(f"\n3. 年龄分布")
            for age_range in self.age_range_order:
                count = len(self.main_data[self.main_data['年龄段'] == age_range])
                percentage = count / total_count * 100
                report.append(f"{age_range}：{count}人，占比{percentage:.1f}%")
                
            # 地域分布
            report.append(f"\n4. 地域分布")
            for district in self.district_order:
                if district != '未知':
                    count = len(self.main_data[self.main_data['所属地市'] == district])
                    percentage = count / total_count * 100
                    report.append(f"{district}：{count}人，占比{percentage:.1f}%")
                
            # 行业分布
            report.append(f"\n5. 行业分布")
            for industry in self.industry_order:
                if industry != '未知':
                    count = len(self.main_data[self.main_data['行业类别'] == industry])
                    percentage = count / total_count * 100
                    report.append(f"{industry}：{count}人，占比{percentage:.1f}%")
                    
            # 交叉分析
            report.append(f"\n6. 交叉分析")
            report.append("\n6.1 各地市主要行业分布")
            for district in self.district_order:
                if district != '未知':
                    district_data = self.main_data[self.main_data['所属地市'] == district]
                    district_industries = pd.Series({industry: len(district_data[district_data['行业类别'] == industry])
                                                  for industry in self.industry_order})
                    district_industries = district_industries.sort_values(ascending=False)
                    top3_industries = district_industries.head(3)
                    report.append(f"\n{district}前三大行业：")
                    for industry, count in top3_industries.items():
                        percentage = count / len(district_data) * 100
                        report.append(f"  - {industry}：{count}人，占比{percentage:.1f}%")
            
            # 保存报告
            report_text = '\n'.join(report)
            with open(os.path.join(self.output_dir, '群体基本情况分析报告.txt'), 'w', encoding='utf-8') as f:
                f.write(report_text)
            print("分析报告生成完成！")
        except Exception as e:
            print(f"报告生成出错：{str(e)}")
            print(traceback.format_exc())
            raise
            
    def save_results(self):
        """保存处理后的数据"""
        try:
            print("正在保存处理结果...")
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)
            # 删除临时列
            if '出生日期' in self.main_data.columns:
                self.main_data.drop('出生日期', axis=1, inplace=True)
            self.main_data.to_excel(os.path.join(self.output_dir, '处理结果.xlsx'), index=False)
            print("数据保存完成！")
        except Exception as e:
            print(f"数据保存出错：{str(e)}")
            print(traceback.format_exc())
            raise
        
    def process_all(self):
        """处理所有数据"""
        try:
            self.load_data()
            self.process_gender()
            self.calculate_age()
            self.process_district()
            self.process_employment()
            stats = self.create_statistics()
            self.generate_report(stats)
            self.save_results()
            print("数据处理完成！")
            return True
        except Exception as e:
            print(f"数据处理过程出错：{str(e)}")
            print(traceback.format_exc())
            return False

if __name__ == '__main__':
    try:
        processor = ExcelDataProcessor('EXCEL模拟题（附件）.xlsx')
        success = processor.process_all()
        if not success:
            print("程序执行失败！")
            exit(1)
    except Exception as e:
        print(f"程序执行出错：{str(e)}")
        print(traceback.format_exc())
        exit(1) 