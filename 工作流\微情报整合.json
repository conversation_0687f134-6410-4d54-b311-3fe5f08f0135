{"name": "微情报整合", "nodes": [{"parameters": {"formTitle": "微情报整合", "formDescription": "上传文件", "formFields": {"values": [{"fieldLabel": "data", "fieldType": "file", "multipleFiles": false}, {"fieldLabel": "model", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "gemini-2.5-flash-preview-05-20"}, {"option": "gemini-2.5-flash-preview-04-17"}, {"option": "gemini-2.0-flash"}]}}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-300, -60], "id": "972f8223-b9d4-430b-8172-a87e65f8577b", "name": "On form submission11", "webhookId": "edd9ba69-bd52-44c7-ba24-0728a4230300"}, {"parameters": {}, "type": "n8n-nodes-docx-converter.docxToText", "typeVersion": 1, "position": [-80, -60], "id": "cfb14d75-6640-4283-b2fc-24cf5d4402f5", "name": "DOCX to Text"}, {"parameters": {"operation": "toText", "sourceProperty": "candidates[0].content.parts[0].text", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [420, -60], "id": "54021de0-e8b6-4e75-8ba7-ba53baf9c091", "name": "Convert to File"}, {"parameters": {"operation": "toText", "sourceProperty": "choices[0].message.content", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [420, -260], "id": "29556e39-6bc3-4661-b237-ab93ed2cb3d5", "name": "Convert to File1"}, {"parameters": {"method": "POST", "url": "https://api.hunyuan.cloud.tencent.com/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer sk-F2SEuGAGFNRv9JiGuinFH1Az70CS2uOd3aCsm8qMHtHlEamS"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"hunyuan-turbo-latest\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"你是一名专业的安全分析师，擅长从多个来源整合信息，并生成清晰、全面的风险报告和防范建议。\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"# 角色你是一位专业的风险分析与情报撰写专家。# 任务你的任务是根据我提供的{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中的内容，自动生成一份结构化、精炼的“微情报”报告。你需要在没有外部范例文件的情况下，严格遵循下述的结构、内容和风格要求。特别注意，你需要从原始素材各段落的初步标题中提炼总结出最终的微情报标题。# 输入1.  **{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}**: 这是一个包含关于特定主题（事件）的多种风险信息、背景、数据和建议的文本文件。**重要提示：此文件中，每段话的第一句话通常是该段内容的初步拟定标题或核心概括。** 你需要从中提取核心内容并进行整合。# 输出要求生成的“微情报”报告必须严格遵循以下结构和要求：1.  **标题**:    *   **生成逻辑**：        *   首先，识别并理解{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中各段落首句的“初步拟定标题”。        *   然后，对这些初步标题进行综合分析、提炼和总结，抓住它们共同指向的核心主题和关键事件。        *   最终形成的标题应高度概括整个微情报的核心内容，并能反映出这些初步标题的共同指向。    *   **格式**：通常采用**“XXX风险提示”**或**“XXX风险预警”**的格式。XXX部分应基于上述提炼总结得出。    *   **内容**：应能准确概括{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中内容的核心主题。如果主题包含多个关键词。    *   **示例风格**（基于提炼总结后）：“‘XX风险提示”。2.  **风险基本情况/背景**:    *   位置：紧随标题之后。    *   内容：        *   开篇应点明主题事件（这通常可以从最终确定的标题或原始素材的初步标题中得到印证），并可简要提及该事件的特殊性或吸引力。        *   如果{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}内容中提及重要的、与当前主题直接相关的政策变化、时间节点或新情况，应在此处简明扼要地介绍，如没有则不用提及。        *   此部分应为后续的风险分析提供必要的上下文。    *   风格：语言简洁，概括性强，信息密度适中，为整个报告奠定基调。3.  **风险分析**:    *   固定开头：必须以**“分析认为，”**作为此部分的起始。    *   内容：        *   基于{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中提及的各类风险点（这些风险点可能在初步标题下方的段落正文中有详细描述），进行高度的提炼、整合和归纳。        *   将众多具体的风险描述总结为**3至5个主要的风险类别/方面**。        *   每个风险类别应有一个概括性的名称或描述（例如：情感矛盾纠纷风险、公共场所秩序风险、网络诈骗风险、个人极端行为风险等），以“风险的具体表现+风险概括名称”的形式展现，每点字数控制在20个字左右（如：存在利用节日通过“杀猪盘”、裸聊敲诈、婚恋交友等方式实施网络诈骗的风险。）不要以“XX风险：XXX”的形式呈现。        *   避免简单罗列原始素材中的风险点，而是要进行抽象和分类。4.  **对策建议**:    *   固定开头：必须以**“因此，建议各地做好以下X方面工作：”**作为此部分的起始，其中X是建议的条数（通常是三至五条，与风险分析的维度可以有所呼应，但不必一一对应）。    *   建议条目格式：        *   每条建议以中文数字序号开头，如“一是”、“二是”、“三是”等。        *   每条建议应以动词开头的短语作为小标题，内容具体、具有操作指导性。    *   内容来源：从{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中提取并整合各项对策建议。如果原始素材中建议较多或分散，需要筛选、合并，使其精炼且有代表性。    *   常见建议方向（供参考，具体需依据素材）：强化重点区域巡逻防控、加强矛盾纠纷排查化解、严查严打特定违法行为（可联合多部门）、加强普法与安全宣传教育、优化应急预案或管理措施等。5.  **语言风格**:    *   **正式与客观**: 使用规范的书面语言，避免口语化、情绪化表达。    *   **精炼与准确**: 用最少的文字表达清晰的含义，避免冗余和不必要的修饰。    *   **信息密度高**: 在有限的篇幅内传递核心信息。6.  **字数控制**:    *   严格控制总字数在**400-600字**之间，不能超过600字。7.  **信息处理核心原则**:    *   **核心信息提取**: 准确识别并提取{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中的关键背景、核心风险和主要对策。    *   **高度整合与归纳**: 对于{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中散落的、相似的或相关的风险点/建议，必须进行有效的整合与归纳，形成高度概括的条目，而不是简单复制粘贴或罗列。    *   **避免冗余**: 主动去除{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中的重复信息和与核心主旨关联度不高的细节。# 工作流程指导1.  仔细阅读并深刻理解{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中的全部内容，**特别关注每段话首句的初步标题信息**。2.  **首先处理标题**：根据各段初步标题，提炼总结形成最终的微情报标题。3.  然后，严格按照上述【输出要求】中明确的各部分结构、内容要点、格式和风格进行撰写报告的其余部分。4.  在撰写“风险分析”和“对策建议”部分时，特别注意信息的提炼、整合和分类能力，力求概括性和条理性。现在，我将提供{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}。请你严格按照上述指示进行处理，并生成“微情报”。最后生成的微情报以“标题+风险基本情况/背景+分析内容+对策建议”组成，除标题需要有，其他如风险基本情况/背景只要有内容即可，不需要有标题。\"\n                }\n  ],\n  \"temperature\": 0.7,\n  \"max_tokens\": 2000,\n  \"top_p\": 1,\n  \"frequency_penalty\": 0,\n  \"presence_penalty\": 0\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [180, -260], "id": "dda06b6b-3a56-4e5f-951c-a21f5e904916", "name": "腾讯混元", "credentials": {"httpHeaderAuth": {"id": "5fweHmjQyyNZiiZB", "name": "Header Auth account"}}}, {"parameters": {"method": "POST", "url": "=https://generativelanguage.googleapis.com/v1beta/models/{{ $('On form submission11').item.json.model }}:generateContent ", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"# 角色你是一位专业的风险分析与情报撰写专家。# 任务你的任务是根据我提供的{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中的内容，自动生成一份结构化、精炼的“微情报”报告。你需要在没有外部范例文件的情况下，严格遵循下述的结构、内容和风格要求。特别注意，你需要从原始素材各段落的初步标题中提炼总结出最终的微情报标题。# 输入1.  **{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}**: 这是一个包含关于特定主题（事件）的多种风险信息、背景、数据和建议的文本文件。**重要提示：此文件中，每段话的第一句话通常是该段内容的初步拟定标题或核心概括。** 你需要从中提取核心内容并进行整合。# 输出要求生成的“微情报”报告必须严格遵循以下结构和要求：1.  **标题**:    *   **生成逻辑**：        *   首先，识别并理解{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中各段落首句的“初步拟定标题”。        *   然后，对这些初步标题进行综合分析、提炼和总结，抓住它们共同指向的核心主题和关键事件。        *   最终形成的标题应高度概括整个微情报的核心内容，并能反映出这些初步标题的共同指向。    *   **格式**：通常采用**“XXX风险提示”**或**“XXX风险预警”**的格式。XXX部分应基于上述提炼总结得出。    *   **内容**：应能准确概括{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中内容的核心主题。如果主题包含多个关键词。    *   **示例风格**（基于提炼总结后）：“‘XX风险提示”。2.  **风险基本情况/背景**:    *   位置：紧随标题之后。    *   内容：        *   开篇应点明主题事件（这通常可以从最终确定的标题或原始素材的初步标题中得到印证），并可简要提及该事件的特殊性或吸引力。        *   如果{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}内容中提及重要的、与当前主题直接相关的政策变化、时间节点或新情况，应在此处简明扼要地介绍，如没有则不用提及。        *   此部分应为后续的风险分析提供必要的上下文。    *   风格：语言简洁，概括性强，信息密度适中，为整个报告奠定基调。3.  **风险分析**:    *   固定开头：必须以**“分析认为，”**作为此部分的起始。    *   内容：        *   基于{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中提及的各类风险点（这些风险点可能在初步标题下方的段落正文中有详细描述），进行高度的提炼、整合和归纳。        *   将众多具体的风险描述总结为**3至5个主要的风险类别/方面**。        *   每个风险类别应有一个概括性的名称或描述（例如：情感矛盾纠纷风险、公共场所秩序风险、网络诈骗风险、个人极端行为风险等），以“风险的具体表现+风险概括名称”的形式展现，每点字数控制在20个字左右（如：存在利用节日通过“杀猪盘”、裸聊敲诈、婚恋交友等方式实施网络诈骗的风险。）不要以“XX风险：XXX”的形式呈现。        *   避免简单罗列原始素材中的风险点，而是要进行抽象和分类。4.  **对策建议**:    *   固定开头：必须以**“因此，建议各地做好以下X方面工作：”**作为此部分的起始，其中X是建议的条数（通常是三至五条，与风险分析的维度可以有所呼应，但不必一一对应）。    *   建议条目格式：        *   每条建议以中文数字序号开头，如“一是”、“二是”、“三是”等。        *   每条建议应以动词开头的短语作为小标题，内容具体、具有操作指导性。    *   内容来源：从{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中提取并整合各项对策建议。如果原始素材中建议较多或分散，需要筛选、合并，使其精炼且有代表性。    *   常见建议方向（供参考，具体需依据素材）：强化重点区域巡逻防控、加强矛盾纠纷排查化解、严查严打特定违法行为（可联合多部门）、加强普法与安全宣传教育、优化应急预案或管理措施等。5.  **语言风格**:    *   **正式与客观**: 使用规范的书面语言，避免口语化、情绪化表达。    *   **精炼与准确**: 用最少的文字表达清晰的含义，避免冗余和不必要的修饰。    *   **信息密度高**: 在有限的篇幅内传递核心信息。6.  **字数控制**:    *   严格控制总字数在**400-600字**之间，不能超过600字。7.  **信息处理核心原则**:    *   **核心信息提取**: 准确识别并提取{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中的关键背景、核心风险和主要对策。    *   **高度整合与归纳**: 对于{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中散落的、相似的或相关的风险点/建议，必须进行有效的整合与归纳，形成高度概括的条目，而不是简单复制粘贴或罗列。    *   **避免冗余**: 主动去除{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中的重复信息和与核心主旨关联度不高的细节。# 工作流程指导1.  仔细阅读并深刻理解{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}中的全部内容，**特别关注每段话首句的初步标题信息**。2.  **首先处理标题**：根据各段初步标题，提炼总结形成最终的微情报标题。3.  然后，严格按照上述【输出要求】中明确的各部分结构、内容要点、格式和风格进行撰写报告的其余部分。4.  在撰写“风险分析”和“对策建议”部分时，特别注意信息的提炼、整合和分类能力，力求概括性和条理性。现在，我将提供{{ JSON.stringify($json.text).replace(/^\\\"|\\\"$/g, '') }}。请你严格按照上述指示进行处理，并生成“微情报”。最后生成的微情报以“标题+风险基本情况/背景+分析内容+对策建议”组成，除标题需要有，其他如风险基本情况/背景只要有内容即可，不需要有标题。\"\n        }\n      ]\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [180, -60], "id": "ed0d29d8-bb38-4428-858e-196fea307818", "name": "Gemini"}], "pinData": {}, "connections": {"On form submission11": {"main": [[{"node": "DOCX to Text", "type": "main", "index": 0}]]}, "DOCX to Text": {"main": [[{"node": "腾讯混元", "type": "main", "index": 0}, {"node": "Gemini", "type": "main", "index": 0}]]}, "腾讯混元": {"main": [[{"node": "Convert to File1", "type": "main", "index": 0}]]}, "Gemini": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0ef3af18-1232-4c84-9e3a-2748cd347bc4", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e91fe71fc41c70f3cd8d49a32329efea986f840395719f4b74ff7ced73716ab0"}, "id": "0ULvLJWl697qK24U", "tags": []}