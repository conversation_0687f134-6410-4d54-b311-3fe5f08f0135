import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import os
from pathlib import Path
import chardet
import tempfile
import win32com.client
import xml.etree.ElementTree as ET
from io import BytesIO
import warnings
import logging
import sys
import io
import ctypes

# 禁用标准错误输出
if sys.platform.startswith('win'):
    # Windows平台
    kernel32 = ctypes.WinDLL('kernel32')
    kernel32.FreeConsole()
else:
    # 非Windows平台
    sys.stderr = open(os.devnull, 'w')

# 配置日志记录
logging.getLogger('PIL').setLevel(logging.ERROR)

# 忽略所有警告
warnings.filterwarnings('ignore')
warnings.simplefilter('ignore')
os.environ['PYTHONWARNINGS'] = 'ignore'

# 设置pandas选项
pd.options.mode.chained_assignment = None

class FileConverterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel文件格式转换器")
        self.root.geometry("600x500")
        
        # 创建主框架
        self.main_frame = tk.Frame(self.root, padx=20, pady=20)
        self.main_frame.pack(expand=True, fill='both')
        
        # 添加版权信息标签
        self.copyright_label = tk.Label(
            self.main_frame,
            text="版权所有：台州市公安局 解晟",
            font=("微软雅黑", 9),
            fg="black"
        )
        self.copyright_label.pack(side='bottom', pady=10)
        
        # 创建按钮和标签
        self.select_btn = tk.Button(
            self.main_frame, 
            text="选择文件", 
            command=self.select_files,
            width=20,
            height=2
        )
        self.select_btn.pack(pady=10)
        
        self.file_label = tk.Label(
            self.main_frame,
            text="未选择文件",
            wraplength=500
        )
        self.file_label.pack(pady=10)
        
        # 添加输出路径选择
        self.output_frame = tk.Frame(self.main_frame)
        self.output_frame.pack(fill='x', pady=10)
        
        self.output_label = tk.Label(
            self.output_frame,
            text="输出路径："
        )
        self.output_label.pack(side='left', padx=5)
        
        self.output_path = tk.StringVar(value=os.path.expanduser("~"))
        self.output_entry = tk.Entry(
            self.output_frame,
            textvariable=self.output_path,
            width=50
        )
        self.output_entry.pack(side='left', padx=5)
        
        self.browse_btn = tk.Button(
            self.output_frame,
            text="浏览",
            command=self.select_output_dir
        )
        self.browse_btn.pack(side='left', padx=5)
        
        self.convert_btn = tk.Button(
            self.main_frame,
            text="开始转换",
            command=self.convert_files,
            width=20,
            height=2,
            state='disabled'
        )
        self.convert_btn.pack(pady=20)
        
        self.selected_files = []
        
    def select_files(self):
        filetypes = (
            ('Excel文件', '*.xls;*.xlsx;*.csv'),
            ('所有文件', '*.*')
        )
        
        files = filedialog.askopenfilenames(
            title='选择需要转换的文件',
            filetypes=filetypes
        )
        
        if files:
            self.selected_files = files
            self.file_label.config(text=f"已选择 {len(files)} 个文件")
            self.convert_btn.config(state='normal')
    
    def detect_encoding(self, file_path):
        with open(file_path, 'rb') as file:
            raw_data = file.read()
            result = chardet.detect(raw_data)
            return result['encoding']
    
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = filedialog.askdirectory(
            title='选择输出文件夹',
            initialdir=self.output_path.get()
        )
        if dir_path:
            self.output_path.set(dir_path)
    
    def convert_files(self):
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择文件！")
            return
            
        # 检查输出目录是否存在
        output_dir = self.output_path.get()
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
            except Exception as e:
                messagebox.showerror("错误", f"创建输出目录失败：{str(e)}")
                return
            
        success_count = 0
        error_files = []
        excel = None
        
        try:
            # 创建Excel应用程序实例
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False
            excel.DisplayAlerts = False  # 禁用Excel警告弹窗
            
            for file_path in self.selected_files:
                try:
                    # 规范化文件路径
                    abs_file_path = os.path.abspath(file_path)
                    output_filename = Path(file_path).stem + '_converted.xlsx'
                    output_path = os.path.join(output_dir, output_filename)
                    abs_output_path = os.path.abspath(output_path)
                    
                    # 处理CSV文件
                    if file_path.lower().endswith('.csv'):
                        try:
                            # 首先尝试检测编码
                            with open(file_path, 'rb') as f:
                                raw_data = f.read()
                                result = chardet.detect(raw_data)
                                detected_encoding = result['encoding']
                                
                            # 尝试不同的编码方式
                            encodings_to_try = [
                                detected_encoding,
                                'gb18030',
                                'gbk',
                                'gb2312',
                                'utf-8',
                                'utf-16',
                                'ascii',
                                'iso-8859-1'
                            ]
                            
                            df = None
                            last_error = None
                            
                            for encoding in encodings_to_try:
                                if not encoding:
                                    continue
                                try:
                                    df = pd.read_csv(file_path, encoding=encoding)
                                    break
                                except Exception as e:
                                    last_error = e
                                    continue
                            
                            if df is not None:
                                df.to_excel(abs_output_path, index=False, engine='openpyxl')
                                success_count += 1
                            else:
                                raise Exception(f"无法读取CSV文件，尝试了以下编码：{encodings_to_try}。最后的错误：{str(last_error)}")
                                
                        except Exception as e:
                            raise Exception(f"CSV转换失败: {str(e)}")
                            
                    else:
                        # 处理Excel文件（保持原有的处理逻辑）
                        try:
                            try:
                                df = pd.read_excel(file_path, engine='xlrd')
                                df.to_excel(abs_output_path, index=False, engine='openpyxl')
                                success_count += 1
                                continue
                            except:
                                pass
                            
                            # 如果pandas方法失败，使用Excel COM对象
                            wb = excel.Workbooks.Open(abs_file_path)
                            
                            # 使用临时文件避免路径问题
                            temp_path = os.path.join(tempfile.gettempdir(), f'temp_{output_filename}')
                            abs_temp_path = os.path.abspath(temp_path)
                            
                            # 保存为临时文件
                            wb.SaveAs(abs_temp_path, FileFormat=51)  # 51 表示 xlsx 格式
                            wb.Close()
                            
                            # 如果临时文件创建成功，移动到目标位置
                            if os.path.exists(abs_temp_path):
                                # 如果目标文件已存在，先删除
                                if os.path.exists(abs_output_path):
                                    os.remove(abs_output_path)
                                os.rename(abs_temp_path, abs_output_path)
                                success_count += 1
                            else:
                                raise Exception("临时文件创建失败")
                            
                        except Exception as e:
                            raise Exception(f"Excel转换失败: {str(e)}")
                            
                except Exception as e:
                    error_files.append(f"{Path(file_path).name}: {str(e)}")
                    
        except Exception as e:
            messagebox.showerror("错误", f"Excel应用程序初始化失败: {str(e)}")
        finally:
            # 确保Excel应用程序被正确关闭
            if excel:
                try:
                    excel.Quit()
                except:
                    pass
        
        # 显示转换结果
        result_message = f"成功转换 {success_count} 个文件\n输出目录：{output_dir}\n"
        if error_files:
            result_message += "\n转换失败的文件：\n" + "\n".join(error_files)
        
        messagebox.showinfo("转换完成", result_message)
        
        # 重置界面
        self.selected_files = []
        self.file_label.config(text="未选择文件")
        self.convert_btn.config(state='disabled')

if __name__ == "__main__":
    root = tk.Tk()
    app = FileConverterApp(root)
    root.mainloop() 