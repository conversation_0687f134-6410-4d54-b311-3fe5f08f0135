{"name": "批量图片OCR转文本", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-320, -60], "id": "fc293c72-664a-4f7a-8a62-3d6ebbf20064", "name": "When clicking ‘Test workflow’"}, {"parameters": {"fileSelector": "C:/Users/<USER>/Pictures/n8n/**", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-100, -60], "id": "a0f698d3-441a-4fe6-a552-edbde0c7cd21", "name": "Read/Write Files from Disk"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [120, -60], "id": "1b2cf0ff-ff04-466a-9f8f-b66834fc45d7", "name": "Loop Over Items"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "name": "Replace Me", "typeVersion": 1, "position": [1280, -40], "id": "a7a6b237-15c7-46f3-841e-6b40fc6db60e"}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [360, -40], "id": "1c74e490-1da1-4a84-a8a2-1c542dce6439", "name": "Extract from File"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyD7qOlQ8ypC0dxbu8SN3f6ExZrvE_YoSpA", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\"text\": \"提取图中的内容并保持原有格式输出\"},\n        {\n          \"inline_data\": {\n            \"mime_type\": \"image/jpeg\",\n            \"data\": \"{{ $json.data }}\"\n          }\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"maxOutputTokens\": 65536\n  }\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [560, -40], "id": "ae3e5cce-b52c-40d0-9d9c-96d8f196068d", "name": "HTTP Request"}, {"parameters": {"operation": "toText", "sourceProperty": "candidates[0].content.parts[0].text", "options": {"encoding": "utf8"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [780, -40], "id": "7aafffb1-9fad-48c3-a191-c365f6a2527d", "name": "Convert to File"}, {"parameters": {"operation": "write", "fileName": "=C:/Users/<USER>/Downloads/n8n-text/{{ $('Loop Over Items').item.json.fileName.split(\".\")[0] }}.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1000, -40], "id": "e19e2560-a79a-462a-a737-079f20093011", "name": "Read/Write Files from Disk1"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Extract from File", "type": "main", "index": 0}]]}, "Replace Me": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "Replace Me", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "7b7c9a83-ebc8-4cb5-83d5-38410ed0a90a", "meta": {"instanceId": "5da0c426bbf64339957ef20dd65d6c58428c1e553c75870db3eb45bb1d5d95d5"}, "id": "9dunOgNuLdQ6VNOO", "tags": []}