app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 风险闭环
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.16@d41b09aca46cdd3876f70b4c91d464c4588fc0bdc844ced6ee426283ead6ce8e
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.2.7@b8a04c0155eb3b9d43ed1199b4387e7f67ef75ad63fcec466eab31a726e2c3a0
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/volcengine_maas:0.0.22@aa29eb006d5ab265ecd16334d165b1757a23d22ca2c9d7d9f7d278941caa9cf8
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: start
        targetType: llm
      id: 1752390343562-llm
      source: '1752390343562'
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: answer
      id: llm-answer
      source: llm
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1752390343562-source-1752390439939-target
      source: '1752390343562'
      sourceHandle: source
      target: '1752390439939'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1752390343562-source-1752390443988-target
      source: '1752390343562'
      sourceHandle: source
      target: '1752390443988'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1752390343562-source-1752390625781-target
      source: '1752390343562'
      sourceHandle: source
      target: '1752390625781'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1752390439939-source-answer-target
      source: '1752390439939'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1752390443988-source-answer-target
      source: '1752390443988'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1752390625781-source-answer-target
      source: '1752390625781'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 53
      id: '1752390343562'
      position:
        x: 30
        y: 244.5
      positionAbsolute:
        x: 30
        y: 244.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.5-flash
          provider: langgenius/gemini/google
        prompt_template:
        - id: 4590186d-8745-497b-9877-b21a66bd8fd2
          role: system
          text: '{{#sys.query#}}中的内容是针对公安工作中的风险点提出的具体对策建议。请你首先根据对策建议中的内容，判断需要公安机关中的刑侦、经侦、治安、网安、政保、法制、巡特警、宣教、食药环等哪些警种部门参与工作，并以“各地收到风险提示后，指令治安、刑侦、宣传……等部门及各辖区派出所开展工作。”（不需要的警种不要列举）为例开头，紧接着对{{#sys.query#}}中的内容进行缩写，去掉一是、二是、三是……，总字数严格控制在110-150字。'
        selected: false
        title: Gemini2.5flash
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: llm
      position:
        x: 333
        y: 244.5
      positionAbsolute:
        x: 333
        y: 244.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        answer: '-豆包1.5pro-

          {{#1752390443988.text#}}

          <br/>

          -豆包1.6flash-

          {{#1752390439939.text#}}

          <br/>

          -豆包1.6-

          {{#1752390625781.text#}}

          <br/>

          -Gemini2.5flash-

          {{#llm.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 193
      id: answer
      position:
        x: 636
        y: 373.5
      positionAbsolute:
        x: 636
        y: 373.5
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: 豆包1.6flash
          provider: langgenius/volcengine_maas/volcengine_maas
        prompt_template:
        - id: c2c8bab5-c9e7-4575-9f30-6a86b77bb96c
          role: system
          text: '{{#sys.query#}}中的内容是针对公安工作中的风险点提出的具体对策建议。请你首先根据对策建议中的内容，判断需要公安机关中的刑侦、经侦、治安、网安、政保、法制、巡特警、宣教、食药环等哪些警种部门参与工作，并以“各地收到风险提示后，指令治安、刑侦、宣传……等部门及各辖区派出所开展工作。”（不需要的警种不要列举）为例开头，紧接着对{{#sys.query#}}中的内容进行缩写，去掉一是、二是、三是……，总字数严格控制在110-150字。'
        selected: false
        title: 豆包1.6flash
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1752390439939'
      position:
        x: 333
        y: 373.5
      positionAbsolute:
        x: 333
        y: 373.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: doubao-1-5-pro-256k-250115
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: c90a9de0-d796-4a06-a1cc-bf6fffb820fd
          role: system
          text: '{{#sys.query#}}中的内容是针对公安工作中的风险点提出的具体对策建议。请你首先根据对策建议中的内容，判断需要公安机关中的刑侦、经侦、治安、网安、政保、法制、巡特警、宣教、食药环等哪些警种部门参与工作，并以“各地收到风险提示后，指令治安、刑侦、宣传……等部门及各辖区派出所开展工作。”（不需要的警种不要列举）为例开头，紧接着对{{#sys.query#}}中的内容进行缩写，去掉一是、二是、三是……，总字数严格控制在110-150字。'
        selected: false
        title: 豆包1.5pro
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1752390443988'
      position:
        x: 333
        y: 502.5
      positionAbsolute:
        x: 333
        y: 502.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: 豆包1.6
          provider: langgenius/volcengine_maas/volcengine_maas
        prompt_template:
        - id: 4376ac96-44a0-4403-80cc-6b88f99f7582
          role: system
          text: '{{#sys.query#}}中的内容是针对公安工作中的风险点提出的具体对策建议。请你首先根据对策建议中的内容，判断需要公安机关中的刑侦、经侦、治安、网安、政保、法制、巡特警、宣教、食药环等哪些警种部门参与工作，并以“各地收到风险提示后，指令治安、刑侦、宣传……等部门及各辖区派出所开展工作。”（不需要的警种不要列举）为例开头，紧接着对{{#sys.query#}}中的内容进行缩写，去掉一是、二是、三是……，总字数严格控制在110-150字。'
        selected: false
        title: 豆包1.6
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1752390625781'
      position:
        x: 333
        y: 631.5
      positionAbsolute:
        x: 333
        y: 631.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    viewport:
      x: 9.75
      y: -5.5
      zoom: 0.7
