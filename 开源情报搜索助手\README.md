# 开源情报搜索助手

## 📝 项目简介
开源情报搜索助手是一款功能强大的热点信息聚合工具，支持Windows系统(Win11/Win10/Win7)。软件可以实时收集国内外热门平台的热点信息，将相似内容进行智能整合并展示。

## ✨ 主要功能
- 📊 国内热点搜索与展示（百度热榜、微博热搜、抖音热搜、今日头条热榜等）
- 🌏 国际热点搜索与展示（news.buzzing.cc、X(Twitter)、Youtube、Tiktok等）
- 🔄 智能整合相似内容并排序
- 🈳 国际热点自动翻译为中文简体
- 👁️ 热点详情查看功能
- 🎨 美观直观的用户界面

## 🔧 系统要求
- 操作系统：Windows 11 / Windows 10 / Windows 7
- Python版本：Python 3.7+
- 网络连接：需要稳定的网络连接

## 📦 依赖库
```
requests>=2.25.1
beautifulsoup4>=4.9.3
PyQt5>=5.15.2
lxml>=4.6.3
translate>=3.6.1
jieba>=0.42.1
```

## 🚀 安装方法
1. 确保您已安装Python 3.7或更高版本
2. 克隆或下载本项目到本地
3. 在项目根目录打开命令提示符或终端
4. 安装所需依赖库：
   ```
   pip install -r requirements.txt
   ```

## 📱 使用方法
1. 在项目根目录运行主程序：
   ```
   python main.py
   ```
2. 在界面左侧选择搜索类型（国内热点/国际热点）
3. 点击"刷新"按钮获取最新热点
4. 在左侧列表中点击感兴趣的热点，右侧将显示详细内容

## 🔍 功能详解
### 国内热点
支持获取以下平台的热点信息：
- 百度热榜
- 微博热搜
- 抖音热搜
- 今日头条热榜
- 知乎热榜
- 哔哩哔哩热门

### 国际热点
支持获取以下平台的信息：
- **Buzzing.cc国际热点**：多个备选URL（news.buzzing.cc、buzzing.cc、hn.buzzing.cc等），支持多种网页结构和内容格式的自动识别
- **X(Twitter)热门话题**：支持从多个信息源获取，包括X.buzzing.cc、Google Trends等，自动处理多种格式
- **Youtube热门视频**：支持直接从YouTube抓取（无需API）或通过辅助网站（yt.buzzing.cc、whatstrending.com）获取热门内容
- **TikTok热门视频**：从多个辅助网站获取TikTok热门趋势（influencermarketinghub.com、remotion.com等）
- Reddit热门话题
- Google Trends

### 智能整合
- 使用文本相似度算法识别相似内容
- 根据热度和来源多样性排序
- 自动合并相似热点减少信息冗余

### 容错机制
- 每个数据源提供多个备选URL
- 多种数据提取方式应对不同网页结构
- 网络问题时自动使用模拟数据，确保用户体验连续性

### 翻译功能
- 自动将国际热点翻译为中文简体
- 保留原文与译文对照
- 支持多种语言检测与翻译

## 🛠️ 技术实现
- 数据获取：使用requests和BeautifulSoup爬取各平台数据
- 界面实现：基于PyQt5构建美观界面
- 文本处理：jieba分词与文本相似度计算
- 翻译服务：使用translate库实现多语言翻译

## 🔄 爬虫系统更新日志

### 今日头条爬虫 (2023年7月更新)
今日头条爬虫经过全面优化，解决了因网站结构变化导致的热榜数据获取失败问题：

- **多阶段请求策略**：模拟真实浏览器行为，先访问首页获取cookies，再访问热榜页
- **高级请求头管理**：动态生成更真实的请求头，包含完整的浏览器标识和引用信息
- **多URL备选机制**：支持多个热榜URL地址，自动尝试不同入口
- **增强的数据提取**：优化了JSON和HTML双重解析能力，支持多种网页结构
- **智能递归搜索**：采用递归算法在复杂JSON结构中定位热榜数据
- **完善的错误处理**：详细日志记录和多层次容错机制
- **退避重试机制**：请求失败时使用指数退避策略，减轻服务器负担
- **API降级策略**：页面解析失败时尝试API接口获取数据
- **模拟数据备份**：所有方法失败时返回模拟数据，确保程序正常运行

### 爬虫系统整体优化
- 增强的会话管理：使用session保持连接，提高效率和真实性
- 智能延迟：在请求间添加随机延迟，模拟人类行为
- 精确的错误日志：详细记录每一步骤和可能的失败原因
- 简化测试：添加专用测试脚本，方便问题诊断和修复
- 代理自动切换：根据国内/国际网站类型自动应用不同代理配置

## 📝 注意事项
- 本工具仅供个人学习和研究使用
- 请遵守相关网站的使用条款和规定
- 爬取频率过高可能导致IP被限制
- 部分网站可能需要使用代理访问

## 📄 许可证
MIT License 

## 功能特性

- **多平台热点获取**：支持获取微博、百度、今日头条、抖音等国内热搜平台的热点话题，以及国际热点。
- **智能内容聚合**：自动整合不同平台的热点数据，提供全面的热点视图。相似内容会被聚合并置顶显示，并用特殊标记突出显示。
- **详细内容展示**：点击热点条目可查看详细内容，包括原文链接和相关图片。
- **智能翻译功能**：自动识别并翻译国际热点内容，便于中文用户理解。
- **历史记录查询**：保存历史热点数据，支持查询过去的热点信息。
- **数据统计分析**：提供热点数据的统计分析功能，展示热点趋势。
- **代理设置功能**：支持为国内和国际网站分别配置代理，解决网络访问问题。
- **智能内容模拟**：当网络原因无法获取实际内容时，提供模拟数据，确保软件可用性。

## 使用说明

### 基本使用
1. 启动程序后，点击左侧的"国内热点"或"国际热点"按钮，获取相应的热点信息。
2. 在热点列表中点击任意条目，右侧将显示该热点的详细内容。
3. 点击内容下方的原文链接，可在浏览器中打开原始页面。

### 热点聚合显示
1. 软件会自动分析不同来源的相似热点，并将它们聚合为单条信息。
2. 聚合热点会被优先排序，显示在列表前端，并带有🔥标记和粉色背景，方便快速识别重要热点。
3. 聚合热点的来源信息会显示所有原始平台，包括聚合的数量，如"百度热榜(2), 微博热搜(1)"。
4. 聚合越多平台的热点，排序越靠前，表明该热点在多个平台都受到关注，具有更高的价值。

### 代理设置
1. 点击顶部菜单栏的"设置" -> "代理设置"，打开代理配置对话框。
2. 根据需要，为国内网站和国际网站分别配置代理：
   - 勾选"启用国内网站代理"或"启用国际网站代理"来开启相应代理。
   - 在代理地址栏中输入代理服务器地址，格式为：`http://地址:端口` 或 `socks5://地址:端口`。
   - 分别设置HTTP和HTTPS代理，或者使用相同的代理地址。
3. 点击"确定"保存设置。设置将立即生效，影响后续的搜索请求。
4. **设置会自动保存**，下次启动程序时会自动加载上次的代理配置，无需重复设置。

代理设置示例：
- HTTP代理：`http://127.0.0.1:7890`
- HTTPS代理：`http://127.0.0.1:7890`
- SOCKS代理：`socks5://127.0.0.1:1080`

注意：通常情况下，国内热点不需要使用代理，而国际热点可能需要代理才能访问。如果您的系统已配置全局代理，也可以不在软件中设置代理。

### 内容查看功能
1. 从热点列表中选择任意条目，软件将尝试获取该热点的详细内容。
2. 内容获取支持智能降级机制：
   - 首先尝试从原始网站获取完整内容，包括正文和图片。
   - 如遇网络问题或访问限制，将尝试多种备选方式提取内容。
   - 当所有尝试均失败时，会显示模拟内容，确保用户体验不中断。
3. 内容页面包含以下元素：
   - 标题：热点的完整标题
   - 内容：热点的详细描述或正文
   - 图片：如果有相关图片，将在内容下方显示
   - 原文链接：点击可在浏览器中打开原始页面

提示：对于某些需要登录或有访问限制的内容（如微博热搜详情、抖音视频等），建议点击原文链接在原平台查看完整内容。 