{"mcpServers": {"Sequential Thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking", "--config", "\"{}\""], "alwaysAllow": ["sequentialthinking"]}, "files": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "C:/Users/<USER>/Downloads/AI写代码"], "alwaysAllow": ["read_file"], "disabled": false}, "excel": {"command": "cmd", "args": ["/c", "npx", "--yes", "@negokaz/excel-mcp-server"], "env": {"EXCEL_MCP_PAGING_CELLS_LIMIT": "4000"}, "alwaysAllow": ["excel_copy_sheet"]}, "quickchart-server": {"command": "cmd", "args": ["/c", "npx", "-y", "@gongrzhe/quickchart-mcp-server"], "alwaysAllow": ["generate_chart"]}, "hotnews": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@wopal/mcp-server-hotnews", "--config", "\"{}\""], "alwaysAllow": ["get_hot_news"]}}}