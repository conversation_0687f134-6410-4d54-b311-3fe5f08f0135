# 全网热点新闻分析系统

全网热点新闻分析系统是一款功能强大的应用程序，可以爬取、分析多个平台的热点新闻数据，帮助用户了解当前全网热点话题。

## 功能特点

- **多平台数据爬取**：支持知乎、微博、百度、B站、今日头条、抖音等主流平台的热点数据爬取
- **智能数据分析**：对热点数据进行智能分析，包括热度归一化、关键词提取和跨平台分析
- **可视化图表展示**：通过多种图表方式直观展示分析结果
- **跨平台热点识别**：自动识别跨平台热点，发现真正的全网热点话题
- **数据导出与保存**：支持将爬取的数据导出为CSV或JSON格式

## 系统要求

- Python 3.6+
- 依赖包：详见requirements.txt

## 安装步骤

1. 克隆或下载本项目代码
2. 安装所需依赖包：
   ```
   pip install -r requirements.txt
   ```
3. 运行主程序：
   ```
   python main.py
   ```

## 使用说明

### 主界面

主界面包含以下功能区域：
- 顶部控制栏：设置爬取数量、开始爬取、分析数据、保存/加载数据等
- 选项卡区域：包含热点新闻列表、分析结果、数据图表、跨平台热点等多个选项卡

### 爬取数据

1. 在顶部控制栏设置爬取数量（10-200条）
2. 点击"开始爬取"按钮
3. 等待爬取完成，查看爬取结果

### 分析数据

1. 爬取数据完成后，点击"分析数据"按钮
2. 等待分析完成，系统会自动显示分析结果
3. 可以在不同选项卡查看详细的分析内容

### 数据导出

1. 点击"保存数据"按钮
2. 选择保存格式（CSV或JSON）和保存位置
3. 确认保存

## 最近更新

### v1.0.2 (2025-03-16)

- **增强功能**：
  - 全面改进今日头条数据爬取功能，增加多种备选方案
  - 增强数据验证机制，确保所有平台数据的完整性和有效性
  - 优化UI界面，改进表格显示和数据处理逻辑
  - 增加详细的日志记录，便于问题排查

- **修复问题**：
  - 修复了今日头条数据无法正常显示的问题
  - 修复了表格背景色设置导致的程序崩溃问题
  - 修复了热度值异常的数据处理问题
  - 解决了部分平台数据格式不一致导致的显示问题

### v1.0.1 (2025-03-16)

- **增强功能**：
  - 爬取总数增加到200条，提供更全面的热点数据
  - 优化抖音数据爬取功能，实现真实数据获取
  - 改进百度热搜爬取功能，增加多重备选方案

- **修复问题**：
  - 修复了百度热搜无法爬取的问题
  - 修复了今日头条数据解析错误
  - 修复了UI界面中QTableWidget相关错误

## 技术实现

### 数据爬取策略

本系统采用多重备选方案确保数据爬取的稳定性：

1. **主要API获取**：优先使用各平台官方或第三方API获取数据
2. **页面解析获取**：当API不可用时，通过解析页面HTML获取数据
3. **备选API获取**：使用备选API作为第三方案
4. **模拟数据生成**：当所有方案都失败时，生成模拟数据以保证系统正常运行

### 数据验证机制

为确保数据质量，系统实现了严格的数据验证机制：

1. **完整性验证**：检查必要字段是否存在
2. **有效性验证**：验证数据内容是否有效
3. **格式转换**：统一不同平台的数据格式
4. **异常处理**：对异常数据进行修正或过滤

## 开发者指南

### 项目结构

- `main.py`: 程序入口
- `ui.py`: 用户界面实现
- `crawler.py`: 数据爬取实现
- `analyzer.py`: 数据分析实现

### 扩展平台

如需添加新的平台支持，请在`crawler.py`中添加相应的爬取方法，并在`fetch_all_platforms`方法中调用。

## 注意事项

- 本程序仅供学习和研究使用，请勿用于商业用途
- 爬取数据时请遵守相关网站的使用条款和规定
- 爬取频率过高可能导致IP被封，请合理设置爬取间隔

## 许可证

MIT License 