import pandas as pd
import re

# 读取第一个文件SS3-2.csv
df1 = pd.read_csv('SS3-2.csv')

# 清理身份证号码中的噪音
def clean_id_number(id_number):
    # 去除标点符号、中文字符和空格
    cleaned = re.sub(r'[^\d]', '', str(id_number))
    return cleaned

# 提取并清理第一个文件中的身份证号码
cleaned_ids = df1['人员身份证号码'].apply(clean_id_number).tolist()

# 读取第二个文件SS3-1.xlsx
df2 = pd.read_excel('666.xlsx')

# 更新第二个文件的第三列
df2['是否比中B市轨迹人员'] = df2['人员身份证号码'].apply(lambda x: '比中' if clean_id_number(x) in cleaned_ids else '')

# 保存更新后的第二个文件
df2.to_excel('666_updated.xlsx', index=False)

print("处理完成，结果已保存到666_updated.xlsx")