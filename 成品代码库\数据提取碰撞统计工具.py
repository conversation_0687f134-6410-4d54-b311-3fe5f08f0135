# -*- coding: utf-8 -*-
"""
数据提取碰撞统计工具
用于从各种文件格式中提取数据并进行匹配分析
支持的数据类型：手机号码、身份证号码、车牌号、完整内容
支持的文件格式：Excel、CSV、Word、TXT
"""

import sys
import os
import re
import pandas as pd
from docx import Document
from PyQt5.QtWidgets import *
from PyQt5.QtCore import Qt, QThread, pyqtSignal

# 添加环境变量
os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = os.path.join(os.path.dirname(sys.executable), "Lib/site-packages/PyQt5/Qt5/plugins/platforms")

# ================ 正则表达式模式 ================
# 手机号码正则表达式
PHONE_PATTERN = r'1[3-9]\d{9}'

# 身份证号码正则表达式
ID_PATTERN = r'[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]'

# 车牌号正则表达式
PLATE_PATTERN = r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]'

# 数据正则表达式（匹配非中文的内容）
DATA_PATTERN = r'[^\u4e00-\u9fa5]+'

def clean_text(text, keep_chinese=False):
    """清理文本中的噪声数据
    Args:
        text: 要清理的文本
        keep_chinese: 是否保留中文字符
    """
    if pd.isna(text):
        return ''
    # 转换为字符串
    text = str(text)
    if keep_chinese:
        # 保留中文、字母和数字，去除其他字符
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
    else:
        # 移除中文字符和其他非字母数字字符
        text = re.sub(r'[\u4e00-\u9fa5]', '', text)
        text = ''.join(filter(str.isalnum, text))
    return text

def extract_phones(text, clean=True):
    """提取手机号码"""
    if clean:
        text = clean_text(text)
    phones = []
    matches = re.finditer(PHONE_PATTERN, text)
    for match in matches:
        phones.append(match.group())
    return list(set(phones))

def extract_ids(text, clean=True):
    """提取身份证号码"""
    if clean:
        text = clean_text(text)
    return list(set(re.findall(ID_PATTERN, text)))

def extract_plates(text, clean=True):
    """提取车牌号"""
    if clean:
        text = clean_text(text)
    return list(set(re.findall(PLATE_PATTERN, text)))

def extract_data(text, clean=True):
    """提取数据（字母、数字、空格、符号，但不包含中文）"""
    if pd.isna(text):
        return []
    
    # 转换为字符串
    text = str(text)
    
    if clean:
        # 移除中文字符、空格和符号，只保留字母和数字
        text = re.sub(r'[\u4e00-\u9fa5]', '', text)  # 移除中文字符
        text = ''.join(filter(str.isalnum, text))  # 只保留字母和数字
        if text:
            return [text]
    else:
        # 直接提取非中文内容
        matches = re.findall(DATA_PATTERN, text)
        # 过滤掉空白字符串
        return [match for match in matches if match.strip()]
    return []

def extract_full_content(text, clean=True):
    """提取完整内容"""
    if pd.isna(text):
        return []
    
    # 转换为字符串，但保留原始格式
    text = str(text)
    
    if clean:
        # 模糊提取：清理噪声数据后提取，但保留中文字符
        text = clean_text(text, keep_chinese=True)
        if text:
            return [text]
    else:
        # 精确提取：完全保留原始文本，包括空格和其他字符
        return [text] if text else []
    return []

# ================ 文件工具类 ================
def read_excel(file_path, sheet_name=None):
    """读取Excel文件内容"""
    try:
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            return {sheet_name: df}
        else:
            return pd.read_excel(file_path, sheet_name=None)
    except Exception as e:
        raise Exception(f"读取Excel文件失败: {str(e)}")

def read_csv(file_path):
    """读取CSV文件内容"""
    try:
        # 尝试不同的编码方式
        encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
        
        # 尝试每种编码
        for encoding in encodings:
            try:
                # 设置pandas选项，避免可能的兼容性问题
                pd.set_option('mode.chained_assignment', None)
                
                # 读取CSV文件，将所有列作为字符串处理
                df = pd.read_csv(file_path, encoding=encoding, dtype=str)
                
                # 处理空值
                df = df.fillna('')
                
                # 统一数据类型为字符串
                for col in df.columns:
                    df[col] = df[col].astype(str)
                
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用编码 {encoding} 读取失败: {str(e)}")
                continue
        
        # 如果所有编码都失败，尝试使用默认编码
        return pd.read_csv(file_path, dtype=str).fillna('')
    except Exception as e:
        raise Exception(f"读取CSV文件失败: {str(e)}")

def read_txt(file_path):
    """读取文本文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        with open(file_path, 'r', encoding='gbk') as f:
            return f.read()
    except Exception as e:
        raise Exception(f"读取文本文件失败: {str(e)}")

def read_word(file_path):
    """读取Word文件内容"""
    try:
        doc = Document(file_path)
        return '\n'.join([paragraph.text for paragraph in doc.paragraphs])
    except Exception as e:
        raise Exception(f"读取Word文件失败: {str(e)}")

def get_file_content(file_path, sheet_name=None):
    """根据文件类型读取内容"""
    ext = os.path.splitext(file_path)[1].lower()
    
    try:
        if ext in ['.xls', '.xlsx']:
            return read_excel(file_path, sheet_name)
        elif ext == '.csv':
            # 对于CSV文件，返回与Excel文件相同的格式
            df = read_csv(file_path)
            if sheet_name:
                return {sheet_name: df}
            else:
                return {'Sheet1': df}  # 将CSV数据放在名为'Sheet1'的工作表中
        elif ext == '.txt':
            return read_txt(file_path)
        elif ext in ['.doc', '.docx']:
            return read_word(file_path)
        else:
            raise Exception(f"不支持的文件类型: {ext}")
    except Exception as e:
        raise Exception(f"读取文件失败: {str(e)}")

def get_excel_sheets(file_path):
    """获取Excel文件中的所有工作表名称"""
    try:
        return pd.ExcelFile(file_path).sheet_names
    except Exception as e:
        raise Exception(f"获取Excel工作表失败: {str(e)}")

def export_to_excel(data, output_path):
    """导出数据到Excel文件"""
    try:
        if isinstance(data, dict):
            with pd.ExcelWriter(output_path) as writer:
                for sheet_name, df in data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
        else:
            data.to_excel(output_path, index=False)
    except Exception as e:
        raise Exception(f"导出Excel文件失败: {str(e)}")

def is_excel_file(file_path):
    """判断是否为Excel文件"""
    ext = os.path.splitext(file_path)[1].lower()
    return ext in ['.xls', '.xlsx']

def is_csv_file(file_path):
    """判断是否为CSV文件"""
    ext = os.path.splitext(file_path)[1].lower()
    return ext == '.csv'

# ================ 数据提取器 ================
class DataExtractor:
    def __init__(self):
        self.extraction_functions = {
            'phone': extract_phones,
            'id': extract_ids,
            'plate': extract_plates,
            'data': extract_data,
            'full': extract_full_content
        }
    
    def extract_from_text(self, text, data_types, clean=True):
        """从文本中提取数据"""
        results = {}
        for data_type in data_types:
            if data_type in self.extraction_functions:
                # 对于完整内容，保留中文字符
                if data_type == 'full':
                    results[data_type] = self.extraction_functions[data_type](text, clean)
                else:
                    results[data_type] = self.extraction_functions[data_type](text, clean)
        return results
    
    def extract_from_file(self, file_path, data_types, sheet_name=None, column=None, example=None, dedup=True, clean=True):
        """从文件中提取数据"""
        try:
            content = get_file_content(file_path, sheet_name)
            
            if isinstance(content, dict):  # Excel文件多个工作表
                results = {}
                for sheet, df in content.items():
                    if column is not None:
                        col_idx = column - 1
                        if col_idx < len(df.columns):
                            texts = []
                            
                            # 先处理示例文本（如果有的话）
                            example_results = {data_type: [] for data_type in data_types}
                            if example:
                                example_cell_results = self.extract_from_text(example, data_types, clean)
                                for data_type in data_types:
                                    extracted = example_cell_results.get(data_type, [])
                                    if extracted:
                                        example_results[data_type].extend(extracted)
                            
                            # 收集所有非空值
                            for idx, value in enumerate(df.iloc[:, col_idx]):
                                if pd.notna(value):
                                    # 在直接提取模式下，完全保留原始文本
                                    text = str(value)
                                    if text and text != example:  # 不重复处理示例文本
                                        texts.append(text)
                            
                            # 分别从每个单元格提取数据
                            all_results = {data_type: example_results[data_type].copy() for data_type in data_types}  # 从示例结果开始
                            for text in texts:
                                cell_results = self.extract_from_text(text, data_types, clean)
                                for data_type in data_types:
                                    extracted = cell_results.get(data_type, [])
                                    if extracted:
                                        all_results[data_type].extend(extracted)
                            
                            # 根据去重选项处理结果
                            for data_type in data_types:
                                if dedup:
                                    all_results[data_type] = list(set(all_results[data_type]))
                            results[sheet] = all_results
                        else:
                            results[sheet] = {data_type: [] for data_type in data_types}
                    else:
                        texts = []
                        for value in df.values.flatten():
                            if pd.notna(value):
                                # 在直接提取模式下，完全保留原始文本
                                text = str(value)
                                if text:
                                    texts.append(text)
                        text = ' '.join(texts)
                        results[sheet] = self.extract_from_text(text, data_types, clean)
                        if dedup:
                            for data_type in data_types:
                                if data_type in results[sheet]:
                                    results[sheet][data_type] = list(set(results[sheet][data_type]))
                return results
            
            elif isinstance(content, pd.DataFrame):  # CSV文件
                if column is not None:
                    col_idx = column - 1
                    if col_idx < len(content.columns):
                        texts = []
                        
                        # 先处理示例文本（如果有的话）
                        example_results = {data_type: [] for data_type in data_types}
                        if example:
                            example_cell_results = self.extract_from_text(example, data_types, clean)
                            for data_type in data_types:
                                extracted = example_cell_results.get(data_type, [])
                                if extracted:
                                    example_results[data_type].extend(extracted)
                        
                        # 收集所有非空值
                        for idx, value in enumerate(content.iloc[:, col_idx]):
                            if pd.notna(value):
                                # 在直接提取模式下，完全保留原始文本
                                text = str(value)
                                if text and text != example:  # 不重复处理示例文本
                                    texts.append(text)
                        
                        # 分别从每个单元格提取数据
                        all_results = {data_type: example_results[data_type].copy() for data_type in data_types}  # 从示例结果开始
                        for text in texts:
                            cell_results = self.extract_from_text(text, data_types, clean)
                            for data_type in data_types:
                                extracted = cell_results.get(data_type, [])
                                if extracted:
                                    all_results[data_type].extend(extracted)
                        
                        # 根据去重选项处理结果
                        for data_type in data_types:
                            if dedup:
                                all_results[data_type] = list(set(all_results[data_type]))
                        return all_results
                    else:
                        return {data_type: [] for data_type in data_types}
                
                # 如果没有指定列，处理所有列
                texts = []
                for col in content.columns:
                    for value in content[col]:
                        if pd.notna(value):
                            # 在直接提取模式下，完全保留原始文本
                            text = str(value)
                            if text:
                                texts.append(text)
                text = ' '.join(texts)
                results = self.extract_from_text(text, data_types, clean)
                if dedup:
                    for data_type in data_types:
                        if data_type in results:
                            results[data_type] = list(set(results[data_type]))
                return results
            
            else:  # 文本文件
                results = self.extract_from_text(content, data_types, clean)
                if dedup:
                    for data_type in data_types:
                        if data_type in results:
                            results[data_type] = list(set(results[data_type]))
                return results
        except Exception as e:
            return {data_type: [] for data_type in data_types}
    
    def extract_from_files(self, file_paths, data_types, sheet_name=None, column=None, dedup=True):
        """从多个文件中提取数据"""
        results = {}
        for file_path in file_paths:
            try:
                results[file_path] = self.extract_from_file(
                    file_path, data_types, sheet_name, column, dedup=dedup
                )
            except Exception as e:
                results[file_path] = {'error': str(e)}
        return results

# ================ 数据匹配器 ================
class DataMatcher:
    def __init__(self):
        pass
    
    def _flatten_extracted_data(self, data, data_type, dedup=True):
        """将提取的数据转换为一个集合或列表"""
        result = set() if dedup else []
        
        if isinstance(data, dict):
            for sheet_data in data.values():
                if isinstance(sheet_data, dict) and data_type in sheet_data:
                    if dedup:
                        result.update(sheet_data[data_type])
                    else:
                        result.extend(sheet_data[data_type])
        elif data_type in data:
            if dedup:
                result.update(data[data_type])
            else:
                result.extend(data[data_type])
            
        return result
    
    def match_data(self, source_data, target_data, dedup=True):
        """对比源数据和目标数据"""
        results = {}
        
        # 获取所有数据类型
        data_types = set()
        for file_data in source_data.values():
            if isinstance(file_data, dict):
                if 'error' in file_data:
                    continue
                if isinstance(list(file_data.values())[0], dict):
                    for sheet_data in file_data.values():
                        data_types.update(sheet_data.keys())
                else:
                    data_types.update(file_data.keys())
        
        # 对每种数据类型进行匹配
        for data_type in data_types:
            results[data_type] = {}
            
            for source_file, source_file_data in source_data.items():
                if isinstance(source_file_data, dict) and 'error' not in source_file_data:
                    # 获取源数据
                    source_values = self._flatten_extracted_data(source_file_data, data_type, dedup)
                    
                    # 获取目标数据（合并所有目标文件的数据）
                    target_values = set() if dedup else []
                    for target_file_data in target_data.values():
                        if isinstance(target_file_data, dict) and 'error' not in target_file_data:
                            file_values = self._flatten_extracted_data(target_file_data, data_type, dedup)
                            if dedup:
                                target_values.update(file_values)
                            else:
                                target_values.extend(file_values)
                    
                    # 进行匹配
                    matched_values = []
                    if dedup:
                        # 去重模式：使用精确匹配（完全相等）
                        source_set = set(str(x) for x in source_values)  # 不再使用strip()
                        target_set = set(str(x) for x in target_values)  # 不再使用strip()
                        matched_values = list(source_set & target_set)  # 使用集合的交集操作
                    else:
                        # 不去重模式：使用精确匹配（完全相等）并保留重复项
                        source_list = [str(x) for x in source_values]  # 不再使用strip()
                        target_list = [str(x) for x in target_values]  # 不再使用strip()
                        for value in source_list:
                            if value in target_list:  # 完全相等比
                                matched_values.append(value)
                    
                    # 记录结果
                    results[data_type][source_file] = {
                        'matched_count': len(matched_values),
                        'matched_values': matched_values,
                        'source_total': len(source_values),
                        'target_total': len(target_values)
                    }
        
        return results
    
    def generate_statistics(self, match_results):
        """生成统计报告"""
        stats = []
        
        for data_type, type_results in match_results.items():
            for source_file, result in type_results.items():
                stats.append({
                    '文件名': source_file,
                    '数据类型': data_type,
                    '待匹配文件数量': result['source_total'],
                    '目标文件数量': result['target_total'],
                    '匹配数量': result['matched_count'],
                    '匹配率': f"{(result['matched_count'] / result['source_total'] * 100):.2f}%" if result['source_total'] > 0 else "0%"
                })
        
        return pd.DataFrame(stats)

# ================ 工作线程 ================
class WorkerThread(QThread):
    """工作线程类用于处理耗时操作"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, task_type, *args, **kwargs):
        super().__init__()
        self.task_type = task_type
        self.args = args
        self.kwargs = kwargs
        
    def run(self):
        try:
            if self.task_type == 'extract_all':
                # 提取所有文件的数据
                source_files = self.args[0]
                target_files = self.args[1]
                source_settings = self.args[2]
                target_settings = self.args[3]
                data_types = self.args[4]
                source_dedup = self.args[5]
                target_dedup = self.args[6]
                clean_extract = self.args[7]
                
                extractor = DataExtractor()
                total_files = len(source_files) + len(target_files)
                processed_files = 0
                
                # 提取待匹配文件数据
                source_results = {}
                for file_path in source_files:
                    try:
                        settings = source_settings[file_path]
                        source_results[file_path] = extractor.extract_from_file(
                            file_path,
                            data_types,
                            sheet_name=settings['sheet'],
                            column=settings['column'],
                            example=settings['example'],
                            dedup=source_dedup,
                            clean=clean_extract
                        )
                    except Exception as e:
                        source_results[file_path] = {'error': str(e)}
                    
                    processed_files += 1
                    self.progress.emit(int(processed_files / total_files * 100))
                
                # 提取目标文件数据
                target_results = {}
                for file_path in target_files:
                    try:
                        settings = target_settings[file_path]
                        target_results[file_path] = extractor.extract_from_file(
                            file_path,
                            data_types,
                            sheet_name=settings['sheet'],
                            column=settings['column'],
                            example=settings['example'],
                            dedup=target_dedup,
                            clean=clean_extract
                        )
                    except Exception as e:
                        target_results[file_path] = {'error': str(e)}
                    
                    processed_files += 1
                    self.progress.emit(int(processed_files / total_files * 100))
                
                self.finished.emit({
                    'source': source_results,
                    'target': target_results
                })
                
            elif self.task_type == 'match_all':
                # 对所有待匹配文件和目标文件进行匹配
                source_data = self.args[0]
                target_data = self.args[1]
                dedup = self.kwargs.get('dedup', True)
                
                matcher = DataMatcher()
                all_results = {}
                
                total_combinations = len(source_data) * len(target_data)
                processed = 0
                
                for source_file, source_file_data in source_data.items():
                    if isinstance(source_file_data, dict) and 'error' not in source_file_data:
                        file_results = {}
                        for target_file, target_file_data in target_data.items():
                            if isinstance(target_file_data, dict) and 'error' not in target_file_data:
                                # 对每个待匹配文件和目标文件进行匹配
                                match_result = matcher.match_data(
                                    {source_file: source_file_data},
                                    {target_file: target_file_data},
                                    dedup=dedup
                                )
                                file_results[target_file] = match_result
                            
                            processed += 1
                            self.progress.emit(int(processed / total_combinations * 100))
                        
                        all_results[source_file] = file_results
                
                self.finished.emit(all_results)
            
        except Exception as e:
            self.error.emit(str(e))

# ================ 主窗口 ================
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("数据提取碰撞统计工具")
        self.setMinimumSize(1200, 1000)
        
        # 初始化数据结构
        self.source_files = []  # 待匹配文件列表
        self.source_file_settings = {}  # 存储每个文件的设置
        self.target_files = []  # 目标文件列表
        self.target_file_settings = {}  # 存储目标文件的设置
        self.extracted_data = {}  # 存储提取的数据
        self.match_results = {}  # 存储匹配结果
        
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f6fa;
            }
            QGroupBox {
                border: 2px solid #dcdde1;
                border-radius: 6px;
                margin-top: 12px;
                font-weight: bold;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #2f3542;
            }
            QPushButton {
                background-color: #0984e3;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #00a8ff;
            }
            QPushButton:pressed {
                background-color: #0652DD;
            }
            QPushButton:disabled {
                background-color: #b2bec3;
            }
            QTableWidget {
                border: 1px solid #dcdde1;
                border-radius: 4px;
                background-color: white;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #f1f2f6;
                padding: 5px;
                border: none;
                border-right: 1px solid #dcdde1;
                border-bottom: 1px solid #dcdde1;
                font-weight: bold;
            }
            QProgressBar {
                border: 1px solid #dcdde1;
                border-radius: 4px;
                text-align: center;
                background-color: white;
            }
            QProgressBar::chunk {
                background-color: #0984e3;
                border-radius: 3px;
            }
            QCheckBox {
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QRadioButton {
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
            QComboBox {
                border: 1px solid #dcdde1;
                border-radius: 4px;
                padding: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
            QLabel {
                color: #2f3542;
            }
        """)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)  # 设置组件之间的间距
        main_layout.setContentsMargins(15, 15, 15, 15)  # 设置边距
        
        # 创建文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()
        file_group.setFixedHeight(400)  # 固定文件选择区域的高度
        
        # 待匹配文件区域
        source_group = QGroupBox("待匹配文件")
        source_layout = QVBoxLayout()
        
        # 待匹配文件工具栏
        source_toolbar = QHBoxLayout()
        self.source_add_btn = QPushButton("添加待匹配文件")
        self.source_clear_btn = QPushButton("清空待匹配文件")
        source_toolbar.addWidget(self.source_add_btn)
        source_toolbar.addWidget(self.source_clear_btn)
        source_toolbar.addStretch()
        
        # 待匹配文件表格
        self.source_table = QTableWidget()
        self.source_table.setColumnCount(4)
        self.source_table.setHorizontalHeaderLabels(['文件名', '工作表', '字段示例', '操作'])
        self.source_table.setColumnWidth(0, 180)  # 文件名列
        self.source_table.setColumnWidth(1, 100)  # 工作表列
        self.source_table.setColumnWidth(2, 120)  # 字段示例列
        self.source_table.setColumnWidth(3, 120)   # 操作列
        
        source_layout.addLayout(source_toolbar)
        source_layout.addWidget(self.source_table)
        source_group.setLayout(source_layout)
        
        # 目标文件区域
        target_group = QGroupBox("目标文件")
        target_layout = QVBoxLayout()
        
        # 目标文件工具栏
        target_toolbar = QHBoxLayout()
        self.target_add_btn = QPushButton("添加目标文件")
        self.target_clear_btn = QPushButton("清空目标文件")
        target_toolbar.addWidget(self.target_add_btn)
        target_toolbar.addWidget(self.target_clear_btn)
        target_toolbar.addStretch()
        
        # 目标文件表格
        self.target_table = QTableWidget()
        self.target_table.setColumnCount(4)
        self.target_table.setHorizontalHeaderLabels(['文件名', '工作表', '字段示例', '操作'])
        self.target_table.setColumnWidth(0, 180)  # 文件名列
        self.target_table.setColumnWidth(1, 100)  # 工作表列
        self.target_table.setColumnWidth(2, 120)  # 字段示例列
        self.target_table.setColumnWidth(3, 120)   # 操作列
        
        target_layout.addLayout(target_toolbar)
        target_layout.addWidget(self.target_table)
        target_group.setLayout(target_layout)
        
        file_layout.addWidget(source_group)
        file_layout.addWidget(target_group)
        file_group.setLayout(file_layout)
        
        # 创建提取设置区域
        extract_group = QGroupBox("提取设置")
        extract_layout = QVBoxLayout()
        extract_group.setFixedHeight(150)  # 固定提取设置区域的高度
        
        # 提取类型选择
        type_layout = QHBoxLayout()
        self.phone_check = QCheckBox("手机号码")
        self.id_check = QCheckBox("身份证号码")
        self.plate_check = QCheckBox("车牌号")
        self.data_check = QCheckBox("数据（包含字母数字，不包含中文）")  # 添加数据选项
        self.full_check = QCheckBox("完整内容")
        type_layout.addWidget(self.phone_check)
        type_layout.addWidget(self.id_check)
        type_layout.addWidget(self.plate_check)
        type_layout.addWidget(self.data_check)  # 添加到布局
        type_layout.addWidget(self.full_check)
        type_layout.addStretch()
        
        # 提取方式选择
        extract_method_layout = QHBoxLayout()
        extract_method_label = QLabel("提取方式:")
        self.direct_extract_radio = QRadioButton("直接提取")
        self.clean_extract_radio = QRadioButton("噪声数据处理后提取")
        self.direct_extract_radio.setChecked(True)  # 默认选择直接提取
        extract_method_layout.addWidget(extract_method_label)
        extract_method_layout.addWidget(self.direct_extract_radio)
        extract_method_layout.addWidget(self.clean_extract_radio)
        extract_method_layout.addStretch()
        
        # 去重选项
        dedup_layout = QHBoxLayout()
        self.source_dedup_check = QCheckBox("待匹配文件去重")
        self.target_dedup_check = QCheckBox("目标文件去重")
        self.match_dedup_check = QCheckBox("碰撞去重")
        self.source_dedup_check.setChecked(True)
        self.target_dedup_check.setChecked(True)
        self.match_dedup_check.setChecked(True)
        dedup_layout.addWidget(self.source_dedup_check)
        dedup_layout.addWidget(self.target_dedup_check)
        dedup_layout.addWidget(self.match_dedup_check)
        dedup_layout.addStretch()
        
        extract_layout.addLayout(type_layout)
        extract_layout.addLayout(extract_method_layout)
        extract_layout.addLayout(dedup_layout)
        extract_group.setLayout(extract_layout)
        
        # 创建操作按钮区域
        button_layout = QHBoxLayout()
        self.extract_btn = QPushButton("开始提取")
        self.match_btn = QPushButton("开始碰撞")
        self.export_extract_btn = QPushButton("导出提取结果")  # 新增导出提取结果按钮
        self.export_match_btn = QPushButton("导出碰撞结果")  # 重命名为导出碰撞结果
        button_layout.addWidget(self.extract_btn)
        button_layout.addWidget(self.match_btn)
        button_layout.addWidget(self.export_extract_btn)
        button_layout.addWidget(self.export_match_btn)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        
        # 创建结果显示表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(7)
        self.result_table.setHorizontalHeaderLabels([
            '待匹配文件', '目标文件', '数据类型', 
            '待匹配文件数量', '目标文件数量', '匹配数量', '匹配率'
        ])
        self.result_table.setColumnWidth(0, 200)  # 待匹配文件列
        self.result_table.setColumnWidth(1, 200)  # 目标文件列
        self.result_table.setColumnWidth(2, 100)  # 数据类型列
        self.result_table.setColumnWidth(3, 120)  # 待匹配文件数量列
        self.result_table.setColumnWidth(4, 120)  # 目标文件数量列
        self.result_table.setColumnWidth(5, 100)  # 匹配数量列
        self.result_table.setColumnWidth(6, 60)   # 匹配率列
        
        # 创建版权信息标签
        copyright_label = QLabel("版权所有：台州市公安局 解晟")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: #000000;
                padding: 10px;
                font-size: 14px;
            }
        """)
        
        # 添加所有组件到主布局
        main_layout.addWidget(file_group, 0)  # 不拉伸
        main_layout.addWidget(extract_group, 0)  # 不拉伸
        main_layout.addLayout(button_layout, 0)  # 不拉伸
        main_layout.addWidget(self.progress_bar, 0)  # 不拉伸
        main_layout.addWidget(self.result_table, 1)  # 设置拉伸因子为1，允许结果表格区域扩展
        main_layout.addWidget(copyright_label, 0)  # 不拉伸
        
        # 连接信号和槽
        self.setup_connections()
        
        # 初始化UI状态
        self.init_ui_state()
        
        # 设置表格的全局样式
        table_style = """
            QTableWidget {
                border: 1px solid #dcdde1;
                border-radius: 4px;
                background-color: white;
            }
            QTableWidget::item {
                padding: 2px 5px;
                min-height: 15px;
            }
            QHeaderView::section {
                background-color: #f1f2f6;
                padding: 5px;
                border: none;
                border-right: 1px solid #dcdde1;
                border-bottom: 1px solid #dcdde1;
                font-weight: bold;
                min-height: 15px;
            }
            QTableWidget QComboBox {
                min-height: 15px;
                padding: 2px 5px;
            }
            QTableWidget QPushButton {
                min-height: 15px;
                margin: 2px 5px;
            }
        """
        
        self.source_table.setStyleSheet(table_style)
        self.target_table.setStyleSheet(table_style)
        self.result_table.setStyleSheet(table_style)
        
        # 设置表格的默认行高
        self.source_table.verticalHeader().setDefaultSectionSize(40)
        self.target_table.verticalHeader().setDefaultSectionSize(40)
        self.result_table.verticalHeader().setDefaultSectionSize(40)
        
        # 设置表格的选择行为
        self.source_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.target_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.result_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # 设置结果表格的最小高度
        self.result_table.setMinimumHeight(300)  # 设置结果表格的最小高度
    
    def setup_connections(self):
        """设置信号和槽的连接"""
        self.source_add_btn.clicked.connect(self.select_source_files)
        self.source_clear_btn.clicked.connect(self.clear_source_files)
        self.target_add_btn.clicked.connect(self.select_target_files)
        self.target_clear_btn.clicked.connect(self.clear_target_files)
        self.extract_btn.clicked.connect(self.start_extract)
        self.match_btn.clicked.connect(self.start_match)
        self.export_extract_btn.clicked.connect(self.show_extract_export_dialog)  # 连接导出提取结果按钮
        self.export_match_btn.clicked.connect(self.show_match_export_dialog)  # 连接导出碰撞结果按钮
    
    def init_ui_state(self):
        """初始化UI状态"""
        self.match_btn.setEnabled(False)
        self.export_extract_btn.setEnabled(False)  # 初始禁用导出提取结果按钮
        self.export_match_btn.setEnabled(False)  # 初始禁用导出碰撞结果按钮
        self.progress_bar.setValue(0)
    
    def select_source_files(self):
        """选择待匹配文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择待匹配文件",
            "",
            "支持的文件 (*.xls *.xlsx *.csv *.doc *.docx *.txt)"
        )
        if files:
            for file in files:
                if file not in self.source_files:
                    self.source_files.append(file)
                    self.add_file_to_table(file, self.source_table)
    
    def select_target_files(self):
        """选择目标文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择目标文件",
            "",
            "支持的文件 (*.xls *.xlsx *.csv *.doc *.docx *.txt)"
        )
        if files:
            for file in files:
                if file not in self.target_files:
                    self.target_files.append(file)
                    self.add_file_to_table(file, self.target_table)
    
    def add_file_to_table(self, file_path, table):
        """将文件添加到表格中"""
        row = table.rowCount()
        table.insertRow(row)
        
        # 设置行高
        table.setRowHeight(row, 30)
        
        # 文件名
        file_name = os.path.basename(file_path)
        name_item = QTableWidgetItem(file_name)
        name_item.setData(Qt.UserRole, file_path)  # 存储完整文件路径
        table.setItem(row, 0, name_item)
        
        # 工作表选择
        sheet_combo = QComboBox()
        sheet_combo.setStyleSheet("""
            QComboBox {
                min-height: 15px;
                padding: 2px 5px;
                min-width: 100px;
            }
            QComboBox QAbstractItemView {
                min-height: 15px;
                min-width: 100px;
            }
        """)
        if is_excel_file(file_path):
            sheets = get_excel_sheets(file_path)
            sheet_combo.addItems(sheets)
        else:
            sheet_combo.addItem("Sheet1")
        table.setCellWidget(row, 1, sheet_combo)
        
        # 字段示例选择
        column_combo = QComboBox()
        column_combo.setStyleSheet("""
            QComboBox {
                min-height: 15px;
                padding: 2px 5px;
                min-width: 120px;
            }
            QComboBox QAbstractItemView {
                min-height: 22px;
                min-width: 120px;
            }
        """)
        self.update_column_combo(file_path, sheet_combo.currentText(), column_combo)
        table.setCellWidget(row, 2, column_combo)
        
        # 删除按钮
        delete_btn = QPushButton("删除")
        delete_btn.setStyleSheet("""
            QPushButton {
                min-height: 15px;
                margin: 2px 5px;
                min-width: 60px;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_file_by_path(file_path, table))
        table.setCellWidget(row, 3, delete_btn)
        
        # 连接工作表选择变更事件
        sheet_combo.currentTextChanged.connect(
            lambda: self.update_column_combo(file_path, sheet_combo.currentText(), column_combo)
        )
        
        # 连接字段示例选择变更事件，选择后自动跳转到下一个
        column_combo.currentIndexChanged.connect(
            lambda: self.scroll_to_next_field(table)
        )
        
        # 设置列宽
        table.setColumnWidth(0, 200)  # 文件名列
        table.setColumnWidth(1, 120)  # 工作表列
        table.setColumnWidth(2, 150)  # 字段示例列
        table.setColumnWidth(3, 120)   # 操作列
    
    def scroll_to_next_field(self, table):
        """跳转到下一个字段示例"""
        current_row = table.currentRow()
        # 获取当前选中的列
        current_column = table.currentColumn()
        
        # 只有在选择字段示例（第3列）时才跳转
        if current_column == 2 and current_row < table.rowCount() - 1:  # 如果不是最后一行
            next_row = current_row + 1
            # 选中下一行
            table.setCurrentCell(next_row, 2)
            # 确保下一行可见
            table.scrollToItem(table.item(next_row, 0))
            # 获取下一行的字段示例下拉框并打开
            next_combo = table.cellWidget(next_row, 2)
            if next_combo:
                next_combo.showPopup()
    
    def remove_file_by_path(self, file_path, table):
        """通过文件路径删除文件"""
        # 查找文件所在行
        for row in range(table.rowCount()):
            item = table.item(row, 0)
            if item and item.data(Qt.UserRole) == file_path:
                # 从对应的文件列表中删除文件
                if table == self.source_table:
                    self.source_files = [f for f in self.source_files if f != file_path]
                else:
                    self.target_files = [f for f in self.target_files if f != file_path]
                # 从表格中删除该行
                table.removeRow(row)
                break
    
    def clear_source_files(self):
        """清空待匹配文件"""
        self.source_files = []
        self.source_table.setRowCount(0)
    
    def clear_target_files(self):
        """清空目标文件"""
        self.target_files = []
        self.target_table.setRowCount(0)
    
    def update_column_combo(self, file_path, sheet_name, combo):
        """更新列选择下拉框"""
        try:
            if is_excel_file(file_path):
                df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            elif is_csv_file(file_path):
                df = pd.read_csv(file_path, header=None)
            else:
                return

            combo.clear()
            if len(df) > 0:
                extractor = DataExtractor()
                data_types = ['phone', 'id', 'plate', 'full']  # 添加'full'类型
                
                for col_idx in range(len(df.columns)):
                    match_found = False
                    column_data = []
                    example_text = None
                    
                    # 收集列中的所有非空值
                    for row_idx in range(len(df)):
                        value = df.iloc[row_idx, col_idx]
                        if pd.notna(value):
                            text = str(value)
                            column_data.append(text)
                            results = extractor.extract_from_text(text, data_types)
                            if any(results.get(data_type, []) for data_type in data_types):
                                example_text = text
                                match_found = True
                                break
                
                    if match_found and example_text:
                        # 保存列索引和示例文本
                        combo.addItem(example_text, {'column': col_idx + 1, 'example': example_text})
                    elif column_data:
                        # 如果没有找到匹配项但有数据，用第一个非空值
                        combo.addItem(column_data[0], {'column': col_idx + 1, 'example': column_data[0]})
                    else:
                        # 如果列完全为空，使用列号
                        combo.addItem(f"列 {col_idx + 1}", {'column': col_idx + 1, 'example': None})
            
            # 确保至少有一个选项
            if combo.count() == 0:
                combo.addItem("列 1", {'column': 1, 'example': None})
            
        except Exception as e:
            print(f"更新列选择失败: {str(e)}")
            # 发生错误时添加默认选项
            combo.clear()
            combo.addItem("列 1", {'column': 1, 'example': None})

    def get_file_settings(self):
        """获取所有文件的设置"""
        self.source_file_settings = {}
        self.target_file_settings = {}
        
        # 获取源文件设置
        for row in range(self.source_table.rowCount()):
            file_name = self.source_table.item(row, 0).text()
            file_path = next(f for f in self.source_files if os.path.basename(f) == file_name)
            sheet_combo = self.source_table.cellWidget(row, 1)
            column_combo = self.source_table.cellWidget(row, 2)
            
            # 获取列索引和示例文本
            data = column_combo.currentData()
            if data is None:
                data = {'column': 1, 'example': None}
            
            self.source_file_settings[file_path] = {
                'sheet': sheet_combo.currentText(),
                'column': data['column'],
                'example': data['example']
            }
        
        # 获取目标文件设置
        for row in range(self.target_table.rowCount()):
            file_name = self.target_table.item(row, 0).text()
            file_path = next(f for f in self.target_files if os.path.basename(f) == file_name)
            sheet_combo = self.target_table.cellWidget(row, 1)
            column_combo = self.target_table.cellWidget(row, 2)
            
            # 获取列索引和示例文本
            data = column_combo.currentData()
            if data is None:
                data = {'column': 1, 'example': None}
            
            self.target_file_settings[file_path] = {
                'sheet': sheet_combo.currentText(),
                'column': data['column'],
                'example': data['example']
            }
    
    def start_extract(self):
        """开始提取数据"""
        if not self.source_files and not self.target_files:
            QMessageBox.warning(self, "警告", "请选择文件")
            return
            
        data_types = self.get_selected_data_types()
        if not data_types:
            QMessageBox.warning(self, "警告", "请少选择一种数据类型")
            return
        
        # 获取提取方式
        clean_extract = self.clean_extract_radio.isChecked()
        
        # 获取所有文件的设置
        self.get_file_settings()
        
        # 创建并启动工作线程
        self.worker = WorkerThread(
            'extract_all',
            self.source_files,
            self.target_files,
            self.source_file_settings,
            self.target_file_settings,
            data_types,
            self.source_dedup_check.isChecked(),
            self.target_dedup_check.isChecked(),
            clean_extract
        )
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.handle_extract_finished)
        self.worker.error.connect(self.handle_error)
        
        # 禁用按钮
        self.extract_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        
        self.worker.start()
    
    def start_match(self):
        """开始数据碰撞"""
        if not self.extracted_data:
            QMessageBox.warning(self, "警告", "请先提取据")
            return
        
        # 创建并启动工作线程
        self.worker = WorkerThread(
            'match_all',
            self.extracted_data['source'],
            self.extracted_data['target'],
            dedup=self.match_dedup_check.isChecked()
        )
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.handle_match_finished)
        self.worker.error.connect(self.handle_error)
        
        # 禁用按钮
        self.match_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        
        self.worker.start()
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def handle_extract_finished(self, result):
        """处理提取完成"""
        self.extracted_data = result
        self.extract_btn.setEnabled(True)
        self.match_btn.setEnabled(True)
        self.export_extract_btn.setEnabled(True)  # 提取完成后启用导出提取结果按钮
        self.progress_bar.setValue(100)
        
        # 显示提取结果统计
        source_total = sum(
            len(data.get(type_, [])) 
            for file_data in result['source'].values() 
            for type_ in self.get_selected_data_types()
            for data in ([file_data] if not isinstance(file_data, dict) else file_data.values())
            if isinstance(data, dict) and 'error' not in data
        )
        
        target_total = sum(
            len(data.get(type_, [])) 
            for file_data in result['target'].values() 
            for type_ in self.get_selected_data_types()
            for data in ([file_data] if not isinstance(file_data, dict) else file_data.values())
            if isinstance(data, dict) and 'error' not in data
        )
        
        QMessageBox.information(
            self, 
            "完成", 
            f"数据提取完成\n待匹配文件共提取 {source_total} 条数据\n目标文件共提取 {target_total} 条数据"
        )
    
    def handle_match_finished(self, result):
        """处理碰撞完成"""
        self.match_results = result
        self.match_btn.setEnabled(True)
        self.export_match_btn.setEnabled(True)  # 碰撞完成后启用导出碰撞结果按钮
        self.progress_bar.setValue(100)
        
        # 显示匹配结果
        self.show_match_results()
    
    def handle_error(self, error_msg):
        """处理错误"""
        self.extract_btn.setEnabled(True)
        self.match_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        QMessageBox.critical(self, "错误", error_msg)
    
    def show_match_results(self):
        """在表格中显示匹配结果"""
        # 清空表格
        self.result_table.setRowCount(0)
        
        # 设置表格列
        self.result_table.setColumnCount(7)
        self.result_table.setHorizontalHeaderLabels([
            '待匹配文件', '目标文件', '数据类型', 
            '待匹配文件数量', '目标文件数量', '匹配数量', '匹配率'
        ])
        
        # 添加数据
        row = 0
        for source_file, target_results in self.match_results.items():
            for target_file, type_results in target_results.items():
                for data_type, result in type_results.items():
                    for file_result in result.values():
                        self.result_table.insertRow(row)
                        self.result_table.setItem(row, 0, QTableWidgetItem(os.path.basename(source_file)))
                        self.result_table.setItem(row, 1, QTableWidgetItem(os.path.basename(target_file)))
                        self.result_table.setItem(row, 2, QTableWidgetItem(data_type))
                        self.result_table.setItem(row, 3, QTableWidgetItem(str(file_result['source_total'])))
                        self.result_table.setItem(row, 4, QTableWidgetItem(str(file_result['target_total'])))
                        self.result_table.setItem(row, 5, QTableWidgetItem(str(file_result['matched_count'])))
                        match_rate = f"{(file_result['matched_count'] / file_result['source_total'] * 100):.2f}%" if file_result['source_total'] > 0 else "0%"
                        self.result_table.setItem(row, 6, QTableWidgetItem(match_rate))
                        row += 1
        
        # 调整列宽
        self.result_table.resizeColumnsToContents()
    
    def show_extract_export_dialog(self):
        """显示提取结果导出对话框"""
        if not self.extracted_data:
            QMessageBox.warning(self, "警告", "没有可导出的提取结果")
            return
            
        # 创建导出选项对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择导出方式")
        dialog.setMinimumWidth(300)
        
        layout = QVBoxLayout()
        
        # 添加导出选项
        extract_source_btn = QPushButton("导出待匹配文件提取结果")
        extract_target_btn = QPushButton("导出目标文件提取结果")
        
        layout.addWidget(extract_source_btn)
        layout.addWidget(extract_target_btn)
        
        dialog.setLayout(layout)
        
        # 连接按钮事件
        extract_source_btn.clicked.connect(lambda: self.export_extracted_data('source', dialog))
        extract_target_btn.clicked.connect(lambda: self.export_extracted_data('target', dialog))
        
        dialog.exec_()
        
    def show_match_export_dialog(self):
        """显示碰撞结果导出对话框"""
        if not self.match_results:
            QMessageBox.warning(self, "警告", "没有可导出的碰撞结果")
            return
            
        # 创建导出选项对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择导出方式")
        dialog.setMinimumWidth(300)
        
        layout = QVBoxLayout()
        
        # 添加导出选项
        stats_btn = QPushButton("导出统计结果")
        detail_btn = QPushButton("导出详细结果")
        
        layout.addWidget(stats_btn)
        layout.addWidget(detail_btn)
        
        dialog.setLayout(layout)
        
        # 连接按钮事件
        stats_btn.clicked.connect(lambda: self.export_statistics(dialog))
        detail_btn.clicked.connect(lambda: self.export_details(dialog))
        
        dialog.exec_()
    
    def export_statistics(self, dialog):
        """导出统计结果"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出统计结果",
            "",
            "Excel文件 (*.xlsx)"
        )
        
        if file_path:
            try:
                # 准备导出数据
                export_data = []
                for source_file, target_results in self.match_results.items():
                    for target_file, type_results in target_results.items():
                        for data_type, result in type_results.items():
                            for file_result in result.values():
                                match_rate = f"{(file_result['matched_count'] / file_result['source_total'] * 100):.2f}%" if file_result['source_total'] > 0 else "0%"
                                export_data.append({
                                    '待匹配文件': os.path.basename(source_file),
                                    '目标文件': os.path.basename(target_file),
                                    '数据类型': data_type,
                                    '待匹配文件数量': file_result['source_total'],
                                    '目标文件数量': file_result['target_total'],
                                    '匹配数量': file_result['matched_count'],
                                    '匹配率': match_rate
                                })
                
                # 创建DataFrame并导出
                df = pd.DataFrame(export_data)
                export_to_excel(df, file_path)
                QMessageBox.information(self, "完成", "统计结果导出成功")
                dialog.close()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def export_details(self, dialog):
        """导出详细结果"""
        # 选择导出目录
        export_dir = QFileDialog.getExistingDirectory(
            self,
            "选择导出目录",
            ""
        )
        
        if not export_dir:
            return
        
        try:
            # 为每个待匹配文件创建一个Excel文件
            for source_file, target_results in self.match_results.items():
                source_name = os.path.splitext(os.path.basename(source_file))[0]
                export_path = os.path.join(export_dir, f"{source_name}_碰撞结果.xlsx")
                
                # 准备每个目标文件的数据
                all_sheets_data = {}
                for target_file, type_results in target_results.items():
                    target_name = os.path.basename(target_file)
                    
                    # 收集所有数据类型的结果
                    data_type_results = {}
                    for data_type, result in type_results.items():
                        for file_result in result.values():
                            # 根据数据类型分类匹配结果
                            if data_type == 'phone':
                                type_name = '手机号码'
                            elif data_type == 'id':
                                type_name = '身份证号码'
                            elif data_type == 'data':
                                type_name = '数据'
                            elif data_type == 'full':
                                type_name = '完整内容'
                            else:
                                type_name = '车牌号'
                            
                            data_type_results[type_name] = {
                                '待匹配文件数量': file_result['source_total'],
                                '目标文件数量': file_result['target_total'],
                                '匹配数量': file_result['matched_count'],
                                '匹配率': f"{(file_result['matched_count'] / file_result['source_total'] * 100):.2f}%" if file_result['source_total'] > 0 else "0%",
                                '匹配内容': file_result['matched_values']
                            }
                    
                    # 创建目标文件的工作表数据
                    export_data = []
                    for type_name, result in data_type_results.items():
                        # 添加统计信息
                        export_data.append({
                            '数据类型': type_name,
                            '待匹配文件数量': result['待匹配文件数量'],
                            '目标文件数量': result['目标文件数量'],
                            '匹配数量': result['匹配数量'],
                            '匹配率': result['匹配率']
                        })
                        # 添加空行
                        export_data.append({
                            '数据类型': '',
                            '待匹配文件数量': '',
                            '目标文件数量': '',
                            '匹配数量': '',
                            '匹配率': ''
                        })
                        # 添加匹配内容
                        for idx, value in enumerate(result['匹配内容'], 1):
                            export_data.append({
                                '数据类型': f'匹配内容 {idx}',
                                '待匹配文件数量': value,
                                '目标文件数量': '',
                                '匹配数量': '',
                                '匹配率': ''
                            })
                        # 添加空行
                        export_data.append({
                            '数据类型': '',
                            '待匹配文件数量': '',
                            '目标文件数量': '',
                            '匹配数量': '',
                            '匹配率': ''
                        })
                    
                    # 创建DataFrame
                    df = pd.DataFrame(export_data)
                    all_sheets_data[target_name] = df
                
                # 导出到Excel文件，每个目标文件一个工作表
                with pd.ExcelWriter(export_path) as writer:
                    for sheet_name, df in all_sheets_data.items():
                        # 处理工作表名称长度限制
                        sheet_name = sheet_name[:31]  # Excel工作表名称最大长度为31
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                        
                        # 获取工作表对象以设置格式
                        worksheet = writer.sheets[sheet_name]
                        
                        # 调整列宽
                        for idx, col in enumerate(df.columns):
                            # 计算最大长度
                            col_max = df[col].astype(str).apply(len).max()
                            col_len = len(str(col))
                            max_length = max(col_max, col_len)
                            # 设置列宽
                            worksheet.column_dimensions[chr(65 + idx)].width = max_length + 2
                
            QMessageBox.information(self, "完成", "详细结果导出成功")
            dialog.close()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
            print(f"导出详细结果时出错: {str(e)}")  # 添加错误日志
    
    def get_selected_data_types(self):
        """获取选中的数据类型"""
        data_types = []
        if self.phone_check.isChecked():
            data_types.append('phone')
        if self.id_check.isChecked():
            data_types.append('id')
        if self.plate_check.isChecked():
            data_types.append('plate')
        if self.data_check.isChecked():  # 添加数据选项的检查
            data_types.append('data')
        if self.full_check.isChecked():
            data_types.append('full')
        return data_types

    def export_extracted_data(self, data_type, dialog):
        """导出提取结果
        Args:
            data_type: 'source' 或 'target'，表示导出源文件还是目标文件的提取结果
            dialog: 对话框实例
        """
        if not self.extracted_data or data_type not in self.extracted_data:
            QMessageBox.warning(self, "警告", "没有可导出的提取结果")
            return
        
        # 选择导出目录
        export_dir = QFileDialog.getExistingDirectory(
            self,
            "选择导出目录",
            ""
        )
        
        if not export_dir:
            return
            
        try:
            # 获取要导出的数据
            extracted_files = self.extracted_data[data_type]
            
            # 为每个文件创建一个Excel文件
            for file_path, file_data in extracted_files.items():
                if isinstance(file_data, dict) and 'error' not in file_data:
                    file_name = os.path.splitext(os.path.basename(file_path))[0]
                    export_path = os.path.join(export_dir, f"{file_name}_提取结果.xlsx")
                    
                    # 准备每个工作表的数据
                    all_sheets_data = {}
                    
                    # 如果是Excel文件的多个工作表数据
                    if isinstance(list(file_data.values())[0], dict):
                        for sheet_name, sheet_data in file_data.items():
                            export_data = []
                            for data_type, values in sheet_data.items():
                                # 添加数据类型标题
                                type_name = {
                                    'phone': '手机号码',
                                    'id': '身份证号码',
                                    'plate': '车牌号',
                                    'data': '数据',
                                    'full': '完整内容'
                                }.get(data_type, data_type)
                                
                                export_data.append({
                                    '数据类型': type_name,
                                    '提取内容': '',
                                    '序号': ''
                                })
                                
                                # 添加提取的内容
                                for idx, value in enumerate(values, 1):
                                    export_data.append({
                                        '数据类型': '',
                                        '提取内容': value,
                                        '序号': idx
                                    })
                                
                                # 添加空行
                                export_data.append({
                                    '数据类型': '',
                                    '提取内容': '',
                                    '序号': ''
                                })
                            
                            # 创建DataFrame
                            df = pd.DataFrame(export_data)
                            all_sheets_data[sheet_name] = df
                    else:
                        # 单个工作表的数据
                        export_data = []
                        for data_type, values in file_data.items():
                            # 添加数据类型标题
                            type_name = {
                                'phone': '手机号码',
                                'id': '身份证号码',
                                'plate': '车牌号',
                                'data': '数据',
                                'full': '完整内容'
                            }.get(data_type, data_type)
                            
                            export_data.append({
                                '数据类型': type_name,
                                '提取内容': '',
                                '序号': ''
                            })
                            
                            # 添加提取的内容
                            for idx, value in enumerate(values, 1):
                                export_data.append({
                                    '数据类型': '',
                                    '提取内容': value,
                                    '序号': idx
                                })
                            
                            # 添加空行
                            export_data.append({
                                '数据类型': '',
                                '提取内容': '',
                                '序号': ''
                            })
                        
                        # 创建DataFrame
                        df = pd.DataFrame(export_data)
                        all_sheets_data['Sheet1'] = df
                    
                    # 导出到Excel文件
                    with pd.ExcelWriter(export_path) as writer:
                        for sheet_name, df in all_sheets_data.items():
                            # 处理工作表名称长度限制
                            sheet_name = sheet_name[:31]  # Excel工作表名称最大长度为31
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                            
                            # 获取工作表对象以设置格式
                            worksheet = writer.sheets[sheet_name]
                            
                            # 调整列宽
                            for idx, col in enumerate(df.columns):
                                # 计算最大长度
                                col_max = df[col].astype(str).apply(len).max()
                                col_len = len(str(col))
                                max_length = max(col_max, col_len)
                                # 设置列宽
                                worksheet.column_dimensions[chr(65 + idx)].width = max_length + 2
                
            QMessageBox.information(self, "完成", "提取结果导出成功")
            dialog.close()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
            print(f"导出提取结果时出错: {str(e)}")  # 添加错误日志

# ================ 程序入口 ================
if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
    sys.exit(app.exec_())
