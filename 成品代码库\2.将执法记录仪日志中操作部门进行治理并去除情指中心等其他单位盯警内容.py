import pandas as pd

# 读取Excel文件
file_path = '合并后的执法记录仪日志.xlsx'
df = pd.read_excel(file_path)

# 替换“操作部门”列中的值
replace_dict = {
    'cmpcs': '玉环楚门派出所',
    'gjpcs': '玉环干江派出所',
    'kmpcs': '玉环坎门派出所',
    'qgpcs': '玉环清港派出所',
    'smpcs': '玉环沙门派出所',
    'xcpcs': '玉环新城派出所',
    'ycpcs': '玉环玉城派出所',
    '陈家俊': '临海杜桥派出所',
    '峰江': '路桥峰江派出所',
    '洪家': '椒江洪家派出所',
    '前所': '椒江前所派出所',
    '桐屿': '路桥桐屿派出所',
    '叶子青': '三门沙柳派出所',
    '杨涛': '温岭石桥头派出所',
    '章安': '椒江章安派出所'
}

for key, value in replace_dict.items():
    df.loc[df['操作用户名'].str.contains(key, na=False), '操作部门'] = value

# 删除“操作用户名”列中含有“指”字的所有行
df = df[~df['操作用户名'].str.contains('指', na=False)]

# 过滤“操作部门”列中含有“派出所”的内容
df_filtered = df[df['操作部门'].str.contains('派出所', na=False)]

# 保存结果到新的Excel文件
output_file_path = '新的执法记录仪日志.xlsx'
df_filtered.to_excel(output_file_path, index=False)

print(f"处理完成，结果已保存到 {output_file_path}")