@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap');

:root {
    --neon-pink: #ff2a6d;
    --neon-blue: #05d9e8;
    --neon-purple: #d300c5;
    --cyber-yellow: #ffd319;
    --dark-bg: #0d0221;
    --grid-color: rgba(5, 217, 232, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--dark-bg);
    color: white;
    font-family: 'Orbitron', sans-serif;
    background-image: 
        linear-gradient(0deg, transparent 24%, var(--grid-color) 25%, var(--grid-color) 26%, transparent 27%, transparent 74%, var(--grid-color) 75%, var(--grid-color) 76%, transparent 77%, transparent),
        linear-gradient(90deg, transparent 24%, var(--grid-color) 25%, var(--grid-color) 26%, transparent 27%, transparent 74%, var(--grid-color) 75%, var(--grid-color) 76%, transparent 77%, transparent);
    background-size: 50px 50px;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    width: 90%;
    max-width: 800px;
    background-color: rgba(13, 2, 33, 0.8);
    border: 2px solid var(--neon-blue);
    border-radius: 5px;
    box-shadow: 0 0 20px var(--neon-blue), inset 0 0 10px var(--neon-blue);
    padding: 20px;
    text-align: center;
}

.game-header {
    margin-bottom: 20px;
}

.neon-text {
    color: var(--neon-pink);
    text-shadow: 
        0 0 5px var(--neon-pink),
        0 0 10px var(--neon-pink),
        0 0 20px var(--neon-pink),
        0 0 40px var(--neon-purple);
    letter-spacing: 2px;
    margin-bottom: 15px;
}

.score-container {
    font-size: 1.5rem;
    color: var(--cyber-yellow);
    text-shadow: 0 0 5px var(--cyber-yellow);
    margin-bottom: 10px;
}

#gameCanvas {
    background-color: rgba(0, 0, 0, 0.7);
    border: 2px solid var(--neon-pink);
    box-shadow: 0 0 10px var(--neon-pink), inset 0 0 5px var(--neon-pink);
    display: block;
    margin: 0 auto;
    image-rendering: pixelated;
}

.controls {
    margin-top: 20px;
}

.cyber-button {
    background-color: transparent;
    color: var(--neon-blue);
    border: 2px solid var(--neon-blue);
    padding: 10px 20px;
    font-family: 'Orbitron', sans-serif;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin: 10px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    box-shadow: 0 0 5px var(--neon-blue);
}

.cyber-button:hover {
    background-color: var(--neon-blue);
    color: var(--dark-bg);
    box-shadow: 0 0 15px var(--neon-blue);
}

.cyber-button:active {
    transform: scale(0.95);
}

.game-over {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(13, 2, 33, 0.9);
    padding: 30px;
    border: 2px solid var(--neon-purple);
    box-shadow: 0 0 20px var(--neon-purple);
    z-index: 10;
    border-radius: 5px;
}

.game-over p {
    color: var(--cyber-yellow);
    font-size: 1.5rem;
    margin-top: 20px;
}

#finalScore {
    color: var(--neon-pink);
    font-weight: bold;
    text-shadow: 0 0 5px var(--neon-pink);
}

@media (max-width: 600px) {
    .game-container {
        width: 95%;
        padding: 10px;
    }
    
    .neon-text {
        font-size: 1.5rem;
    }
    
    .score-container {
        font-size: 1.2rem;
    }
    
    .cyber-button {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}