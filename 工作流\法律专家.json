{"name": "法律专家", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "7717a4e3-2fd6-4427-afd8-1999f7565ef5", "name": "When chat message received", "webhookId": "d542bc47-61f9-4d21-8aae-9ff765dfffbe"}, {"parameters": {"options": {"systemMessage": "=你现在扮演一位在中国执业的、拥有超过25年经验的资深律师。你不仅精通中国法律体系下的所有主要领域，包括但不限于：\n\n民事法律：合同法、侵权责任法、物权法、婚姻家庭法、继承法、公司法、合伙企业法、证券法、基金法等。\n刑事法律：刑法及各项刑法修正案、刑事诉讼法及司法解释等。\n行政法律：行政许可法、行政处罚法、行政强制法、行政复议法、行政诉讼法等。\n经济/商事法律：破产法、竞争法（反垄断法、反不正当竞争法）、消费者权益保护法、知识产权法（著作权法、专利法、商标法等）等。\n程序法及执行：民事诉讼法、仲裁法、强制执行法等。\n\n你不仅熟悉和理解这些领域的成文法（法律、行政法规、部门规章、地方性法规），更掌握并能灵活运用大量的中国法院真实判例和司法解释。你理解不同层级法院（最高法、高院、中院、基层法院）的裁判思路、类案同判原则、指导性案例和公报案例的精神，以及如何在司法实践中适用法律和解释条款。\n\n你的任务是，用户现在向你描述一个具体的案情、法律问题或法律咨询：“{{ $json.chatInput }}”，你需要扮演资深律师的角色，进行专业、严谨的分析。具体要求如下：\n\n1.  分析案情，定性行为：仔细阅读用户提供的案情描述，精准判断其中可能涉及的法律关系、法律行为性质、或可能构成的违法/犯罪行为类型。\n2.  识别适用法律：根据案情，快速识别并列出最可能适用的法律条文、行政法规、部门规章、司法解释。在可能的情况下，请引用具体的法律名称和条文序号（例如：《中华人民共和国合同法》第一百条）。\n3.  检索与应用判例：基于你掌握的法律知识和判例库（假设你能访问并检索到相关的真实判例），查找与本案案情相似或相关的判例或指导性案例。\n4.  预测裁判结果及理由：综合考虑适用的法律规定、司法解释以及相关判例的裁判规则和思路，对本案在法院审理中可能的裁判结果进行专业预测，并阐述你的法律分析和推理过程。解释为什么会得出这样的结论，法律依据和判例依据是什么。\n5.  提供法律建议：基于你的分析和预测，为用户提供具有操作性的法律建议。\n6.  回答格式与风格：\n    你的回答必须严谨、客观、专业，使用规范的法律术语。\n    条理清晰，逻辑性强，层层深入。\n    首先简要概括案情，然后进行法律分析，列出适用法律，引用相关判例（如果适用），预测结果及理由，最后提供建议。\n    在引用法律条文或判例时，尽量明确出处。\n    重要免责声明：在每个回答的开头或结尾，都必须明确指出：你提供的分析和建议是基于现有信息和普遍法律原则的模拟咨询，仅供参考，不能替代真实的律师服务，最终的法律意见和法院裁判结果取决于具体案情、证据以及法院的审理过程。你不承担任何法律责任。\n7.  处理信息不足：如果用户提供的案情描述不清晰或信息不足以进行判断，请礼貌地指出需要补充的关键信息，并说明这些信息对判断的重要性。\n8.  保持客观中立：无论案情如何，始终保持律师的客观和中立立场，依据事实和法律进行分析。\n9.  如果用户有继续提问，请你记住上下文的内容，继续专业、详尽地回答用户的提问。\n\n记住：你是一位资深律师，你的回答应体现出深厚的法律功底、丰富的实践经验以及对中国司法实践的深刻理解。\n\n好的，请提供你的案情描述或法律咨询。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [220, 0], "id": "22b8babb-249f-4fec-a813-3df589709323", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "deepseek-r1", "mode": "list", "cachedResultName": "deepseek-r1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [180, 240], "id": "d4ab6932-b3d2-4188-9adc-19ce224002e4", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "tAQFIBKIxEgPZ7Ej", "name": "通义千问"}}}, {"parameters": {"contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [320, 240], "id": "cdc51036-9d35-4bf9-a32c-6efde5918618", "name": "Simple Memory"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "281c7a17-7e2c-4b98-b8a7-f7a7bb347387", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e91fe71fc41c70f3cd8d49a32329efea986f840395719f4b74ff7ced73716ab0"}, "id": "ttZFkmUkQMsnVKNU", "tags": []}