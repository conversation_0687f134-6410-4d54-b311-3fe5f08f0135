{"name": "大型文件理解", "nodes": [{"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2160, 180], "id": "8b9cc8a6-dbbe-4761-9f9e-3b238853bbef", "name": "Merge2"}, {"parameters": {"formTitle": "大型文件理解", "formDescription": "上传文件、图片、影片等（可以超过20M）", "formFields": {"values": [{"fieldLabel": "data", "fieldType": "file", "multipleFiles": false}, {"fieldLabel": "model", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "gemini-2.5-flash-preview-05-20"}, {"option": "gemini-2.5-flash-preview-04-17"}, {"option": "gemini-2.0-flash"}]}}, {"fieldLabel": "prompt", "fieldType": "textarea", "placeholder": "提取图中的内容并保持原有格式输出/帮我总结一下这篇文章的主要内容"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [740, 160], "id": "9d9f279b-fb3d-4aee-8f7d-95b1f4de9359", "name": "On form submission7", "webhookId": "401bed78-5f28-4990-8a7c-e1a373bff850"}, {"parameters": {"assignments": {"assignments": [{"id": "31fc816c-88eb-4832-8c58-9409d5d76b34", "name": "NUM_BYTES", "value": "={{ $binary.data.fileSize }}", "type": "string"}, {"id": "9d83561b-0409-4e4e-8315-fff3dc395b88", "name": "MIME_TYPE", "value": "={{ $binary.data.mimeType }}", "type": "string"}, {"id": "f705ece3-5ea6-4f9d-bc5b-332876289bca", "name": "DISPLAY_NAME", "value": "={{ $binary.data.fileName }}", "type": "string"}, {"id": "efc87b7e-c3a4-47e0-8290-2f97398e5531", "name": "model", "value": "={{ $json.model }}", "type": "string"}, {"id": "558a70a8-4d3c-4c98-a52d-f336b5e0e4d6", "name": "prompt", "value": "={{ $json.prompt }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1000, 160], "id": "0bab3811-e6df-4db3-8987-b9984dd80cc0", "name": "Edit Fields3"}, {"parameters": {"method": "POST", "url": "=https://generativelanguage.googleapis.com/v1beta/models/{{ $('Edit Fields3').item.json.model }}:generateContent ", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"contents\": [\n        {\n            \"parts\": [\n                {\n                    \"text\": \"{{ $('Edit Fields3').item.json.prompt }}\"\n                },\n                {\n                    \"file_data\": {\n                        \"mime_type\": \"{{ $json.file.mimeType }}\",\n                        \"file_uri\": \"{{ $json.file.uri }}\"\n                    }\n                }\n            ]\n        }\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3380, 180], "id": "c38ba12a-ef07-4c26-9d59-f634a5827df8", "name": "HTTP Request10"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/upload/v1beta/files", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Goog-Upload-Protocol", "value": "resumable"}, {"name": "X-Goog-Upload-Command", "value": "start"}, {"name": "X-Goog-Upload-Header-Content-Length", "value": "={{ $binary.data.fileSize }}"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-Goog-Upload-Header-Content-Type", "value": "={{ $binary.data.mimeType }}"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "application/json", "body": "={   \"file\": {     \"display_name\": \"{{ $json.data.filename }}\"   } }", "options": {"response": {"response": {"fullResponse": true}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1520, 260], "id": "7b56705f-ae97-4f88-8db3-eeeb99edb261", "name": "GetUpLoadUrl1"}, {"parameters": {"content": "## Trigger and Prepare Data\n\n*   **Nodes:** `On form submission`, `Set`\n*   **Purpose:**\n    1.  `On form submission`: Starts the workflow when a user submits a form. It captures the uploaded file binary data and other form inputs (e.g., model choice, thinking budget).\n    2.  `Set`: Extracts essential metadata from the uploaded file's binary data (like file size, MIME type, and file name) and makes them easily accessible. It also combines these with other form inputs received from the trigger node.\n*   **Configuration:**\n    *   `On form submission`: Configured to accept file upload and relevant text/dropdown inputs.\n    *   `Set`: Assignments are created to get binary properties like `={{ $binary.data.fileSize }}`, `={{ $binary.data.mimeType }}`, `={{ $binary.data.fileName }}` and also include fields from the previous node like `={{ $json.model }}`.\n\n---\n\n## 觸發與數據準備\n\n*   **節點：** `On form submission` (表單提交時), `Set` (設置)\n*   **目的：**\n    1.  `On form submission` (表單提交時): 當使用者提交表單時啟動工作流程。它捕獲上傳的檔案二進位數據和其他表單輸入（例如，模型選擇、思考預算）。\n    2.  `Set` (設置): 從上傳檔案的二進位數據中提取必要的元數據（例如檔案大小、MIME 類型和檔案名稱），使其易於訪問。同時將這些數據與從觸發器節點接收到的其他表單輸入合併。\n*   **設定：**\n    *   `On form submission` (表單提交時): 配置為接受檔案上傳和相關的文字/下拉輸入。\n    *   `Set` (設置): 創建賦值以獲取二進位屬性，例如 `={{ $binary.data.fileSize }}` (檔案大小), `={{ $binary.data.mimeType }}` (MIME 類型), `={{ $binary.data.fileName }}` (檔案名稱)，並包含來自前一個節點的字段，例如 `={{ $json.model }}` (模型)。", "height": 1020, "width": 620, "color": 12}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 420], "id": "9c3e18b3-5880-4b87-922d-1c6320f91d85", "name": "Sticky Note (Trigger & Prep)"}, {"parameters": {"content": "## Initiate Resumable Upload to Google File API\n\n*   **Nodes:** `HTTP Request`\n*   **Purpose:** Call the Google File API (`https://generativelanguage.googleapis.com/upload/v1beta/files`) to start a resumable upload session for the large file. This API call, with the `start` command, returns a unique upload URL (`x-goog-upload-url` header) needed for the subsequent file transfer.\n*   **Configuration:**\n    *   **URL:** `https://generativelanguage.googleapis.com/upload/v1beta/files`\n    *   **Method:** `POST`\n    *   **Authentication:** Generic HTTP Query (using your Google API key credential).\n    *   **Headers:** Set `X-Goog-Upload-Protocol: resumable`, `X-Goog-Upload-Command: start`. Include `X-Goog-Upload-Header-Content-Length` and `X-Goog-Upload-Header-Content-Type` using the file metadata extracted earlier (e.g., `={{ $binary.data.fileSize }}`, `={{ $binary.data.mimeType }}`). Set `Content-Type: application/json` for the request body.\n    *   **Body:** JSON body providing initial file details like display name. Example: `={ \"file\": { \"display_name\": \"{{ $json.data.filename }}\" } }`.\n    *   **Options:** Crucially, set the `response` option to `Full Response` to capture the response headers, especially `x-goog-upload-url`.\n\n---\n\n## 啟動 Google File API 可續傳上傳\n\n*   **節點：** `HTTP Request` (HTTP 請求)\n*   **目的：** 呼叫 Google File API (`https://generativelanguage.googleapis.com/upload/v1beta/files`) 為大型檔案啟動一個可續傳上傳會話。此 API 呼叫使用 `start` 命令，會返回一個唯一的上傳 URL（在 `x-goog-upload-url` 標頭中），後續的檔案傳輸需要這個 URL。\n*   **設定：**\n    *   **URL:** `https://generativelanguage.googleapis.com/upload/v1beta/files`\n    *   **方法:** `POST`\n    *   **驗證:** 通用 HTTP 查詢（使用您的 Google API 金鑰憑證）。\n    *   **標頭:** 設定 `X-Goog-Upload-Protocol: resumable` (協議: 可續傳), `X-Goog-Upload-Command: start` (命令: 開始)。使用先前提取的檔案元數據包含 `X-Goog-Upload-Header-Content-Length` (檔案長度) 和 `X-Goog-Upload-Header-Content-Type` (內容類型)，例如 `={{ $binary.data.fileSize }}` (檔案大小), `={{ $binary.data.mimeType }}` (MIME 類型)。為請求主體設定 `Content-Type: application/json` (內容類型)。\n    *   **主體:** JSON 主體提供初始檔案詳細資訊，如顯示名稱。範例：`={ \"file\": { \"display_name\": \"{{ $json.data.filename }}\" } }` (顯示名稱)。\n    *   **選項:** 關鍵地，將 `response` 選項設定為 `Full Response` (完整回應) 以捕獲回應標頭，特別是 `x-goog-upload-url` (上傳 URL)。", "height": 1020, "width": 660, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1260, 420], "id": "f18c146c-1900-4455-83d2-072c558fdaea", "name": "Sticky Note (Initiate Upload)"}, {"parameters": {"content": "## Combine Data for Upload\n\n*   **Nodes:** `Merge`\n*   **Purpose:** This node is crucial for combining the outputs from two different branches: the data containing file metadata and form inputs (from the `Set` node) and the data containing the upload URL (from the \"Initiate Upload\" `HTTP Request` node's response headers). The actual file binary data is implicitly passed through from the original trigger. Merging ensures that the upload URL and the file binary are available together for the next step (the actual file upload).\n*   **Configuration:**\n    *   **Mode:** `Combine`\n    *   **Combine By:** `By Position`. Connect the output of the `Set` node to Input 1 and the output of the \"Initiate Upload\" `HTTP Request` node to Input 2.\n\n---\n\n## 合併數據以上傳\n\n*   **節點：** `Merge` (合併)\n*   **目的：** 此節點對於合併來自兩個不同分支的輸出至關重要：包含檔案元數據和表單輸入的數據（來自 `Set` 節點）以及包含上傳 URL 的數據（來自「啟動上傳」 `HTTP Request` 節點的回應標頭）。實際的檔案二進位數據會從原始觸發器隱式傳遞。合併確保上傳 URL 和檔案二進位數據在下一步（實際檔案上傳）中同時可用。\n*   **設定：**\n    *   **模式:** `Combine` (合併)\n    *   **合併方式:** `By Position` (按位置)。將 `Set` 節點的輸出連接到輸入 1，將「啟動上傳」 `HTTP Request` 節點的輸出連接到輸入 2。", "height": 1020, "width": 620, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1940, 420], "id": "88c8e7bc-80bc-4de1-b21e-f4e5c236618d", "name": "Sticky Note (Merge)"}, {"parameters": {"content": "## Upload File Binary to Google\n\n*   **Nodes:** `HTTP Request`\n*   **Purpose:** This is where the actual large file binary data is sent to the Google File API using the specific upload URL obtained in the initiation step. The `upload, finalize` command instructs <PERSON> to accept the data and finalize the file creation on their end.\n*   **Configuration:**\n    *   **URL:** `={{ $json.headers['x-goog-upload-url'] }}` (Dynamically uses the upload URL from the merged data).\n    *   **Method:** `POST`\n    *   **Authentication:** Generic HTTP Query (using the same credential as before).\n    *   **Headers:** Set `Content-Length` (using the file size from the merged data, e.g., `={{ $('Edit Fields').item.json.NUM_BYTES }}`), `X-Goog-Upload-Offset: 0` (indicates starting from the beginning), and `X-Goog-Upload-Command: upload, finalize`.\n    *   **Body:** Configured to send the raw binary data. Use `Content Type: multipart-form-data` and specify the binary property name from the original trigger (e.g., `data`). The file name for the form part can be dynamic (e.g., `={{ $('Edit Fields').item.json.data.filename }}`).\n*   **Output:** The response will contain details about the uploaded file, including its unique `uri` (e.g., `files/your_file_id`) which is needed for the next step (processing with Gemini).\n\n---\n\n## 上傳檔案二進位數據至 Google\n\n*   **節點：** `HTTP Request` (HTTP 請求)\n*   **目的：** 在此將實際的大型檔案二進位數據使用在啟動步驟中獲取的特定上傳 URL 發送至 Google File API。`upload, finalize` 命令指示 Google 接受數據並在其端完成檔案創建。\n*   **設定：**\n    *   **URL:** `={{ $json.headers['x-goog-upload-url'] }}` (動態使用來自合併數據的上傳 URL)。\n    *   **方法:** `POST`\n    *   **驗證:** 通用 HTTP 查詢（使用與之前相同的憑證）。\n    *   **標頭:** 設定 `Content-Length` (內容長度，使用來自合併數據的檔案大小，例如 `={{ $('Edit Fields').item.json.NUM_BYTES }}`), `X-Goog-Upload-Offset: 0` (指示從頭開始), 以及 `X-Goog-Upload-Command: upload, finalize` (上傳命令)。\n    *   **主體:** 配置為發送原始二進位數據。使用 `Content Type: multipart-form-data` (內容類型) 並指定來自原始觸發器的二進位屬性名稱（例如 `data`）。表單部分的檔案名稱可以是動態的（例如 `={{ $('Edit Fields').item.json.data.filename }}`）。\n*   **輸出：** 回應將包含有關已上傳檔案的詳細資訊，包括其唯一的 `uri` (例如 `files/your_file_id`)，這是下一步（使用 Gemini 處理）所需的。", "height": 1020, "width": 640, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2580, 420], "id": "5acee9d9-f0fa-4082-8e9c-684628df05bb", "name": "Sticky Note (Upload Binary)"}, {"parameters": {"content": "## Process File with Gemini\n\n*   **Nodes:** `HTTP Request`\n*   **Purpose:** Make a final API call to the Gemini `generateContent` endpoint. Instead of sending the file binary directly (which isn't possible for large files in a single request), this request references the file by its unique `file_uri` obtained from the previous successful upload step. The request also includes the prompt or instruction for <PERSON> (e.g., summarize the file).\n*   **Configuration:**\n    *   **URL:** `https://generativelanguage.googleapis.com/v1beta/models/{{ $('Edit Fields').item.json.model }}:generateContent` (Dynamically uses the model name selected in the form).\n    *   **Method:** `POST`\n    *   **Authentication:** Generic HTTP Query (using the same credential).\n    *   **Headers:** `Content-Type: application/json`.\n    *   **Body:** JSON body formatted for the `generateContent` API, including the prompt in a `parts` object and the file reference using `file_data` with the `mime_type` and the crucial `file_uri`. Example: `={{ { \"contents\": [{ \"parts\":[ { \"text\": \"summarize\" }, { \"file_data\":{ \"mime_type\": \"{{ $json.file.mimeType }}\", \"file_uri\": \"{{ $json.file.uri }}\" } } ] }] } }}` (Note: `file.mimeType` and `file.uri` are expected from the response of the *previous* upload node).\n*   **Output:** The response from Gemini containing the processed result (e.g., the summary).\n\n---\n\n## 使用 Gemini 處理檔案\n\n*   **節點：** `HTTP Request` (HTTP 請求)\n*   **目的：** 向 Gemini `generateContent` 端點發出最後一個 API 呼叫。此請求不是直接發送檔案二進位數據（大型檔案在單個請求中無法實現），而是通過前一個成功上傳步驟中獲取的唯一 `file_uri` (檔案 URI) 來引用檔案。請求還包含給 Gemini 的提示或指令（例如，摘要檔案）。\n*   **設定：**\n    *   **URL:** `https://generativelanguage.googleapis.com/v1beta/models/{{ $('Edit Fields').item.json.model }}:generateContent` (動態使用在表單中選定的模型名稱)。\n    *   **方法:** `POST`\n    *   **驗證:** 通用 HTTP 查詢（使用相同的憑證）。\n    *   **標頭:** `Content-Type: application/json` (內容類型)。\n    *   **主體:** 格式化為 `generateContent` API 的 JSON 主體，在 `parts` 對象中包含提示，並使用包含 `mime_type` (MIME 類型) 和關鍵的 `file_uri` (檔案 URI) 的 `file_data` 來引用檔案。範例：`={{ { \"contents\": [{ \"parts\":[ { \"text\": \"summarize\" }, { \"file_data\":{ \"mime_type\": \"{{ $json.file.mimeType }}\", \"file_uri\": \"{{ $json.file.uri }}\" } } ] }] } }}` (注意：`file.mimeType` 和 `file.uri` 預期來自前一個上傳節點的回應)。\n*   **輸出：** 包含處理結果（例如，摘要）的 Gemini 回應。", "height": 1020, "width": 640, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3240, 420], "id": "f28b5ac4-b9ee-4bab-8ea4-b4397790d617", "name": "Sticky Note (Process Gemini)"}, {"parameters": {"content": "## large file Understanding - audio, documents, video\n\nEnglish:\nThis section outlines a general workflow for understanding large files like audio, documents, or videos using Gemini. The core idea is to first upload the file using Google's resumable upload protocol and then pass the file's URI to <PERSON> for processing.\n\nSteps:\n1.  **Trigger**: Typically a form submission (`On form submission4`) where the user uploads a file and might select a model or other parameters.\n2.  **Prepare Data**: An `Edit Fields` node (`Edit Fields2`) extracts file metadata (size, MIME type, name) and any user-selected options (like model name, thinking budget).\n3.  **Get Upload URL**: An `HTTP Request` node (`GetUpLoadUrl`) initiates the resumable upload by calling the Google File API (`https://generativelanguage.googleapis.com/upload/v1beta/files`) with command `start`. This returns a unique upload URL in the `x-goog-upload-url` header.\n4.  **Merge Data**: A `Merge` node combines the file metadata with the upload URL.\n5.  **Upload File**: Another `HTTP Request` node (`HTTP Request1`) uploads the actual file binary data to the obtained upload URL, using command `upload, finalize`.\n6.  **Process with Gemini**: A final `HTTP Request` node (`HTTP Request7`) sends a request to the Gemini API (`<model_name>:generateContent`). This request includes the `file_uri` of the uploaded file (obtained from the previous step's response) and a prompt (e.g., \"Summarize this audio file\").\n\n繁體中文:\n## 大型檔案理解 - 音訊、文件、影片\n\n本節概述了使用 Gemini 理解音訊、文件或影片等大型檔案的一般工作流程。核心思想是首先使用 Google 的可續傳上傳協議上傳檔案，然後將檔案的 URI 傳遞給 Gemini 進行處理。\n\n步驟：\n1.  **觸發器**: 通常是表單提交 (`On form submission4`)，使用者上傳檔案並可能選擇模型或其他參數。\n2.  **準備數據**: `Edit Fields` 節點 (`Edit Fields2`) 提取檔案元數據（大小、MIME 類型、名稱）和任何使用者選擇的選項（如模型名稱、思考預算）。\n3.  **獲取上傳 URL**: `HTTP Request` 節點 (`GetUpLoadUrl`) 通過呼叫 Google File API (`https://generativelanguage.googleapis.com/upload/v1beta/files`) 並使用 `start` 命令來啟動可續傳上傳。這會在 `x-goog-upload-url` 標頭中返回一個唯一的上傳 URL。\n4.  **合併數據**: `Merge` 節點將檔案元數據與上傳 URL 合併。\n5.  **上傳檔案**: 另一個 `HTTP Request` 節點 (`HTTP Request1`) 將實際的檔案二進制數據上傳到獲得的上傳 URL，使用 `upload, finalize` 命令。\n6.  **使用 Gemini 處理**: 最後一個 `HTTP Request` 節點 (`HTTP Request7`) 向 Gemini API (`<模型名稱>:generateContent`) 發送請求。此請求包括上傳檔案的 `file_uri`（從上一步的回應中獲得）和一個提示（例如「摘要此音訊檔案」）。", "height": 1440, "width": 600, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "7e149ad6-5c82-44a5-904f-556a6b167fd3", "name": "Sticky Note (Overview)"}, {"parameters": {"content": "# large file Understanding - audio, documents, video\n", "height": 80, "width": 3260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 0], "id": "ac4f65df-737a-4611-b724-dc65995beb0e", "name": "Sticky Note (Form Trigger)5"}, {"parameters": {"content": "# Workflow", "height": 300, "width": 3260, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 100], "id": "4f42ba6f-34af-4694-929f-f6bf8ef73396", "name": "Sticky Note (<PERSON> Trigger)6"}, {"parameters": {"method": "POST", "url": "={{ $json.headers['x-goog-upload-url'] }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Length", "value": "={{ $('Edit Fields3').item.json.NUM_BYTES }}"}, {"name": "X-Goog-Upload-Offset", "value": "0"}, {"name": "X-Goog-Upload-Command", "value": "upload, finalize"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "={{ $('Edit Fields3').item.json.data.filename }}", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2820, 180], "id": "1ad9514c-9640-4aed-b3cd-754001f7b268", "name": "HTTP Request2"}, {"parameters": {"operation": "toText", "sourceProperty": "candidates[0].content.parts[0].text", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [3580, 180], "id": "897ec276-d33f-4fa9-b090-94d0c279b158", "name": "Convert to File"}], "pinData": {}, "connections": {"Merge2": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "On form submission7": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}, "Edit Fields3": {"main": [[{"node": "GetUpLoadUrl1", "type": "main", "index": 0}, {"node": "Merge2", "type": "main", "index": 0}]]}, "GetUpLoadUrl1": {"main": [[{"node": "Merge2", "type": "main", "index": 1}]]}, "HTTP Request2": {"main": [[{"node": "HTTP Request10", "type": "main", "index": 0}]]}, "HTTP Request10": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "56a52c3d-6f1b-436f-9012-687789977276", "meta": {"instanceId": "04695fa805d662a7ca811a5d9568e6cc080c07e6ec9f81360f8146eccb701cc6"}, "id": "2K0NDdZvauIuAedR", "tags": []}