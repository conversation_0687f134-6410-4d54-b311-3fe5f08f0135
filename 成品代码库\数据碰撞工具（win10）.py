import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import re
import os
import logging


# 数据清洗函数：去除空格、标点符号等
def clean_data(text):
    if pd.isna(text):
        return ''
    # 转换为字符串
    text = str(text)
    # 去除所有非中文字符和字母数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
    # 去除多余的空格（如果有）
    text = text.strip()
    return text

# 数据匹配和计数函数
def match_data(df1, column1, df2, column2, match_mode="exact"):
    logging.info(f"开始数据匹配，模式：{match_mode}")
    
    # 创建数据副本，避免修改原始数据
    df1_copy = df1.copy()
    df2_copy = df2.copy()
    
    # 统一数据类型为字符串
    df1_copy[column1] = df1_copy[column1].astype(str)
    df2_copy[column2] = df2_copy[column2].astype(str)
    
    if match_mode == "exact":
        # 精确匹配模式
        matched = pd.merge(df1_copy, df2_copy, left_on=column1, right_on=column2, how='inner')
        # 获取匹配列的唯一值
        unique_matches = pd.DataFrame({
            '匹配值': matched[column1].unique()
        })
    else:
        # 模糊匹配模式
        # 清洗数据
        logging.debug(f"清洗第一个文件的列: {column1}")
        df1_copy['cleaned'] = df1_copy[column1].apply(clean_data)
        logging.debug(f"清洗第二个文件的列: {column2}")
        df2_copy['cleaned'] = df2_copy[column2].apply(clean_data)
        
        # 使用merge进行匹配（碰撞）
        logging.debug("执行数据合并匹配")
        matched = pd.merge(df1_copy, df2_copy, on='cleaned', how='inner')
        # 获取清洗后的匹配列的唯一值
        unique_matches = pd.DataFrame({
            '匹配值': matched['cleaned'].unique()
        })
        matched = matched.drop(columns=['cleaned'])  # 删除辅助列
    
    # 计数
    match_count = len(unique_matches)
    logging.info(f"匹配完成，去重后总匹配数: {match_count}")
    
    return unique_matches, match_count

# 保存结果函数
def save_results(matched_df):
    logging.info("开始保存匹配结果")
    if matched_df.empty:
        messagebox.showwarning("无匹配结果", "没有匹配到任何结果。")
        logging.warning("尝试保存空的匹配结果")
        return
    # 提供保存对话框
    file_path = filedialog.asksaveasfilename(defaultextension=".xlsx",
                                             filetypes=[("Excel files", "*.xlsx"),
                                                        ("CSV files", "*.csv")])
    if file_path:
        try:
            if file_path.endswith('.csv'):
                matched_df.to_csv(file_path, index=False, encoding='utf-8-sig')
                logging.info(f"结果已保存为 CSV 文件: {file_path}")
            else:
                matched_df.to_excel(file_path, index=False, engine='openpyxl')
                logging.info(f"结果已保存为 Excel 文件: {file_path}")
            messagebox.showinfo("保存成功", f"结果已保存到 {file_path}")
        except Exception as e:
            logging.error(f"保存文件时出错: {e}")
            messagebox.showerror("保存失败", f"保存文件时出错: {e}")

# 主应用类
class DataMatcherApp:
    def __init__(self, root):
        self.root = root
        self.root.title("数据匹配工具")
        self.root.geometry("700x550")
        self.root.resizable(False, False)
        # self.root.iconbitmap(False, default=None)  # 注释或移除这行
        
        # 初始化变量
        self.file1_path = ""
        self.file2_path = ""
        self.df1 = None
        self.df2 = None
        self.matched_df = None

        # 创建界面组件
        self.create_widgets()

    def create_widgets(self):
        # 创建框架以更好地布局
        frame = tk.Frame(self.root, padx=10, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)

        # 第一个文件上传
        tk.Label(frame, text="选择第一个文件:").grid(row=0, column=0, sticky=tk.W, pady=(0,5))

        self.file1_entry = tk.Entry(frame, width=60)
        self.file1_entry.grid(row=1, column=0, padx=(0,5), pady=(0,5))

        self.file1_button = tk.Button(frame, text="浏览", width=10, command=self.browse_file1)
        self.file1_button.grid(row=1, column=1, pady=(0,5))

        # 第二个文件上传
        tk.Label(frame, text="选择第二个文件:").grid(row=2, column=0, sticky=tk.W, pady=(10,5))

        self.file2_entry = tk.Entry(frame, width=60)
        self.file2_entry.grid(row=3, column=0, padx=(0,5), pady=(0,5))

        self.file2_button = tk.Button(frame, text="浏览", width=10, command=self.browse_file2)
        self.file2_button.grid(row=3, column=1, pady=(0,5))

        # 选择列
        tk.Label(frame, text="选择用于匹配的第一个文件列:").grid(row=4, column=0, sticky=tk.W, pady=(10,5))

        self.column1_var = tk.StringVar()
        self.column1_dropdown = tk.OptionMenu(frame, self.column1_var, ())
        self.column1_dropdown.config(width=58)
        self.column1_dropdown.grid(row=5, column=0, columnspan=2, pady=(0,5))

        tk.Label(frame, text="选择用于匹配的第二个文件列:").grid(row=6, column=0, sticky=tk.W, pady=(10,5))

        self.column2_var = tk.StringVar()
        self.column2_dropdown = tk.OptionMenu(frame, self.column2_var, ())
        self.column2_dropdown.config(width=58)
        self.column2_dropdown.grid(row=7, column=0, columnspan=2, pady=(0,5))

        # 在匹配按钮之前添加匹配模式选择
        tk.Label(frame, text="选择匹配模式:").grid(row=8, column=0, sticky=tk.W, pady=(10,5))
        
        # 添加单选钮组
        self.match_mode = tk.StringVar(value="exact")
        tk.Radiobutton(frame, text="精确匹配（完全一致）", variable=self.match_mode, 
                       value="exact").grid(row=8, column=0, sticky=tk.W, padx=(100,0))
        tk.Radiobutton(frame, text="模糊匹配（忽略标点空格等噪声数据）", variable=self.match_mode, 
                       value="fuzzy").grid(row=8, column=0, sticky=tk.W, padx=(300,0))

        # 匹配按钮移到下一行
        self.match_button = tk.Button(frame, text="开始匹配", width=20, command=self.start_matching)
        self.match_button.grid(row=9, column=0, columnspan=2, pady=(20,10))

        # 结果展示
        self.result_label = tk.Label(frame, text="匹配结果将显示在弹出窗口中。")
        self.result_label.grid(row=10, column=0, columnspan=2, pady=(10,5))

        # 保存按钮
        self.save_button = tk.Button(frame, text="保存结果", width=20, command=self.save_results_button)
        self.save_button.grid(row=11, column=0, columnspan=2, pady=(5,10))
        self.save_button.config(state=tk.DISABLED)

        # 添加版权信息标签
        copyright_label = tk.Label(frame, text="版权所有：台州市公安局 解晟", fg="black")
        copyright_label.grid(row=12, column=0, columnspan=2, pady=(20,5))

    def browse_file1(self):
        file_path = filedialog.askopenfilename(filetypes=[("All files", "*.*"),
                                                         ("Excel files", "*.xlsx *.xls"),
                                                         ("CSV files", "*.csv"),
                                                         ("Text files", "*.txt")])
        if file_path:
            self.file1_path = file_path
            self.file1_entry.delete(0, tk.END)
            self.file1_entry.insert(0, file_path)
            logging.info(f"选择第一个文件: {file_path}")
            self.load_file1()

    def browse_file2(self):
        file_path = filedialog.askopenfilename(filetypes=[("All files", "*.*"),
                                                         ("Excel files", "*.xlsx *.xls"),
                                                         ("CSV files", "*.csv"),
                                                         ("Text files", "*.txt")])
        if file_path:
            self.file2_path = file_path
            self.file2_entry.delete(0, tk.END)
            self.file2_entry.insert(0, file_path)
            logging.info(f"选择第二个文件: {file_path}")
            self.load_file2()

    def load_file1(self):
        try:
            self.df1 = self.read_file(self.file1_path)
            columns = list(self.df1.columns)
            if not columns:
                raise ValueError("第一个文件中没有列。")
            self.column1_var.set(columns[0])
            menu = self.column1_dropdown["menu"]
            menu.delete(0, "end")
            for col in columns:
                menu.add_command(label=col, command=lambda value=col: self.column1_var.set(value))
            logging.info(f"加载第一个文件的列: {columns}")
        except Exception as e:
            logging.error(f"无法读取第一个文件: {e}")
            messagebox.showerror("文件读取错误", f"无法读取第一个文件: {e}")

    def load_file2(self):
        try:
            self.df2 = self.read_file(self.file2_path)
            columns = list(self.df2.columns)
            if not columns:
                raise ValueError("第二个文件中没有列。")
            self.column2_var.set(columns[0])
            menu = self.column2_dropdown["menu"]
            menu.delete(0, "end")
            for col in columns:
                menu.add_command(label=col, command=lambda value=col: self.column2_var.set(value))
            logging.info(f"加载第二个文件的列: {columns}")
        except Exception as e:
            logging.error(f"无法读取第二个文件: {e}")
            messagebox.showerror("文件读取错误", f"无法读取第二个文件: {e}")

    def read_file(self, file_path):
        ext = os.path.splitext(file_path)[1].lower()
        logging.debug(f"读取文件: {file_path}, 扩展名: {ext}")
        
        # 尝试不同的编码方式
        encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
        
        if ext in ['.xlsx', '.xls']:
            engines = {
                '.xlsx': 'openpyxl',
                '.xls': 'xlrd'
            }
            engine = engines.get(ext, 'openpyxl')
            return pd.read_excel(file_path, engine=engine)
        elif ext == '.csv':
            # 尝试不同的编码方式读取CSV文件
            for encoding in encodings:
                try:
                    return pd.read_csv(file_path, encoding=encoding)
                except UnicodeDecodeError:
                    continue
            raise ValueError(f"无法使用已知编码方式读取文件，请确保文件编码正确")
        elif ext == '.txt':
            # 尝试不同的编码方式读取TXT文件
            for encoding in encodings:
                try:
                    return pd.read_csv(file_path, delimiter='\t', encoding=encoding)
                except UnicodeDecodeError:
                    continue
            raise ValueError(f"无法使用已知编码方式读取文件，请确保文件编码正确")
        else:
            raise ValueError("不支持的文件格式")

    def start_matching(self):
        logging.info("点击开始匹配按钮")
        if self.df1 is None or self.df2 is None or self.df1.empty or self.df2.empty:
            messagebox.showwarning("文件未选择", "请确保已选择并加载两个文件。")
            logging.warning("文件未选择或加载")
            return
        
        col1 = self.column1_var.get()
        col2 = self.column2_var.get()
        if not col1 or not col2:
            messagebox.showwarning("列未选择", "请确保已选择用于匹配的两列。")
            logging.warning("匹配列未选择")
            return
        
        # 获取匹配模式
        match_mode = self.match_mode.get()
        
        # 显示匹配开始的提示
        mode_text = "精确" if match_mode == "exact" else "模糊"
        messagebox.showinfo("开始匹配", f"数据{mode_text}匹配即将开始，请稍候...")
        logging.info(f"匹配列: file1 -> {col1}, file2 -> {col2}, 匹配模式: {mode_text}")
        
        try:
            matched_df, count = match_data(self.df1, col1, self.df2, col2, match_mode)
            if count > 0:
                # 显示结果
                result_window = tk.Toplevel(self.root)
                result_window.title(f"{mode_text}匹配结果（去重后）")
                result_window.geometry("400x600")
                
                # 使用文本框展示
                text = tk.Text(result_window, wrap='none')
                text.pack(expand=True, fill='both')
                
                # 插入DataFrame内容
                text.insert(tk.END, matched_df.to_string(index=False))
                
                # 添加滚动条
                scrollbar_y = tk.Scrollbar(result_window, command=text.yview)
                scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
                text.configure(yscrollcommand=scrollbar_y.set)
                
                # 显示计数
                tk.Label(result_window, text=f"去重后匹配总数: {count}").pack(pady=5)
                
                # 保存按钮可用
                self.matched_df = matched_df
                self.save_button.config(state=tk.NORMAL)
                logging.info("匹配完成并显示结果")
            else:
                messagebox.showinfo("无匹配结果", "没有找到任何匹配项。")
                self.save_button.config(state=tk.DISABLED)
                logging.info("无匹配结果")
        except Exception as e:
            logging.error(f"匹配过程中出现错误: {e}")
            messagebox.showerror("匹配错误", f"匹配过程中出现错误: {e}")

    def save_results_button(self):
        if hasattr(self, 'matched_df') and self.matched_df is not None and not self.matched_df.empty:
            save_results(self.matched_df)
        else:
            messagebox.showwarning("无数据", "没有匹配结果可保存。")
            logging.warning("尝试保存无数据的匹配结果")

# 运行应用程序
if __name__ == "__main__":
    try:
        logging.info("应用程序启动")
        root = tk.Tk()
        app = DataMatcherApp(root)
        root.mainloop()
        logging.info("应用程序正常退出")
    except Exception as e:
        logging.critical(f"应用程序崩溃: {e}")
