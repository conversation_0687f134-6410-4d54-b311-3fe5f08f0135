app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 翻译家
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.2.1@88c1b2c816ef2ea36fc411b35298a621b3260d34bc08bd9357772092728aadde
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.14@b45d5713c339854f5ae5528a746afca789fae6b306ce7a6c84e689000beffa73
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.16@77274df8fe2632cac66bfd153fcc75aa5e96abbe92b5c611b8984ad9f4cd4457
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: llm
        targetType: answer
      id: llm-answer
      selected: false
      source: llm
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1748181901228-source-llm-target
      selected: false
      source: '1748181901228'
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1748181901228-source-1748181948341-target
      selected: false
      source: '1748181901228'
      sourceHandle: source
      target: '1748181948341'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1748181901228-source-1748182217411-target
      selected: false
      source: '1748181901228'
      sourceHandle: source
      target: '1748182217411'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1748181901228-source-1748182242587-target
      selected: false
      source: '1748181901228'
      sourceHandle: source
      target: '1748182242587'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1748182217411-source-1748182933381-target
      selected: false
      source: '1748182217411'
      sourceHandle: source
      target: '1748182933381'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1748181948341-source-1748182936133-target
      selected: false
      source: '1748181948341'
      sourceHandle: source
      target: '1748182936133'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1748182242587-source-1748182938250-target
      selected: false
      source: '1748182242587'
      sourceHandle: source
      target: '1748182938250'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: variable-aggregator
      id: 1748182933381-source-1748183786592-target
      selected: false
      source: '1748182933381'
      sourceHandle: source
      target: '1748183786592'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: variable-aggregator
      id: 1748182938250-source-1748183786592-target
      selected: false
      source: '1748182938250'
      sourceHandle: source
      target: '1748183786592'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: variable-aggregator
      id: answer-source-1748183786592-target
      selected: false
      source: answer
      sourceHandle: source
      target: '1748183786592'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: variable-aggregator
      id: 1748182936133-source-1748183786592-target
      selected: false
      source: '1748182936133'
      sourceHandle: source
      target: '1748183786592'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: variable-aggregator
        targetType: answer
      id: 1748183786592-source-1748188771235-target
      source: '1748183786592'
      sourceHandle: source
      target: '1748188771235'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 54
      id: '1748181901228'
      position:
        x: 82.58563092322044
        y: 282
      positionAbsolute:
        x: 82.58563092322044
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.5-flash-preview-04-17
          provider: langgenius/gemini/google
        prompt_template:
        - id: 28fb0442-c11b-4d4b-8312-5b49cd920413
          role: system
          text: 你是一个精通多国语言的翻译专家。请根据“{{#sys.query#}}”中的内容，先进行语种判断，如果是中文的，请将中文翻译成英文和日文；如果是非中文的，请将所有非中文的翻译成中文。“{{#sys.query#}}”中的内容都是需要翻译的内容，你只需要输出翻译结果，不需要输出思考/think的内容，更不能进行分析和回答。翻译结果如果是中文的请符合中文的语言习惯，如果是英文或者日文也请符合相应语言的习惯。
        selected: true
        structured_output_enabled: false
        title: Gemini2.5
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: llm
      position:
        x: 363.15729879467494
        y: 87.33714157322052
      positionAbsolute:
        x: 363.15729879467494
        y: 87.33714157322052
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#llm.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 105
      id: answer
      position:
        x: 652
        y: 87.33714157322052
      positionAbsolute:
        x: 652
        y: 87.33714157322052
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: deepseek-ai/DeepSeek-R1
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 0d5d71ec-05bd-4d30-9016-fe4792ae27c8
          role: system
          text: 你是一个精通多国语言的翻译专家。请根据“{{#sys.query#}}”中的内容，先进行语种判断，如果是中文的，请将中文翻译成英文和日文；如果是非中文的，请将所有非中文的翻译成中文。“{{#sys.query#}}”中的内容都是需要翻译的内容，你只需要输出翻译结果，不需要输出思考/think的内容，更不能进行分析和回答。翻译结果如果是中文的请符合中文的语言习惯，如果是英文或者日文也请符合相应语言的习惯。
        selected: false
        title: DeepseekR1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1748181948341'
      position:
        x: 363.15729879467494
        y: 241.54295512707392
      positionAbsolute:
        x: 363.15729879467494
        y: 241.54295512707392
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen3-235B-A22B
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: df13ddf0-dca7-4d89-8776-db0a04ea2e70
          role: system
          text: 你是一个精通多国语言的翻译专家。请根据“{{#sys.query#}}”中的内容，先进行语种判断，如果是中文的，请将中文翻译成英文和日文；如果是非中文的，请将所有非中文的翻译成中文。“{{#sys.query#}}”中的内容都是需要翻译的内容，你只需要输出翻译结果，不需要输出思考/think的内容，更不能进行分析和回答。翻译结果如果是中文的请符合中文的语言习惯，如果是英文或者日文也请符合相应语言的习惯。
        selected: false
        title: Qwen3-235B-A22B
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1748182217411'
      position:
        x: 363.15729879467494
        y: 426.7652177747593
      positionAbsolute:
        x: 363.15729879467494
        y: 426.7652177747593
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: doubao-1-5-pro-256k-250115
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 9a71ea61-70d2-490b-be8e-f6b828f071d2
          role: system
          text: 你是一个精通多国语言的翻译专家。请根据“{{#sys.query#}}”中的内容，先进行语种判断，如果是中文的，请将中文翻译成英文和日文；如果是非中文的，请将所有非中文的翻译成中文。“{{#sys.query#}}”中的内容都是需要翻译的内容，你只需要输出翻译结果，不需要输出思考/think的内容，更不能进行分析和回答。翻译结果如果是中文的请符合中文的语言习惯，如果是英文或者日文也请符合相应语言的习惯。
        selected: false
        title: doubao1.5pro
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1748182242587'
      position:
        x: 363.15729879467494
        y: 619.9887789173066
      positionAbsolute:
        x: 363.15729879467494
        y: 619.9887789173066
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1748182217411.text#}}'
        desc: ''
        selected: false
        title: 直接回复 3
        type: answer
        variables: []
      height: 105
      id: '1748182933381'
      position:
        x: 662.6100159655728
        y: 416.27851799442453
      positionAbsolute:
        x: 662.6100159655728
        y: 416.27851799442453
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1748181948341.text#}}'
        desc: ''
        selected: false
        title: 直接回复 4
        type: answer
        variables: []
      height: 105
      id: '1748182936133'
      position:
        x: 652
        y: 241.54295512707392
      positionAbsolute:
        x: 652
        y: 241.54295512707392
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1748182242587.text#}}'
        desc: ''
        selected: false
        title: 直接回复 5
        type: answer
        variables: []
      height: 105
      id: '1748182938250'
      position:
        x: 662.6100159655728
        y: 610.8944795182442
      positionAbsolute:
        x: 662.6100159655728
        y: 610.8944795182442
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        advanced_settings:
          group_enabled: false
          groups:
          - groupId: 5b294b0a-924c-4391-a2bf-9d1aece5cdd1
            group_name: Group1
            output_type: string
            variables:
            - - '1748182217411'
              - text
            - - '1748182242587'
              - text
            - - llm
              - text
            - - '1748181948341'
              - text
        desc: ''
        output_type: string
        selected: false
        title: 变量聚合器
        type: variable-aggregator
        variables:
        - - '1748182217411'
          - text
        - - '1748182242587'
          - text
        - - llm
          - text
        - - '1748181948341'
          - text
      height: 174
      id: '1748183786592'
      position:
        x: 968.8467200265533
        y: 228.52271433217913
      positionAbsolute:
        x: 968.8467200265533
        y: 228.52271433217913
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '

          翻译结果：

          -Gemini2.5flash-

          {{#llm.text#}}


          -Deepseek R1-

          {{#1748181948341.text#}}


          -Qwen3-235B-A22B-

          {{#1748182217411.text#}}


          -doubao1.5Pro-

          {{#1748182242587.text#}}'
        desc: ''
        selected: false
        title: 翻译结果
        type: answer
        variables: []
      height: 194
      id: '1748188771235'
      position:
        x: 1308.0958477156062
        y: 228.52271433217913
      positionAbsolute:
        x: 1308.0958477156062
        y: 228.52271433217913
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 264.980457752205
      y: 177.7002707486618
      zoom: 0.5236470909484411
