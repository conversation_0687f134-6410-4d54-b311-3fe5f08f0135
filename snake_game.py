import tkinter as tk
import random
import math
import os
import json
from tkinter import messagebox, font, simpledialog

# 创建声音类，处理游戏音效
class SoundManager:
    def __init__(self):
        try:
            import winsound
            self.winsound = winsound
            self.sound_enabled = True
        except ImportError:
            self.sound_enabled = False
            print("声音模块导入失败: 音效功能将被禁用")
    
    def play_eat(self):
        if self.sound_enabled:
            # 播放高频声音模拟吃食物
            self.winsound.Beep(1000, 50)
    
    def play_level_up(self):
        if self.sound_enabled:
            # 播放上升音模拟升级
            for freq in range(1000, 2000, 200):
                self.winsound.Beep(freq, 50)
    
    def play_game_over(self):
        if self.sound_enabled:
            # 播放下降音模拟游戏结束
            for freq in range(1000, 500, -100):
                self.winsound.Beep(freq, 100)
    
    def play_obstacle_hit(self):
        if self.sound_enabled:
            # 播放撞击障碍物的声音
            self.winsound.Beep(200, 100)

class CyberpunkSnake:
    def __init__(self, master):
        self.master = master
        self.master.title("赛博朋克2077: 霓虹蛇行")
        self.master.resizable(False, False)
        self.master.config(bg='#000000')
        
        # 初始化音效管理器
        self.sound_manager = SoundManager()
        
        # 创建游戏区域
        self.width = 600
        self.height = 600
        self.cell_size = 20
        
        # 难度设置
        self.difficulties = {
            "简单": {"delay": 200, "description": "低速模式，新手友好", "obstacles": 3},
            "普通": {"delay": 150, "description": "标准速度，适合大多数玩家", "obstacles": 5},
            "困难": {"delay": 100, "description": "高速模式，挑战反应极限", "obstacles": 8},
            "噩梦": {"delay": 70, "description": "极速模式，仅适合赛博改造人", "obstacles": 12}
        }
        
        # 初始化动画计数器
        self.animation_counter = 0
        self.glow_intensity = 0
        self.glow_direction = 1
        
        # 赛博朋克配色方案
        self.colors = {
            "background": "#000010",
            "grid": "#041020",
            "snake_head": "#00FFFF",
            "snake_body": "#0088FF",
            "food": "#FF00FF",
            "food_glow": "#FF60FF",
            "text_neon": "#00FFFF",
            "text_pink": "#FF00FF",
            "border": "#00FFFF",
            "obstacle": "#FF3000",
            "obstacle_glow": "#FF6030",
            "moving_obstacle": "#FF9000",
            "moving_obstacle_glow": "#FFA030",
            "trap_obstacle": "#00FF00",
            "trap_obstacle_glow": "#50FF50"
        }
        
        # 加载字体
        self.score_font = font.Font(family="Arial", size=12, weight="bold")
        self.title_font = font.Font(family="Arial", size=16, weight="bold")
        
        # 加载最高分
        self.high_scores = self.load_high_scores()
        
        # 显示难度选择对话框
        self.show_difficulty_dialog()
        
        # 创建状态栏
        self.frame = tk.Frame(self.master, bg='black')
        self.frame.pack()
        
        self.title_label = tk.Label(self.frame, 
                            text="赛博朋克2077: 霓虹蛇行", 
                            fg=self.colors["text_neon"], 
                            bg='black',
                            font=self.title_font)
        self.title_label.grid(row=0, column=0, padx=10, pady=5)
        
        self.score_label = tk.Label(self.frame, 
                            text="分数: 0", 
                            fg=self.colors["text_pink"], 
                            bg='black',
                            font=self.score_font)
        self.score_label.grid(row=0, column=1, padx=10, pady=5)
        
        self.level_label = tk.Label(self.frame, 
                            text="等级: 1", 
                            fg=self.colors["text_pink"], 
                            bg='black',
                            font=self.score_font)
        self.level_label.grid(row=0, column=2, padx=10, pady=5)
        
        self.high_score_label = tk.Label(self.frame, 
                            text=f"最高分: {self.high_scores[self.difficulty]}", 
                            fg=self.colors["text_neon"], 
                            bg='black',
                            font=self.score_font)
        self.high_score_label.grid(row=0, column=3, padx=10, pady=5)
        
        self.difficulty_label = tk.Label(self.frame, 
                            text=f"难度: {self.difficulty}", 
                            fg=self.colors["text_pink"], 
                            bg='black',
                            font=self.score_font)
        self.difficulty_label.grid(row=0, column=4, padx=10, pady=5)
        
        # 创建游戏画布
        self.canvas = tk.Canvas(self.master, 
                        width=self.width, 
                        height=self.height, 
                        bg=self.colors["background"],
                        highlightbackground=self.colors["border"],
                        highlightthickness=2)
        self.canvas.pack()
        
        # 创建赛博朋克网格背景
        self.draw_grid()
        
        # 初始化游戏变量
        self.reset()
        
        # 绑定键盘事件
        self.master.bind("<Key>", self.handle_key)
        
        # 创建菜单
        self.create_menu()
        
        # 开始游戏循环
        self.update()
    
    def create_menu(self):
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)
        
        game_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="游戏", menu=game_menu)
        game_menu.add_command(label="新游戏", command=self.new_game)
        game_menu.add_command(label="切换难度", command=self.change_difficulty)
        game_menu.add_separator()
        game_menu.add_command(label="退出", command=self.master.quit)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def load_high_scores(self):
        # 尝试加载最高分文件，如果不存在则创建新的
        high_scores = {
            "简单": 0,
            "普通": 0,
            "困难": 0,
            "噩梦": 0
        }
        
        try:
            if os.path.exists("snake_high_scores.json"):
                with open("snake_high_scores.json", "r") as f:
                    saved_scores = json.load(f)
                    for difficulty in high_scores:
                        if difficulty in saved_scores:
                            high_scores[difficulty] = saved_scores[difficulty]
        except Exception as e:
            print(f"加载最高分失败: {e}")
        
        return high_scores
    
    def save_high_scores(self):
        try:
            with open("snake_high_scores.json", "w") as f:
                json.dump(self.high_scores, f)
        except Exception as e:
            print(f"保存最高分失败: {e}")
    
    def show_difficulty_dialog(self):
        difficulty_options = list(self.difficulties.keys())
        difficulty_dialog = tk.Toplevel(self.master)
        difficulty_dialog.title("选择难度")
        difficulty_dialog.geometry("300x250")
        difficulty_dialog.resizable(False, False)
        difficulty_dialog.config(bg='#000020')
        difficulty_dialog.transient(self.master)
        difficulty_dialog.grab_set()
        
        tk.Label(difficulty_dialog, 
                 text="选择游戏难度", 
                 fg="#00FFFF", 
                 bg="#000020",
                 font=("Arial", 14, "bold")).pack(pady=10)
        
        selected_difficulty = tk.StringVar(value="普通")
        
        for difficulty in difficulty_options:
            frame = tk.Frame(difficulty_dialog, bg="#000020")
            frame.pack(fill="x", padx=20, pady=5)
            
            rb = tk.Radiobutton(frame, 
                                text=difficulty, 
                                value=difficulty,
                                variable=selected_difficulty,
                                fg="#FFFFFF",
                                bg="#000020",
                                selectcolor="#000080",
                                font=("Arial", 12))
            rb.pack(side="left")
            
            desc_label = tk.Label(frame, 
                                  text=self.difficulties[difficulty]["description"],
                                  fg="#888888",
                                  bg="#000020",
                                  font=("Arial", 10))
            desc_label.pack(side="left", padx=10)
        
        def set_difficulty():
            self.difficulty = selected_difficulty.get()
            self.delay = self.difficulties[self.difficulty]["delay"]
            difficulty_dialog.destroy()
        
        tk.Button(difficulty_dialog, 
                  text="开始游戏", 
                  command=set_difficulty,
                  bg="#000080",
                  fg="#FFFFFF",
                  activebackground="#0000FF",
                  activeforeground="#FFFFFF",
                  font=("Arial", 12)).pack(pady=15)
        
        # 等待对话框关闭
        self.master.wait_window(difficulty_dialog)
    
    def change_difficulty(self):
        # 暂停游戏
        old_paused = self.paused
        self.paused = True
        
        # 显示难度选择对话框
        self.show_difficulty_dialog()
        
        # 更新难度标签
        self.difficulty_label.config(text=f"难度: {self.difficulty}")
        self.high_score_label.config(text=f"最高分: {self.high_scores[self.difficulty]}")
        
        # 如果游戏不是原来就暂停的，就继续游戏
        if not old_paused:
            self.paused = False
            self.update()
    
    def show_help(self):
        help_text = """
游戏操作:
- 使用方向键 (↑ ↓ ← →) 控制蛇的移动
- 空格键暂停/继续游戏
- 按ESC键返回菜单

游戏规则:
- 吃到食物得10分
- 每50分升一级，游戏加速
- 每升两级会新增障碍物

障碍物类型:
- 红色方块：静态障碍物，撞上就结束
- 橙色菱形：移动障碍物，会沿指定方向移动
- 绿色三角：陷阱障碍物，当蛇接近时会闪烁

- 撞到墙壁、自己或任何障碍物会导致游戏结束
        """
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        about_text = """
赛博朋克2077: 霓虹蛇行
版本: 1.0.0

一个具有赛博朋克风格的贪吃蛇游戏
灵感来自《赛博朋克2077》

©2024 夜之城数字娱乐
        """
        messagebox.showinfo("关于", about_text)
    
    def new_game(self):
        self.reset()
    
    def draw_grid(self):
        # 绘制网格线
        for i in range(0, self.width, self.cell_size):
            alpha = 255 if i % (self.cell_size * 5) == 0 else 100
            grid_color = self.colors["grid"]
            self.canvas.create_line(i, 0, i, self.height, fill=grid_color, width=1, tags="grid")
        
        for i in range(0, self.height, self.cell_size):
            alpha = 255 if i % (self.cell_size * 5) == 0 else 100
            grid_color = self.colors["grid"]
            self.canvas.create_line(0, i, self.width, i, fill=grid_color, width=1, tags="grid")
    
    def reset(self):
        # 重置游戏状态
        self.direction = "Right"
        self.next_direction = "Right"
        self.score = 0
        self.level = 1
        self.game_over = False
        self.paused = False
        self.animation_counter = 0
        
        # 更新分数和等级显示
        self.score_label.config(text=f"分数: {self.score}")
        self.level_label.config(text=f"等级: {self.level}")
        
        # 创建蛇
        self.snake = [(100, 100), (80, 100), (60, 100)]
        
        # 创建食物 - 先初始化食物，再生成障碍物
        self.food = None
        self.create_food_initial()  # 使用一个特殊方法来初始化食物
        
        # 创建障碍物列表 - 现在包含障碍物类型
        # 每个障碍物是一个元组: (x, y, 类型, 移动方向, 移动计数器)
        # 类型: 0 = 静态, 1 = 移动, 2 = 陷阱
        self.obstacles = []
        
        # 生成初始障碍物
        self.generate_obstacles(self.difficulties[self.difficulty]["obstacles"])
        
        # 清除画布
        self.canvas.delete("all")
        self.draw_grid()
        
        # 绘制蛇
        self.draw_snake()
        
        # 绘制障碍物
        self.draw_obstacles()
        
        # 绘制食物
        self.draw_food()
    
    def create_food_initial(self):
        """初始化食物位置，用于游戏启动时"""
        # 设置初始食物位置 - 在蛇头前方的位置
        head_x, head_y = self.snake[0]
        food_x = (head_x + 5 * self.cell_size) % self.width
        food_y = head_y
        self.food = (food_x, food_y)
    
    def generate_obstacles(self, count):
        """生成指定数量的障碍物，包括不同类型"""
        self.obstacles = []
        
        # 地图可放置位置的网格数
        grid_width = self.width // self.cell_size
        grid_height = self.height // self.cell_size
        
        # 计算安全区域（游戏开始时蛇周围区域）
        safe_zone = [(x, y) for x in range(40, 160, self.cell_size) 
                          for y in range(80, 120, self.cell_size)]
        
        # 确保食物位置不会生成障碍物
        if self.food:
            safe_zone.append(self.food)
        
        for i in range(count):
            while True:
                # 随机生成障碍物位置
                x = random.randint(0, grid_width - 1) * self.cell_size
                y = random.randint(0, grid_height - 1) * self.cell_size
                
                # 确保障碍物不会生成在蛇身上、食物上或其他障碍物上
                # 同时避开蛇的初始位置周围
                if ((x, y) not in [(obs[0], obs[1]) for obs in self.obstacles] and
                    (x, y) not in self.snake and 
                    (x, y) != self.food and 
                    (x, y) not in safe_zone):
                    
                    # 随机决定障碍物类型
                    # 难度越高，出现移动和陷阱障碍物的概率越大
                    if self.difficulty == "简单":
                        type_chance = random.random()
                        if type_chance < 0.9:  # 90%是静态障碍物
                            obstacle_type = 0  # 静态
                        elif type_chance < 0.95:  # 5%是移动障碍物
                            obstacle_type = 1  # 移动
                        else:  # 5%是陷阱障碍物
                            obstacle_type = 2  # 陷阱
                    elif self.difficulty == "普通":
                        type_chance = random.random()
                        if type_chance < 0.8:  # 80%是静态障碍物
                            obstacle_type = 0
                        elif type_chance < 0.9:  # 10%是移动障碍物
                            obstacle_type = 1
                        else:  # 10%是陷阱障碍物
                            obstacle_type = 2
                    elif self.difficulty == "困难":
                        type_chance = random.random()
                        if type_chance < 0.7:  # 70%是静态障碍物
                            obstacle_type = 0
                        elif type_chance < 0.85:  # 15%是移动障碍物
                            obstacle_type = 1
                        else:  # 15%是陷阱障碍物
                            obstacle_type = 2
                    else:  # 噩梦难度
                        type_chance = random.random()
                        if type_chance < 0.6:  # 60%是静态障碍物
                            obstacle_type = 0
                        elif type_chance < 0.8:  # 20%是移动障碍物
                            obstacle_type = 1
                        else:  # 20%是陷阱障碍物
                            obstacle_type = 2
                    
                    # 对于移动障碍物，随机选择一个移动方向
                    # 0: 上, 1: 右, 2: 下, 3: 左
                    if obstacle_type == 1:
                        direction = random.randint(0, 3)
                    else:
                        direction = -1  # 非移动障碍物没有方向
                    
                    # 添加障碍物: (x, y, 类型, 移动方向, 移动计数器)
                    self.obstacles.append((x, y, obstacle_type, direction, 0))
                    break
    
    def draw_obstacles(self):
        """绘制障碍物，根据不同类型有不同的视觉效果"""
        for obstacle in self.obstacles:
            x, y, obstacle_type, _, _ = obstacle
            
            # 根据障碍物类型选择颜色
            if obstacle_type == 0:  # 静态障碍物
                color = self.colors["obstacle"]
                glow_color = self.colors["obstacle_glow"]
                shape = "rectangle"  # 方形
            elif obstacle_type == 1:  # 移动障碍物
                color = self.colors["moving_obstacle"]
                glow_color = self.colors["moving_obstacle_glow"]
                shape = "diamond"  # 菱形
            else:  # 陷阱障碍物
                color = self.colors["trap_obstacle"]
                glow_color = self.colors["trap_obstacle_glow"]
                shape = "triangle"  # 三角形
            
            # 创建带有辉光效果的障碍物
            glow_size = 2
            if shape == "rectangle":
                # 矩形障碍物
                self.canvas.create_rectangle(
                    x - glow_size, y - glow_size,
                    x + self.cell_size + glow_size, y + self.cell_size + glow_size,
                    fill="", outline=glow_color, width=2, tags="obstacle_glow"
                )
                
                self.canvas.create_rectangle(
                    x, y,
                    x + self.cell_size, y + self.cell_size,
                    fill=color, outline="#FFFFFF", width=1, tags="obstacle"
                )
            elif shape == "diamond":
                # 菱形障碍物（移动障碍物）
                center_x = x + self.cell_size / 2
                center_y = y + self.cell_size / 2
                size = self.cell_size / 2 + glow_size
                
                # 辉光
                self.canvas.create_polygon(
                    center_x, center_y - size,  # 上
                    center_x + size, center_y,  # 右
                    center_x, center_y + size,  # 下
                    center_x - size, center_y,  # 左
                    fill="", outline=glow_color, width=2, tags="obstacle_glow"
                )
                
                # 菱形主体
                size = self.cell_size / 2
                self.canvas.create_polygon(
                    center_x, center_y - size,  # 上
                    center_x + size, center_y,  # 右
                    center_x, center_y + size,  # 下
                    center_x - size, center_y,  # 左
                    fill=color, outline="#FFFFFF", width=1, tags="obstacle"
                )
            elif shape == "triangle":
                # 三角形障碍物（陷阱障碍物）
                center_x = x + self.cell_size / 2
                top_y = y
                bottom_y = y + self.cell_size
                
                # 辉光
                self.canvas.create_polygon(
                    center_x, top_y - glow_size,  # 上
                    x + self.cell_size + glow_size, bottom_y + glow_size,  # 右下
                    x - glow_size, bottom_y + glow_size,  # 左下
                    fill="", outline=glow_color, width=2, tags="obstacle_glow"
                )
                
                # 三角形主体
                self.canvas.create_polygon(
                    center_x, top_y,  # 上
                    x + self.cell_size, bottom_y,  # 右下
                    x, bottom_y,  # 左下
                    fill=color, outline="#FFFFFF", width=1, tags="obstacle"
                )
                
            # 对于移动障碍物，添加方向指示器
            if obstacle_type == 1:
                center_x = x + self.cell_size / 2
                center_y = y + self.cell_size / 2
                direction = obstacle[3]
                
                # 根据方向绘制小箭头
                arrow_size = self.cell_size / 5
                if direction == 0:  # 上
                    self.canvas.create_line(
                        center_x, center_y + arrow_size,
                        center_x, center_y - arrow_size,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                    self.canvas.create_line(
                        center_x, center_y - arrow_size,
                        center_x - arrow_size/2, center_y - arrow_size/2,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                    self.canvas.create_line(
                        center_x, center_y - arrow_size,
                        center_x + arrow_size/2, center_y - arrow_size/2,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                elif direction == 1:  # 右
                    self.canvas.create_line(
                        center_x - arrow_size, center_y,
                        center_x + arrow_size, center_y,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                    self.canvas.create_line(
                        center_x + arrow_size, center_y,
                        center_x + arrow_size/2, center_y - arrow_size/2,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                    self.canvas.create_line(
                        center_x + arrow_size, center_y,
                        center_x + arrow_size/2, center_y + arrow_size/2,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                elif direction == 2:  # 下
                    self.canvas.create_line(
                        center_x, center_y - arrow_size,
                        center_x, center_y + arrow_size,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                    self.canvas.create_line(
                        center_x, center_y + arrow_size,
                        center_x - arrow_size/2, center_y + arrow_size/2,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                    self.canvas.create_line(
                        center_x, center_y + arrow_size,
                        center_x + arrow_size/2, center_y + arrow_size/2,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                elif direction == 3:  # 左
                    self.canvas.create_line(
                        center_x + arrow_size, center_y,
                        center_x - arrow_size, center_y,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                    self.canvas.create_line(
                        center_x - arrow_size, center_y,
                        center_x - arrow_size/2, center_y - arrow_size/2,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
                    self.canvas.create_line(
                        center_x - arrow_size, center_y,
                        center_x - arrow_size/2, center_y + arrow_size/2,
                        fill="#FFFFFF", width=2, tags="obstacle"
                    )
    
    def update_obstacles(self):
        """更新障碍物状态，处理移动障碍物和陷阱障碍物的特殊行为"""
        new_obstacles = []
        
        for obstacle in self.obstacles:
            x, y, obstacle_type, direction, counter = obstacle
            
            if obstacle_type == 1:  # 移动障碍物
                # 每隔一段时间移动一次
                if counter >= 15:  # 每15帧移动一次
                    counter = 0
                    
                    # 根据方向移动
                    if direction == 0:  # 上
                        y = (y - self.cell_size) % self.height
                    elif direction == 1:  # 右
                        x = (x + self.cell_size) % self.width
                    elif direction == 2:  # 下
                        y = (y + self.cell_size) % self.height
                    elif direction == 3:  # 左
                        x = (x - self.cell_size) % self.width
                else:
                    counter += 1
            
            elif obstacle_type == 2:  # 陷阱障碍物
                # 陷阱障碍物在蛇接近时会闪烁
                # 闪烁用counter模拟
                counter = (counter + 1) % 40  # 40帧一个完整闪烁周期
            
            # 检查移动后的障碍物是否与蛇或其他障碍物重叠
            # 如果重叠，需要处理（例如改变方向或不移动）
            # 这里简化处理，如果与蛇头重叠就直接导致游戏结束
            if (x, y) == self.snake[0]:
                self.game_over = True
                self.sound_manager.play_game_over()
                break
            
            # 更新障碍物信息
            new_obstacles.append((x, y, obstacle_type, direction, counter))
        
        self.obstacles = new_obstacles
    
    def move_snake(self):
        # 移动蛇
        head_x, head_y = self.snake[0]
        
        # 根据方向确定新的头部位置
        if self.direction == "Right":
            new_head = (head_x + self.cell_size, head_y)
        elif self.direction == "Left":
            new_head = (head_x - self.cell_size, head_y)
        elif self.direction == "Up":
            new_head = (head_x, head_y - self.cell_size)
        elif self.direction == "Down":
            new_head = (head_x, head_y + self.cell_size)
        
        # 检查碰撞
        if self.check_collision(new_head):
            self.game_over = True
            self.sound_manager.play_game_over()
            return
        
        # 将新头部添加到蛇的列表
        self.snake.insert(0, new_head)
        
        # 检查是否吃到食物
        if new_head == self.food:
            # 播放吃食物音效
            self.sound_manager.play_eat()
            
            # 增加分数
            self.score += 10
            self.score_label.config(text=f"分数: {self.score}")
            
            # 升级判断
            if self.score % 50 == 0:
                self.level += 1
                self.level_label.config(text=f"等级: {self.level}")
                # 提高速度
                self.delay = max(50, self.delay - 10)
                
                # 播放升级音效
                self.sound_manager.play_level_up()
                
                # 显示升级信息
                self.show_level_up_message()
            
            # 创建新食物
            self.create_food()
            
            # 绘制新食物
            self.draw_food()
            
            # 播放霓虹脉冲动画
            self.play_pulse_animation()
        else:
            # 如果没有吃到食物，移除尾部
            self.snake.pop()
    
    def play_pulse_animation(self):
        # 创建从蛇头向外扩散的霓虹脉冲效果
        head_x, head_y = self.snake[0]
        center_x = head_x + self.cell_size / 2
        center_y = head_y + self.cell_size / 2
        
        for radius in range(5, 40, 5):
            # 创建脉冲圆圈
            pulse = self.canvas.create_oval(
                center_x - radius, center_y - radius,
                center_x + radius, center_y + radius,
                outline=self.colors["food"],
                width=2,
                tags="pulse"
            )
            
            # 延迟后淡出脉冲
            self.master.after(radius * 10, lambda p=pulse: self.canvas.delete(p))
    
    def show_level_up_message(self):
        # 显示升级消息
        message = self.canvas.create_text(
            self.width // 2, self.height // 2,
            text=f"升级！ 等级 {self.level}",
            fill=self.colors["text_neon"],
            font=("Arial", 20, "bold"),
            tags="level_message"
        )
        
        # 随着等级提高，增加障碍物
        if self.level > 1 and self.level % 2 == 0:  # 每两级增加一个障碍物
            obstacle_count = min(2, self.level // 2)  # 每次最多增加2个
            self.add_obstacles(obstacle_count)
            
            # 显示障碍物增加信息
            obstacle_msg = self.canvas.create_text(
                self.width // 2, self.height // 2 + 30,
                text=f"警告：新增 {obstacle_count} 个障碍物！",
                fill=self.colors["obstacle"],
                font=("Arial", 14, "bold"),
                tags="level_message"
            )
            
            # 2秒后删除障碍物信息
            self.master.after(1500, lambda: self.canvas.delete(obstacle_msg))
        
        # 2秒后删除升级消息
        self.master.after(1500, lambda: self.canvas.delete(message))
    
    def add_obstacles(self, count):
        """
        在游戏过程中增加新的障碍物
        """
        grid_width = self.width // self.cell_size
        grid_height = self.height // self.cell_size
        
        # 获取蛇头位置，设定安全区域（蛇头周围不生成障碍物）
        head_x, head_y = self.snake[0]
        safe_distance = 5 * self.cell_size  # 安全区域半径
        
        for _ in range(count):
            attempts = 0
            while attempts < 100:  # 最多尝试100次
                # 随机生成障碍物位置
                x = random.randint(0, grid_width - 1) * self.cell_size
                y = random.randint(0, grid_height - 1) * self.cell_size
                
                # 计算与蛇头的距离
                distance = math.sqrt((x - head_x)**2 + (y - head_y)**2)
                
                # 确保障碍物不会生成在蛇身上、食物上、其他障碍物上，以及蛇头附近
                if ((x, y) not in self.snake and 
                    (x, y) != self.food and 
                    (x, y) not in [(obs[0], obs[1]) for obs in self.obstacles] and
                    distance > safe_distance):
                    
                    # 随机决定障碍物类型
                    # 等级越高，出现特殊障碍物的概率越大
                    type_chance = random.random()
                    if self.level < 3:
                        obstacle_type = 0  # 低等级只生成静态障碍物
                    elif self.level < 5:
                        obstacle_type = 1 if type_chance < 0.3 else 0  # 30%几率生成移动障碍物
                    else:
                        if type_chance < 0.4:  # 40%是静态障碍物
                            obstacle_type = 0
                        elif type_chance < 0.7:  # 30%是移动障碍物
                            obstacle_type = 1
                        else:  # 30%是陷阱障碍物
                            obstacle_type = 2
                    
                    # 对于移动障碍物，随机选择一个移动方向
                    # 0: 上, 1: 右, 2: 下, 3: 左
                    if obstacle_type == 1:
                        direction = random.randint(0, 3)
                    else:
                        direction = -1  # 非移动障碍物没有方向
                    
                    # 添加障碍物: (x, y, 类型, 移动方向, 移动计数器)
                    self.obstacles.append((x, y, obstacle_type, direction, 0))
                    break
                
                attempts += 1
    
    def check_collision(self, position):
        # 检查蛇是否碰到边界
        x, y = position
        if x < 0 or x >= self.width or y < 0 or y >= self.height:
            return True
        
        # 检查蛇是否碰到自己
        if position in self.snake[:-1]:
            return True
        
        # 检查蛇是否碰到障碍物
        for obs_x, obs_y, obs_type, _, _ in self.obstacles:
            if position == (obs_x, obs_y):
                self.sound_manager.play_obstacle_hit()
                return True
        
        return False
    
    def create_food(self):
        # 随机生成食物位置
        while True:
            x = random.randint(0, (self.width - self.cell_size) // self.cell_size) * self.cell_size
            y = random.randint(0, (self.height - self.cell_size) // self.cell_size) * self.cell_size
            
            # 确保食物不会生成在蛇身上或障碍物上
            if (x, y) not in self.snake and (x, y) not in [(obs[0], obs[1]) for obs in self.obstacles]:
                self.food = (x, y)
                break
    
    def draw_food(self):
        # 绘制食物
        x, y = self.food
        
        # 创建一个脉动辉光效果的食物
        self.glow_intensity += 0.05 * self.glow_direction
        if self.glow_intensity >= 1.0:
            self.glow_direction = -1
        elif self.glow_intensity <= 0.4:
            self.glow_direction = 1
            
        glow_size = 2 + int(2 * self.glow_intensity)
        glow_color = self.colors["food_glow"]
        
        # 绘制外部辉光
        self.canvas.create_oval(
            x - glow_size, y - glow_size,
            x + self.cell_size + glow_size, y + self.cell_size + glow_size,
            fill="", outline=glow_color, width=2, tags="food_glow"
        )
        
        # 绘制食物主体
        self.canvas.create_oval(
            x, y,
            x + self.cell_size, y + self.cell_size,
            fill=self.colors["food"], outline="#FFFFFF", width=1, tags="food"
        )
    
    def draw_snake(self):
        # 绘制蛇头
        head_x, head_y = self.snake[0]
        
        # 绘制带有辉光效果的蛇头
        glow_size = 2
        self.canvas.create_rectangle(
            head_x - glow_size, head_y - glow_size,
            head_x + self.cell_size + glow_size, head_y + self.cell_size + glow_size,
            fill="", outline=self.colors["snake_head"], width=2, tags="snake_glow"
        )
        
        self.canvas.create_rectangle(
            head_x, head_y,
            head_x + self.cell_size, head_y + self.cell_size,
            fill=self.colors["snake_head"], outline="white", width=1, tags="snake"
        )
        
        # 绘制蛇身，呈现霓虹蓝向深蓝的渐变效果
        for i, segment in enumerate(self.snake[1:]):
            x, y = segment
            
            # 计算颜色渐变
            r = 0
            g = max(136 - i * 5, 0)
            b = max(255 - i * 10, 20)
            color = f'#{r:02x}{g:02x}{b:02x}'
            
            self.canvas.create_rectangle(
                x, y,
                x + self.cell_size, y + self.cell_size,
                fill=color, outline="white", width=1, tags="snake"
            )
    
    def update(self):
        if not self.game_over and not self.paused:
            # 动画计数器
            self.animation_counter += 1
            
            # 清除画布上的蛇、障碍物和食物
            self.canvas.delete("snake")
            self.canvas.delete("snake_glow")
            self.canvas.delete("obstacle")
            self.canvas.delete("obstacle_glow")
            self.canvas.delete("food")
            self.canvas.delete("food_glow")
            
            # 更新障碍物状态
            self.update_obstacles()
            
            # 更新方向
            self.direction = self.next_direction
            
            # 移动蛇
            self.move_snake()
            
            # 如果游戏还没结束，重新绘制蛇、障碍物和食物
            if not self.game_over:
                self.draw_snake()
                self.draw_obstacles()
                self.draw_food()
        
        # 如果游戏结束，显示游戏结束信息
        if self.game_over:
            # 检查是否为新高分
            if self.score > self.high_scores[self.difficulty]:
                self.high_scores[self.difficulty] = self.score
                self.high_score_label.config(text=f"最高分: {self.high_scores[self.difficulty]}")
                self.save_high_scores()
                self.show_new_high_score()
            else:
                self.game_over_message()
        else:
            # 继续游戏循环
            self.master.after(self.delay, self.update)
    
    def show_new_high_score(self):
        # 清除游戏结束消息
        self.canvas.delete("game_over")
        self.canvas.delete("game_over_bg")
        
        # 绘制半透明黑色背景
        self.canvas.create_rectangle(
            0, 0, self.width, self.height,
            fill="#000000", stipple="gray50",
            tags="high_score_bg"
        )
        
        # 绘制新高分消息
        self.canvas.create_text(
            self.width // 2, self.height // 2 - 60,
            text="新纪录！",
            fill=self.colors["text_pink"],
            font=("Arial", 36, "bold"),
            tags="high_score"
        )
        
        self.canvas.create_text(
            self.width // 2, self.height // 2,
            text=f"最高分: {self.score}",
            fill=self.colors["text_neon"],
            font=("Arial", 24),
            tags="high_score"
        )
        
        self.canvas.create_text(
            self.width // 2, self.height // 2 + 60,
            text="按任意键重新开始",
            fill="#FFFFFF",
            font=("Arial", 14),
            tags="high_score"
        )
        
        # 闪烁效果
        self.master.after(500, self.flash_high_score)
    
    def flash_high_score(self):
        if not self.game_over:
            return
        
        self.canvas.delete("high_score")
        self.show_new_high_score()
    
    def handle_key(self, event):
        key = event.keysym
        
        # 游戏结束时，任意键重新开始
        if self.game_over:
            self.reset()
            self.update()
            return
        
        # ESC键显示菜单
        if key == "Escape":
            self.paused = True
            self.show_pause_menu()
            return
        
        # 空格键暂停/继续游戏
        if key == "space":
            self.paused = not self.paused
            
            # 显示暂停信息
            if self.paused:
                self.pause_text = self.canvas.create_text(
                    self.width // 2, self.height // 2,
                    text="暂停",
                    fill=self.colors["text_neon"],
                    font=("Arial", 24, "bold"),
                    tags="pause"
                )
            else:
                self.canvas.delete("pause")
                self.update()
            return
        
        # 改变方向
        # 禁止蛇掉头（例如，如果蛇正在向右移动，不能直接改变方向为向左）
        if key == "Up" and self.direction != "Down":
            self.next_direction = "Up"
        elif key == "Down" and self.direction != "Up":
            self.next_direction = "Down"
        elif key == "Left" and self.direction != "Right":
            self.next_direction = "Left"
        elif key == "Right" and self.direction != "Left":
            self.next_direction = "Right"
    
    def show_pause_menu(self):
        # 显示暂停菜单
        pause_menu = tk.Toplevel(self.master)
        pause_menu.title("游戏暂停")
        pause_menu.geometry("250x200")
        pause_menu.resizable(False, False)
        pause_menu.config(bg='#000020')
        pause_menu.transient(self.master)
        pause_menu.grab_set()
        
        tk.Label(pause_menu, 
                 text="游戏暂停", 
                 fg="#00FFFF", 
                 bg="#000020",
                 font=("Arial", 16, "bold")).pack(pady=10)
        
        # 继续游戏按钮
        tk.Button(pause_menu, 
                  text="继续游戏", 
                  command=lambda: self.resume_game(pause_menu),
                  bg="#000080",
                  fg="#FFFFFF",
                  activebackground="#0000FF",
                  activeforeground="#FFFFFF",
                  width=15,
                  font=("Arial", 12)).pack(pady=5)
        
        # 新游戏按钮
        tk.Button(pause_menu, 
                  text="新游戏", 
                  command=lambda: self.new_game_from_menu(pause_menu),
                  bg="#000080",
                  fg="#FFFFFF",
                  activebackground="#0000FF",
                  activeforeground="#FFFFFF",
                  width=15,
                  font=("Arial", 12)).pack(pady=5)
        
        # 更改难度按钮
        tk.Button(pause_menu, 
                  text="更改难度", 
                  command=lambda: self.change_difficulty_from_menu(pause_menu),
                  bg="#000080",
                  fg="#FFFFFF",
                  activebackground="#0000FF",
                  activeforeground="#FFFFFF",
                  width=15,
                  font=("Arial", 12)).pack(pady=5)
        
        # 退出游戏按钮
        tk.Button(pause_menu, 
                  text="退出游戏", 
                  command=self.master.quit,
                  bg="#800000",
                  fg="#FFFFFF",
                  activebackground="#FF0000",
                  activeforeground="#FFFFFF",
                  width=15,
                  font=("Arial", 12)).pack(pady=5)
    
    def resume_game(self, menu):
        menu.destroy()
        self.canvas.delete("pause")
        self.paused = False
        self.update()
    
    def new_game_from_menu(self, menu):
        menu.destroy()
        self.reset()
        self.paused = False
        self.update()
    
    def change_difficulty_from_menu(self, menu):
        menu.destroy()
        self.change_difficulty()
    
    def game_over_message(self):
        # 绘制半透明黑色背景
        self.canvas.create_rectangle(
            0, 0, self.width, self.height,
            fill="#000000", stipple="gray50",
            tags="game_over_bg"
        )
        
        # 绘制闪烁的"GAME OVER"文本
        self.animation_counter += 1
        if self.animation_counter % 20 < 10:
            text_color = self.colors["text_pink"]
        else:
            text_color = self.colors["text_neon"]
            
        self.canvas.create_text(
            self.width // 2, self.height // 2 - 40,
            text="系统崩溃",
            fill=text_color,
            font=("Arial", 30, "bold"),
            tags="game_over"
        )
        
        self.canvas.create_text(
            self.width // 2, self.height // 2 + 10,
            text=f"最终得分: {self.score}",
            fill=self.colors["text_neon"],
            font=("Arial", 18),
            tags="game_over"
        )
        
        self.canvas.create_text(
            self.width // 2, self.height // 2 + 50,
            text="按任意键重启系统",
            fill="#FFFFFF",
            font=("Arial", 14),
            tags="game_over"
        )
        
        # 闪烁效果
        self.master.after(500, self.flash_game_over)
    
    def flash_game_over(self):
        if not self.game_over:
            return
        
        self.canvas.delete("game_over")
        self.game_over_message()

if __name__ == "__main__":
    root = tk.Tk()
    game = CyberpunkSnake(root)
    root.mainloop() 