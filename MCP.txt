{
  "mcpServers": {
    "Sequential Thinking": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-sequential-thinking",
        "--config",
        "\"{}\""
      ],
      "disabled": false,
      "autoApprove": [
        "sequentialthinking",
        "sequentialthinking"
      ]
    },
    "context7-mcp": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@upstash/context7-mcp",
        "--key",
        "b78971c8-a620-4979-b359-a5a162e5e50c",
        "--profile",
        "zonal-asp-Icf58B"
      ],
      "disabled": false,
      "autoApprove": []
    },
    "files": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "C:/Users/<USER>/Downloads/AI写代码"
      ],
      "disabled": false,
      "autoApprove": []
    },
    "excel": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "--yes",
        "@negokaz/excel-mcp-server"
      ],
      "env": {
        "EXCEL_MCP_PAGING_CELLS_LIMIT": "4000"
      },
      "disabled": false,
      "autoApprove": []
    },
    "quickchart-server": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@gongrzhe/quickchart-mcp-server"
      ],
      "disabled": false,
      "autoApprove": []
    },
    "n8n-local": {
      "command": "node",
      "args": [
        "C:/Users/<USER>/Downloads/AI写代码/n8n-mcp-server/build/index.js"
      ],
      "env": {
        "N8N_API_URL": "http://n8n-wdzohslb.us-east-1.clawcloudrun.com/api/v1",
        "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MDAxNjM2Yi0wNGM3LTRiM2UtYmEzMy1hZTBmMTNjY2RjZDciLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUzODc4NjUyfQ.oBQ9YSheS6n1wGc0qsAbtFj--Dq3LCJfgZc4MwHwEuA"
      }
    },
    "hotnews": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@wopal/mcp-server-hotnews",
        "--config",
        "\"{}\""
      ],
      "disabled": false,
      "autoApprove": [
        "get_hot_news"
      ]
    }
  }
}





TRAE：

{
  "mcpServers": {
    "Sequential Thinking": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-sequential-thinking",
        "--config",
        "\"{}\""
      ]
    }
  }
}


{
  "mcpServers": {
    "files": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "C:/Users/<USER>/Downloads/AI/pythonProject1"
      ]
    }
  }
}

{
  "mcpServers": {
    "hotnews": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@wopal/mcp-server-hotnews",
        "--config",
        "\"{}\""
      ]
    }
  }
}

{
  "mcpServers": {
    "quickchart-server": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@gongrzhe/quickchart-mcp-server"
      ]
    }
  }
}

{
  "mcpServers": {
    "excel": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "--yes",
        "@negokaz/excel-mcp-server"
      ],
      "env": {
        "EXCEL_MCP_PAGING_CELLS_LIMIT": "4000"
      }
    }
  }
}

{
  "mcpServers": {
    "context7-mcp": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@upstash/context7-mcp",
        "--key",
        "b78971c8-a620-4979-b359-a5a162e5e50c",
        "--profile",
        "zonal-asp-Icf58B"
      ]
    }
  }
}

{
  "mcpServers": {
    "n8n-local": {
      "command": "node",
      "args": [
        "C:/Users/<USER>/Downloads/AI写代码/n8n-mcp-server/build/index.js"
      ],
      "env": {
        "N8N_API_URL": "http://n8n-wdzohslb.us-east-1.clawcloudrun.com/api/v1",
        "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MDAxNjM2Yi0wNGM3LTRiM2UtYmEzMy1hZTBmMTNjY2RjZDciLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUzODc4NjUyfQ.oBQ9YSheS6n1wGc0qsAbtFj--Dq3LCJfgZc4MwHwEuA"
      }
    }
  }
}

