import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import jieba
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QLabel, QTableWidget, 
                           QTableWidgetItem, QTabWidget, QComboBox, QSpinBox,
                           QMessageBox, QFileDialog, QProgressBar, QSplitter,
                           QFrame, QGridLayout, QLineEdit, QTextBrowser, QHeaderView)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QUrl
from PyQt5.QtGui import QFont, QColor, QIcon, QDesktopServices
import time
from datetime import datetime
import json
import logging
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ui.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("HotNewsUI")

# 导入爬虫和分析器
from crawler import HotNewsCrawler
from analyzer import HotNewsAnalyzer

# 爬虫线程类
class CrawlerThread(QThread):
    """爬虫线程，用于在后台执行爬虫任务"""
    update_progress = pyqtSignal(int, str)  # 更新进度的信号
    crawl_finished = pyqtSignal(object)    # 爬取完成的信号
    
    def __init__(self, limit=100):
        super().__init__()
        self.limit = limit
        self.crawler = HotNewsCrawler()
        
    def run(self):
        """执行爬虫任务"""
        try:
            # 知乎热榜
            self.update_progress.emit(0, "正在爬取知乎热榜...")
            zhihu_data = self.crawler.fetch_zhihu_hot(self.limit)
            self.update_progress.emit(20, f"已获取知乎热榜 {len(zhihu_data)} 条")
            
            # 微博热搜
            self.update_progress.emit(20, "正在爬取微博热搜...")
            weibo_data = self.crawler.fetch_weibo_hot(self.limit)
            self.update_progress.emit(40, f"已获取微博热搜 {len(weibo_data)} 条")
            
            # 百度热搜
            self.update_progress.emit(40, "正在爬取百度热搜...")
            baidu_data = self.crawler.fetch_baidu_hot(self.limit)
            self.update_progress.emit(60, f"已获取百度热搜 {len(baidu_data)} 条")
            
            # B站热门
            self.update_progress.emit(60, "正在爬取B站热门...")
            bilibili_data = self.crawler.fetch_bilibili_hot(self.limit)
            self.update_progress.emit(80, f"已获取B站热门 {len(bilibili_data)} 条")
            
            # 头条热点
            self.update_progress.emit(80, "正在爬取今日头条热点...")
            toutiao_data = self.crawler.fetch_toutiao_hot(self.limit)
            self.update_progress.emit(90, f"已获取头条热点 {len(toutiao_data)} 条")
            
            # 抖音热点
            self.update_progress.emit(90, "正在爬取抖音热点...")
            douyin_data = self.crawler.fetch_douyin_hot(self.limit)
            self.update_progress.emit(100, f"已获取抖音热点 {len(douyin_data)} 条")
            
            # 合并数据
            all_data = zhihu_data + weibo_data + baidu_data + bilibili_data + toutiao_data + douyin_data
            
            # 添加爬取时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            for item in all_data:
                item['crawl_time'] = current_time
            
            self.update_progress.emit(100, f"爬取完成，共获取 {len(all_data)} 条热点新闻")
            self.crawl_finished.emit(all_data)
            
        except Exception as e:
            logger.error(f"爬虫线程出错: {str(e)}")
            self.update_progress.emit(0, f"爬取出错: {str(e)}")


# 分析线程类
class AnalyzerThread(QThread):
    """分析线程，用于在后台执行数据分析任务"""
    update_progress = pyqtSignal(int, str)  # 更新进度的信号
    analysis_finished = pyqtSignal(object)  # 分析完成的信号
    
    def __init__(self, news_data, top_n=100):
        super().__init__()
        self.news_data = news_data
        self.top_n = top_n
        self.analyzer = HotNewsAnalyzer(news_data)
        
    def run(self):
        """执行分析任务"""
        try:
            # 数据预处理
            self.update_progress.emit(0, "正在进行数据预处理...")
            self.analyzer.preprocess_data()
            self.update_progress.emit(20, "数据预处理完成")
            
            # 热度归一化
            self.update_progress.emit(20, "正在归一化热度数据...")
            self.analyzer.normalize_heat()
            self.update_progress.emit(40, "热度归一化完成")
            
            # 获取热门新闻
            self.update_progress.emit(40, "正在分析热门新闻...")
            top_news = self.analyzer.get_top_news(self.top_n)
            self.update_progress.emit(60, f"热门新闻分析完成，共 {len(top_news)} 条")
            
            # 跨平台分析
            self.update_progress.emit(60, "正在进行跨平台新闻分析...")
            cross_platform = self.analyzer.analyze_cross_platform()
            self.update_progress.emit(80, f"跨平台分析完成，共 {len(cross_platform)} 组")
            
            # 关键词分析
            self.update_progress.emit(80, "正在进行关键词分析...")
            keywords = self.analyzer.analyze_keywords()
            platform_dist = self.analyzer.analyze_platform_distribution()
            
            # 综合分析结果
            result = {
                'top_news': top_news,
                'cross_platform': cross_platform,
                'keywords': keywords,
                'platform_distribution': platform_dist,
                'total_news': len(self.news_data),
                'analysis_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.update_progress.emit(100, "分析完成")
            self.analysis_finished.emit(result)
            
        except Exception as e:
            logger.error(f"分析线程出错: {str(e)}")
            self.update_progress.emit(0, f"分析出错: {str(e)}")


# 自定义图表画布
class MplCanvas(FigureCanvas):
    """自定义Matplotlib画布，用于在Qt界面显示图表"""
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super(MplCanvas, self).__init__(self.fig)
        self.setParent(parent)
        
        # 设置图表样式
        plt.style.use('ggplot')  # 使用ggplot样式
        self.fig.set_facecolor('#f0f0f0')
        self.axes.set_facecolor('#f8f8f8')
        
        # 调整布局
        self.fig.tight_layout()


# 关键词云图组件
class KeywordCloudWidget(QWidget):
    """关键词云图组件，用于显示热门关键词"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QVBoxLayout(self)
        
        # 创建图表
        self.canvas = MplCanvas(self, width=5, height=4, dpi=100)
        self.layout.addWidget(self.canvas)
        
    def update_chart(self, keywords):
        """更新关键词云图"""
        try:
            self.canvas.axes.clear()
            
            words = list(keywords.keys())
            weights = list(keywords.values())
            
            # 确保有数据
            if not words:
                self.canvas.axes.text(0.5, 0.5, "没有关键词数据", 
                                     ha='center', va='center', fontsize=14)
                self.canvas.draw()
                return
            
            # 创建水平条形图
            y_pos = np.arange(len(words))
            self.canvas.axes.barh(y_pos, weights, align='center', alpha=0.7, color='#4CAF50')
            self.canvas.axes.set_yticks(y_pos)
            self.canvas.axes.set_yticklabels(words)
            self.canvas.axes.invert_yaxis()  # 使最大值在顶部
            self.canvas.axes.set_xlabel('权重')
            self.canvas.axes.set_title('热门关键词')
            
            # 调整布局并绘制
            self.canvas.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"更新关键词云图出错: {str(e)}")


# 平台分布图组件
class PlatformDistWidget(QWidget):
    """平台分布图组件，用于显示各平台热点分布"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QVBoxLayout(self)
        
        # 创建图表
        self.canvas = MplCanvas(self, width=5, height=4, dpi=100)
        self.layout.addWidget(self.canvas)
        
    def update_chart(self, platform_dist):
        """更新平台分布图"""
        try:
            self.canvas.axes.clear()
            
            platforms = list(platform_dist.keys())
            counts = list(platform_dist.values())
            
            # 确保有数据
            if not platforms:
                self.canvas.axes.text(0.5, 0.5, "没有平台分布数据", 
                                     ha='center', va='center', fontsize=14)
                self.canvas.draw()
                return
            
            # 创建饼图
            colors = ['#4CAF50', '#2196F3', '#FF9800', '#E91E63', '#9C27B0', '#607D8B']
            explode = [0.1 if i == counts.index(max(counts)) else 0 for i in range(len(counts))]
            self.canvas.axes.pie(counts, explode=explode, labels=platforms, colors=colors,
                                autopct='%1.1f%%', shadow=True, startangle=90)
            self.canvas.axes.axis('equal')  # 使饼图为正圆形
            self.canvas.axes.set_title('各平台热点分布')
            
            # 调整布局并绘制
            self.canvas.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"更新平台分布图出错: {str(e)}")


# 热度排行图组件
class HeatRankWidget(QWidget):
    """热度排行图组件，用于显示新闻热度排行"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QVBoxLayout(self)
        
        # 创建图表
        self.canvas = MplCanvas(self, width=5, height=4, dpi=100)
        self.layout.addWidget(self.canvas)
        
    def update_chart(self, top_news, limit=10):
        """更新热度排行图"""
        try:
            self.canvas.axes.clear()
            
            # 确保有数据
            if not top_news:
                self.canvas.axes.text(0.5, 0.5, "没有热点新闻数据", 
                                     ha='center', va='center', fontsize=14)
                self.canvas.draw()
                return
            
            # 提取标题和热度
            data = top_news[:limit]  # 只显示前N条
            titles = [item['title'][:15] + '...' if len(item['title']) > 15 else item['title'] for item in data]
            heat_scores = [item['heat_score'] if 'heat_score' in item else item['heat'] for item in data]
            platforms = [item['platform'] for item in data]
            
            # 创建水平条形图
            y_pos = np.arange(len(titles))
            
            # 根据平台设置不同颜色
            platform_colors = {
                '知乎': '#0066FF',
                '微博': '#FF6600',
                '百度': '#2932E1',
                'B站': '#FB7299',
                '头条': '#FF0000',
                '抖音': '#000000'
            }
            colors = [platform_colors.get(p, '#999999') for p in platforms]
            
            self.canvas.axes.barh(y_pos, heat_scores, align='center', alpha=0.7, color=colors)
            self.canvas.axes.set_yticks(y_pos)
            self.canvas.axes.set_yticklabels(titles)
            self.canvas.axes.invert_yaxis()  # 使最大值在顶部
            self.canvas.axes.set_xlabel('热度分数')
            self.canvas.axes.set_title('热点新闻排行')
            
            # 添加平台标签
            for i, (score, platform) in enumerate(zip(heat_scores, platforms)):
                self.canvas.axes.text(score + 1, i, platform, va='center')
            
            # 调整布局并绘制
            self.canvas.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"更新热度排行图出错: {str(e)}")


# 主窗口类 - 第一部分：基本设置
class HotNewsMainWindow(QMainWindow):
    """热点新闻分析系统主窗口"""
    
    def __init__(self):
        super().__init__()
        
        # 设置窗口标题和大小
        self.setWindowTitle("全网热点新闻分析系统")
        self.resize(1200, 800)
        
        # 初始化数据
        self.news_data = []
        self.analysis_result = {}
        
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # 创建界面组件
        self.setup_ui() 

    def setup_ui(self):
        """设置界面组件"""
        # 创建顶部控制栏
        self.setup_top_controls()
        
        # 创建进度条
        self.setup_progress_bar()
        
        # 创建选项卡
        self.setup_tabs()
        
        # 设置样式
        self.setup_styles()
    
    def setup_top_controls(self):
        """设置顶部控制栏"""
        # 创建顶部控制栏容器
        top_frame = QFrame()
        top_frame.setFrameShape(QFrame.StyledPanel)
        top_frame.setMaximumHeight(100)
        top_layout = QHBoxLayout(top_frame)
        
        # 创建控制组件
        title_label = QLabel("全网热点新闻分析系统")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        
        # 新闻数量控制
        limit_label = QLabel("爬取数量:")
        self.limit_spin = QSpinBox()
        self.limit_spin.setRange(10, 200)
        self.limit_spin.setValue(50)
        self.limit_spin.setSingleStep(10)
        
        # 按钮
        self.crawl_btn = QPushButton("开始爬取")
        self.crawl_btn.setMinimumWidth(120)
        self.crawl_btn.clicked.connect(self.start_crawling)
        
        self.analyze_btn = QPushButton("分析数据")
        self.analyze_btn.setMinimumWidth(120)
        self.analyze_btn.setEnabled(False)
        self.analyze_btn.clicked.connect(self.start_analyzing)
        
        self.save_btn = QPushButton("保存数据")
        self.save_btn.setMinimumWidth(120)
        self.save_btn.setEnabled(False)
        self.save_btn.clicked.connect(self.save_data)
        
        self.load_btn = QPushButton("加载数据")
        self.load_btn.setMinimumWidth(120)
        self.load_btn.clicked.connect(self.load_data)
        
        # 添加组件到布局
        top_layout.addWidget(title_label)
        top_layout.addStretch()
        top_layout.addWidget(limit_label)
        top_layout.addWidget(self.limit_spin)
        top_layout.addWidget(self.crawl_btn)
        top_layout.addWidget(self.analyze_btn)
        top_layout.addWidget(self.save_btn)
        top_layout.addWidget(self.load_btn)
        
        # 添加顶部控制栏到主布局
        self.main_layout.addWidget(top_frame)
    
    def setup_progress_bar(self):
        """设置进度条"""
        progress_frame = QFrame()
        progress_frame.setMaximumHeight(50)
        progress_layout = QHBoxLayout(progress_frame)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        self.status_label = QLabel("就绪")
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        
        self.main_layout.addWidget(progress_frame)
    
    def setup_tabs(self):
        """设置选项卡"""
        self.tab_widget = QTabWidget()
        
        # 创建各个选项卡
        self.setup_news_tab()      # 新闻列表选项卡
        self.setup_analysis_tab()  # 分析结果选项卡
        self.setup_charts_tab()    # 图表选项卡
        self.setup_cross_tab()     # 跨平台热点选项卡
        
        self.main_layout.addWidget(self.tab_widget)
    
    def setup_styles(self):
        """设置界面样式"""
        # 设置全局样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget {
                background-color: #ffffff;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: #ffffff;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                border: 1px solid #cccccc;
                padding: 6px 12px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom-color: #ffffff;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0d8aee;
            }
            QPushButton:pressed {
                background-color: #0c7cd5;
            }
            QPushButton:disabled {
                background-color: #9e9e9e;
            }
            QFrame {
                background-color: #ffffff;
                border-radius: 4px;
            }
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #f0f0f0;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
                margin: 0.5px;
            }
            QLabel {
                color: #424242;
            }
            QTableWidget {
                border: 1px solid #e0e0e0;
                gridline-color: #f0f0f0;
                selection-background-color: #e3f2fd;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 4px;
                border: 1px solid #e0e0e0;
                font-weight: bold;
            }
        """) 

    def setup_news_tab(self):
        """设置新闻列表选项卡"""
        news_tab = QWidget()
        news_layout = QVBoxLayout(news_tab)
        
        # 创建表格
        self.news_table = QTableWidget()
        self.news_table.setColumnCount(5)
        self.news_table.setHorizontalHeaderLabels(["标题", "平台", "热度", "爬取时间", "链接"])
        self.news_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 不可编辑
        self.news_table.setSelectionBehavior(QTableWidget.SelectRows)  # 选择整行
        self.news_table.setAlternatingRowColors(True)  # 隔行变色
        self.news_table.horizontalHeader().setStretchLastSection(True)  # 最后一列自动拉伸
        self.news_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # 标题列自动拉伸
        self.news_table.verticalHeader().setVisible(False)  # 隐藏垂直表头
        self.news_table.cellDoubleClicked.connect(self.open_news_link)  # 双击打开链接
        
        # 创建搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索新闻")
        self.search_edit.textChanged.connect(self.filter_news)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        
        # 创建平台筛选
        platform_layout = QHBoxLayout()
        platform_label = QLabel("平台筛选:")
        self.platform_combo = QComboBox()
        self.platform_combo.addItem("全部平台")
        self.platform_combo.addItems(["知乎", "微博", "百度", "B站", "头条", "抖音"])
        self.platform_combo.currentTextChanged.connect(self.filter_news)
        platform_layout.addWidget(platform_label)
        platform_layout.addWidget(self.platform_combo)
        platform_layout.addStretch()
        
        # 添加到布局
        news_layout.addLayout(search_layout)
        news_layout.addLayout(platform_layout)
        news_layout.addWidget(self.news_table)
        
        self.tab_widget.addTab(news_tab, "热点新闻列表")
    
    def setup_analysis_tab(self):
        """设置分析结果选项卡"""
        analysis_tab = QWidget()
        analysis_layout = QVBoxLayout(analysis_tab)
        
        # 创建结果显示区域
        self.analysis_browser = QTextBrowser()
        self.analysis_browser.setOpenExternalLinks(True)
        
        # 添加到布局
        analysis_layout.addWidget(self.analysis_browser)
        
        self.tab_widget.addTab(analysis_tab, "分析结果")
    
    def setup_charts_tab(self):
        """设置图表选项卡"""
        charts_tab = QWidget()
        charts_layout = QGridLayout(charts_tab)
        
        # 创建图表组件
        self.keyword_widget = KeywordCloudWidget()
        self.platform_widget = PlatformDistWidget()
        self.heat_rank_widget = HeatRankWidget()
        
        # 添加到布局
        charts_layout.addWidget(QLabel("热门关键词"), 0, 0)
        charts_layout.addWidget(self.keyword_widget, 1, 0)
        charts_layout.addWidget(QLabel("平台分布"), 0, 1)
        charts_layout.addWidget(self.platform_widget, 1, 1)
        charts_layout.addWidget(QLabel("热度排行"), 2, 0, 1, 2)
        charts_layout.addWidget(self.heat_rank_widget, 3, 0, 1, 2)
        
        self.tab_widget.addTab(charts_tab, "数据图表")
    
    def setup_cross_tab(self):
        """设置跨平台热点选项卡"""
        cross_tab = QWidget()
        cross_layout = QVBoxLayout(cross_tab)
        
        # 创建表格
        self.cross_table = QTableWidget()
        self.cross_table.setColumnCount(4)
        self.cross_table.setHorizontalHeaderLabels(["主新闻标题", "平台", "跨平台数", "热度"])
        self.cross_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 不可编辑
        self.cross_table.setSelectionBehavior(QTableWidget.SelectRows)  # 选择整行
        self.cross_table.setAlternatingRowColors(True)  # 隔行变色
        self.cross_table.horizontalHeader().setStretchLastSection(True)  # 最后一列自动拉伸
        self.cross_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # 标题列自动拉伸
        self.cross_table.verticalHeader().setVisible(False)  # 隐藏垂直表头
        self.cross_table.cellClicked.connect(self.show_similar_news)  # 点击显示相似新闻
        
        # 创建相似新闻显示区
        self.similar_browser = QTextBrowser()
        self.similar_browser.setOpenExternalLinks(True)
        self.similar_browser.setMaximumHeight(200)
        
        # 添加到布局
        cross_layout.addWidget(QLabel("跨平台热点新闻:"))
        cross_layout.addWidget(self.cross_table)
        cross_layout.addWidget(QLabel("相似新闻:"))
        cross_layout.addWidget(self.similar_browser)
        
        self.tab_widget.addTab(cross_tab, "跨平台热点") 

    def start_crawling(self):
        """开始爬取新闻"""
        # 禁用按钮
        self.crawl_btn.setEnabled(False)
        self.load_btn.setEnabled(False)
        
        # 获取爬取数量
        limit = self.limit_spin.value()
        
        # 更新状态
        self.status_label.setText("正在爬取热点新闻...")
        
        # 创建爬虫线程
        self.crawler_thread = CrawlerThread(limit)
        self.crawler_thread.update_progress.connect(self.update_crawl_progress)
        self.crawler_thread.crawl_finished.connect(self.crawl_finished)
        
        # 启动线程
        self.crawler_thread.start()
    
    def update_crawl_progress(self, value, message):
        """更新爬取进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
    
    def crawl_finished(self, news_data):
        """爬取完成处理"""
        # 保存数据
        self.news_data = news_data
        
        # 启用按钮
        self.crawl_btn.setEnabled(True)
        self.analyze_btn.setEnabled(True)
        self.save_btn.setEnabled(True)
        self.load_btn.setEnabled(True)
        
        # 更新状态
        self.status_label.setText(f"爬取完成，共获取 {len(news_data)} 条热点新闻")
        
        # 更新新闻列表
        self.update_news_table()
    
    def update_news_table(self):
        """更新新闻列表表格"""
        # 清空表格
        self.news_table.setRowCount(0)
        
        # 添加数据
        row_count = 0
        for news in self.news_data:
            # 验证数据完整性
            if not self.validate_news_item(news):
                continue
                
            # 插入行并创建单元格项
            self.news_table.insertRow(row_count)
            
            # 设置单元格内容
            title_item = QTableWidgetItem(news['title'])
            platform_item = QTableWidgetItem(news['platform'])
            heat_item = QTableWidgetItem(str(news['heat']))
            time_item = QTableWidgetItem(news.get('crawl_time', ''))
            link_item = QTableWidgetItem(news['url'])
            link_item.setForeground(QColor('#0000FF'))  # 蓝色链接
            
            # 设置项目到表格
            self.news_table.setItem(row_count, 0, title_item)
            self.news_table.setItem(row_count, 1, platform_item)
            self.news_table.setItem(row_count, 2, heat_item)
            self.news_table.setItem(row_count, 3, time_item)
            self.news_table.setItem(row_count, 4, link_item)
            
            # 根据平台设置颜色
            platform_colors = {
                '知乎': '#0066FF',
                '微博': '#FF6600',
                '百度': '#2932E1',
                'B站': '#FB7299',
                '头条': '#FF0000',
                '抖音': '#000000'
            }
            platform = news['platform']
            if platform in platform_colors:
                color = QColor(platform_colors[platform] + '22')  # 添加透明度
                for col in range(5):
                    # 确保单元格项已创建
                    item = self.news_table.item(row_count, col)
                    if item:
                        item.setBackground(color)
            
            # 增加行计数
            row_count += 1
    
    def validate_news_item(self, news):
        """验证新闻数据项是否有效"""
        # 验证必要字段是否存在
        required_fields = ['title', 'platform', 'heat', 'url']
        for field in required_fields:
            if field not in news or news[field] is None:
                logger.warning(f"新闻数据缺少必要字段: {field}")
                return False
                
        # 验证标题不为空
        if not news['title'] or not isinstance(news['title'], str) or not news['title'].strip():
            logger.warning(f"新闻标题为空或无效: {news.get('title', None)}")
            return False
            
        # 验证热度为数字，并转换格式
        try:
            # 转换热度为数字
            if isinstance(news['heat'], str):
                if news['heat'].isdigit():
                    news['heat'] = int(news['heat'])
                else:
                    try:
                        news['heat'] = float(news['heat'])
                    except ValueError:
                        news['heat'] = 10000  # 默认热度
                        
            # 如果热度仍不是数字类型，设置为默认值
            if not isinstance(news['heat'], (int, float)):
                logger.warning(f"热度值不是数字类型: {news['heat']}, 设置为默认值")
                news['heat'] = 10000
                
            # 热度为0或负数时，给予适当的值
            if news['heat'] <= 0:
                logger.warning(f"热度值小于等于0: {news['heat']}, 设置为适当值")
                # 根据平台分配不同范围的热度值
                platform_heat_ranges = {
                    '头条': (200000, 800000),
                    '微博': (300000, 900000),
                    '知乎': (100000, 500000),
                    '百度': (200000, 700000),
                    'B站': (50000, 300000),
                    '抖音': (500000, 1000000)
                }
                
                heat_range = platform_heat_ranges.get(news['platform'], (10000, 50000))
                news['heat'] = random.randint(*heat_range)
                
        except Exception as e:
            logger.error(f"处理热度值时出错: {str(e)}")
            news['heat'] = 10000  # 设置默认热度
            
        # 特殊处理头条数据，确保它们是有效的
        if news['platform'] == '头条':
            # 如果热度异常低，设置为合理范围
            heat = float(news['heat'])
            if heat < 100:
                news['heat'] = 200000 + random.randint(0, 300000)
                logger.info(f"调整头条热度值: {heat} -> {news['heat']}")
                
        # 确保URL是有效的
        if not news['url'] or not isinstance(news['url'], str) or not news['url'].startswith('http'):
            news['url'] = f"https://www.{news['platform'].lower()}.com/search?q={news['title']}"
            logger.warning(f"URL无效，已设置为默认搜索URL: {news['url']}")
                
        return True
    
    def filter_news(self):
        """过滤新闻列表"""
        keyword = self.search_edit.text().lower()
        platform = self.platform_combo.currentText()
        
        self.news_table.setRowCount(0)
        
        row = 0
        for news in self.news_data:
            # 验证数据完整性
            if not self.validate_news_item(news):
                continue
                
            # 检查平台筛选
            if platform != "全部平台" and news['platform'] != platform:
                continue
                
            # 检查关键词
            if keyword and keyword not in news['title'].lower():
                continue
                
            # 添加符合条件的行
            self.news_table.insertRow(row)
            
            # 创建单元格项目
            title_item = QTableWidgetItem(news['title'])
            platform_item = QTableWidgetItem(news['platform'])
            heat_item = QTableWidgetItem(str(news['heat']))
            time_item = QTableWidgetItem(news.get('crawl_time', ''))
            link_item = QTableWidgetItem(news['url'])
            link_item.setForeground(QColor('#0000FF'))
            
            # 设置项目到表格
            self.news_table.setItem(row, 0, title_item)
            self.news_table.setItem(row, 1, platform_item)
            self.news_table.setItem(row, 2, heat_item)
            self.news_table.setItem(row, 3, time_item)
            self.news_table.setItem(row, 4, link_item)
            
            # 根据平台设置颜色
            platform_colors = {
                '知乎': '#0066FF',
                '微博': '#FF6600',
                '百度': '#2932E1',
                'B站': '#FB7299',
                '头条': '#FF0000',
                '抖音': '#000000'
            }
            if news['platform'] in platform_colors:
                color = QColor(platform_colors[news['platform']] + '22')  # 添加透明度
                for col in range(5):
                    # 确保单元格项已创建
                    item = self.news_table.item(row, col)
                    if item:
                        item.setBackground(color)
            
            row += 1
    
    def open_news_link(self, row, column):
        """打开新闻链接"""
        url = self.news_table.item(row, 4).text()
        if url:
            QDesktopServices.openUrl(QUrl(url))
    
    def start_analyzing(self):
        """开始分析数据"""
        if not self.news_data:
            QMessageBox.warning(self, "警告", "没有数据可供分析！")
            return
        
        # 禁用按钮
        self.analyze_btn.setEnabled(False)
        
        # 更新状态
        self.status_label.setText("正在分析数据...")
        
        # 创建分析线程
        self.analyzer_thread = AnalyzerThread(self.news_data, 100)
        self.analyzer_thread.update_progress.connect(self.update_analyze_progress)
        self.analyzer_thread.analysis_finished.connect(self.analysis_finished)
        
        # 启动线程
        self.analyzer_thread.start()
    
    def update_analyze_progress(self, value, message):
        """更新分析进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
    
    def analysis_finished(self, result):
        """分析完成处理"""
        # 保存结果
        self.analysis_result = result
        
        # 启用按钮
        self.analyze_btn.setEnabled(True)
        
        # 更新状态
        self.status_label.setText(f"分析完成，共分析 {result['total_news']} 条热点新闻")
        
        # 更新分析结果
        self.update_analysis_tab()
        
        # 更新图表
        self.update_charts()
        
        # 更新跨平台热点
        self.update_cross_tab()
        
        # 切换到分析选项卡
        self.tab_widget.setCurrentIndex(1)
    
    def update_analysis_tab(self):
        """更新分析结果选项卡"""
        if not self.analysis_result:
            return
            
        # 构建分析报告HTML
        html = f"""
        <h2>全网热点新闻分析报告</h2>
        <p><b>分析时间：</b>{self.analysis_result['analysis_time']}</p>
        <p><b>总新闻数：</b>{self.analysis_result['total_news']} 条</p>
        <h3>平台分布</h3>
        <ul>
        """
        
        # 平台分布
        for platform, count in self.analysis_result['platform_distribution'].items():
            html += f"<li>{platform}: {count} 条</li>"
        
        html += "</ul><h3>热门关键词</h3><ul>"
        
        # 热门关键词
        sorted_keywords = sorted(self.analysis_result['keywords'].items(), key=lambda x: x[1], reverse=True)
        for keyword, weight in sorted_keywords[:15]:  # 增加到15个关键词
            html += f"<li>{keyword}: {weight:.4f}</li>"
        
        html += "</ul><h3>热度最高的十条新闻</h3><ol>"
        
        # 热度最高的新闻
        for news in self.analysis_result['top_news'][:10]:
            title = news['title']
            platform = news['platform']
            heat = news.get('heat_score', news.get('heat', 0))
            url = news['url']
            html += f'<li><a href="{url}">{title}</a> ({platform}, 热度: {heat:.1f})</li>'
        
        html += "</ol><h3>跨平台热点</h3><ul>"
        
        # 跨平台热点 - 增加显示数量到15个
        max_display = min(15, len(self.analysis_result['cross_platform']))
        for i, group in enumerate(self.analysis_result['cross_platform'][:max_display]):
            main_news = group['main_news']
            title = main_news['title']
            platform = main_news['platform']
            platforms = group['platforms']
            similar_count = len(group['similar_news'])
            html += f"<li><b>{title}</b> (主平台: {platform}, 覆盖平台: {', '.join(platforms)}, 相似新闻数: {similar_count})</li>"
        
        # 添加跨平台热点总数
        total_cross_platform = len(self.analysis_result['cross_platform'])
        html += f"</ul><p>共发现 <b>{total_cross_platform}</b> 组跨平台热点。"
        
        if total_cross_platform > max_display:
            html += f" 此处仅显示前 {max_display} 组，更多内容请查看\"跨平台热点\"选项卡。"
        
        html += "</p>"
        
        # 设置HTML内容
        self.analysis_browser.setHtml(html)
    
    def update_charts(self):
        """更新图表选项卡"""
        if not self.analysis_result:
            return
            
        # 更新关键词图表
        self.keyword_widget.update_chart(self.analysis_result['keywords'])
        
        # 更新平台分布图表
        self.platform_widget.update_chart(self.analysis_result['platform_distribution'])
        
        # 更新热度排行图表
        self.heat_rank_widget.update_chart(self.analysis_result['top_news'], 10)
    
    def update_cross_tab(self):
        """更新跨平台热点选项卡"""
        if not self.analysis_result or 'cross_platform' not in self.analysis_result:
            return
            
        # 清空表格
        self.cross_table.setRowCount(0)
        
        # 获取跨平台热点数据，最多显示30个
        cross_platform_data = self.analysis_result['cross_platform'][:30]
        
        # 添加数据
        for i, group in enumerate(cross_platform_data):
            self.cross_table.insertRow(i)
            
            main_news = group['main_news']
            
            # 设置单元格内容
            self.cross_table.setItem(i, 0, QTableWidgetItem(main_news['title']))
            self.cross_table.setItem(i, 1, QTableWidgetItem(main_news['platform']))
            self.cross_table.setItem(i, 2, QTableWidgetItem(str(group['total_platforms'])))
            heat = main_news.get('heat_score', main_news.get('heat', 0))
            self.cross_table.setItem(i, 3, QTableWidgetItem(str(heat)))
            
            # 根据跨平台数设置颜色
            color_intensity = min(255, group['total_platforms'] * 40)  # 减小倍数，使颜色不那么深
            for col in range(4):
                item = self.cross_table.item(i, col)
                if item:
                    item.setBackground(QColor(0, color_intensity, 0, 40))
        
        # 在表格底部添加说明
        if cross_platform_data:
            row_count = self.cross_table.rowCount()
            self.cross_table.insertRow(row_count)
            info_item = QTableWidgetItem(f"共找到 {len(self.analysis_result['cross_platform'])} 组跨平台热点")
            info_item.setTextAlignment(Qt.AlignCenter)
            # 合并单元格来显示说明
            self.cross_table.setItem(row_count, 0, info_item)
            self.cross_table.setSpan(row_count, 0, 1, 4)
        
        # 自动调整列宽
        self.cross_table.resizeColumnsToContents()
        
        # 防止没有数据时显示空白
        if not cross_platform_data:
            logger.warning("未找到跨平台热点数据")
            self.similar_browser.setHtml("<p>未找到跨平台热点数据。可能原因：<br>1. 爬取的数据量不足<br>2. 各平台热点差异较大<br>3. 算法未能识别相似热点</p>")
    
    def show_similar_news(self, row, column):
        """显示相似新闻"""
        # 跳过最后一行（说明行）
        if row >= self.cross_table.rowCount() - 1:
            return
            
        if not self.analysis_result or 'cross_platform' not in self.analysis_result or row >= len(self.analysis_result['cross_platform']):
            return
            
        group = self.analysis_result['cross_platform'][row]
        main_news = group['main_news']
        similar_news = group['similar_news']
        
        # 构建HTML
        html = f"""
        <h3>相似新闻组：{main_news['title']}</h3>
        <p><b>主新闻平台：</b>{main_news['platform']}</p>
        <p><b>覆盖平台：</b>{', '.join(group['platforms'])}</p>
        <p><b>相似新闻数：</b>{len(similar_news)}</p>
        
        <table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>
        <tr>
            <th>标题</th>
            <th>平台</th>
            <th>热度</th>
            <th>链接</th>
        </tr>
        """
        
        # 添加主新闻
        html += f"""
        <tr bgcolor='#E6F3FF'>
            <td><b>{main_news['title']}</b></td>
            <td>{main_news['platform']}</td>
            <td>{main_news.get('heat_score', main_news.get('heat', 0))}</td>
            <td><a href='{main_news['url']}'>查看原文</a></td>
        </tr>
        """
        
        # 添加相似新闻
        for news in similar_news:
            html += f"""
            <tr>
                <td>{news['title']}</td>
                <td>{news['platform']}</td>
                <td>{news.get('heat_score', news.get('heat', 0))}</td>
                <td><a href='{news['url']}'>查看原文</a></td>
            </tr>
            """
        
        html += "</table>"
        
        # 设置HTML内容
        self.similar_browser.setHtml(html)
    
    def save_data(self):
        """保存数据到文件"""
        if not self.news_data:
            QMessageBox.warning(self, "警告", "没有数据可供保存！")
            return
            
        # 打开文件对话框
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存数据", "", "CSV文件 (*.csv);;JSON文件 (*.json)"
        )
        
        if not filename:
            return
            
        try:
            # 根据文件扩展名保存
            if filename.endswith('.csv'):
                df = pd.DataFrame(self.news_data)
                df.to_csv(filename, index=False, encoding='utf-8-sig')
            else:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.news_data, f, ensure_ascii=False, indent=2)
            
            QMessageBox.information(self, "成功", f"数据已成功保存到 {filename}")
            self.status_label.setText(f"数据已保存到 {filename}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存数据时出错: {str(e)}")
            logger.error(f"保存数据出错: {str(e)}")
    
    def load_data(self):
        """从文件加载数据"""
        # 打开文件对话框
        filename, _ = QFileDialog.getOpenFileName(
            self, "加载数据", "", "CSV文件 (*.csv);;JSON文件 (*.json);;所有文件 (*)"
        )
        
        if not filename:
            return
            
        try:
            # 根据文件扩展名加载
            if filename.endswith('.csv'):
                df = pd.read_csv(filename, encoding='utf-8-sig')
                self.news_data = df.to_dict('records')
            else:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.news_data = json.load(f)
            
            # 更新状态
            self.status_label.setText(f"已从 {filename} 加载 {len(self.news_data)} 条新闻")
            
            # 更新新闻列表
            self.update_news_table()
            
            # 启用按钮
            self.analyze_btn.setEnabled(True)
            self.save_btn.setEnabled(True)
            
            QMessageBox.information(self, "成功", f"已从 {filename} 加载 {len(self.news_data)} 条新闻数据")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据时出错: {str(e)}")
            logger.error(f"加载数据出错: {str(e)}")


# 运行应用程序
def run_app():
    app = QApplication(sys.argv)
    window = HotNewsMainWindow()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    run_app() 