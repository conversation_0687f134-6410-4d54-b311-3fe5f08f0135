#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
内容抓取模块 - 负责获取热点详细内容
"""

import re
import time
import random
import requests
from bs4 import BeautifulSoup
import logging
import json
from urllib.parse import urlparse

# 导入代理配置
from .spider import get_proxy_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ContentFetcher')

class ContentFetcher:
    """内容获取器，负责获取热点的详细内容"""
    
    def __init__(self):
        """初始化内容获取器"""
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0'
        ]
        self.timeout = 10
        self.max_retries = 3
        self.is_domestic = True  # 默认为国内内容
        
    def get_random_headers(self):
        """获取随机User-Agent的请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    def request_get(self, url, retry_count=0):
        """发送GET请求并返回响应"""
        if retry_count >= self.max_retries:
            logger.warning(f"已达到最大重试次数 ({self.max_retries})，放弃请求: {url}")
            return None
            
        try:
            # 获取代理配置
            proxy_config = get_proxy_config()
            proxies = None
            
            # 根据是否为国内网站设置不同的代理
            if self.is_domestic:
                # 使用国内代理
                if proxy_config['domestic']['enabled']:
                    proxies = {
                        'http': proxy_config['domestic']['http'],
                        'https': proxy_config['domestic']['https']
                    }
                    logger.info(f"使用国内代理请求: {url}")
            else:
                # 使用国际代理
                if proxy_config['international']['enabled']:
                    proxies = {
                        'http': proxy_config['international']['http'],
                        'https': proxy_config['international']['https']
                    }
                    logger.info(f"使用国际代理请求: {url}")
                    
            # 清理不完整的代理设置
            if proxies:
                if not proxies['http']:
                    proxies['http'] = None
                if not proxies['https']:
                    proxies['https'] = None
                    
                # 如果两个代理都为空，则不使用代理
                if not proxies['http'] and not proxies['https']:
                    proxies = None
                    
            logger.info(f"请求 ({retry_count + 1}/{self.max_retries}): {url}")
            response = requests.get(
                url, 
                headers=self.get_random_headers(), 
                timeout=self.timeout,
                proxies=proxies
            )
            
            # 检查响应状态码
            if 200 <= response.status_code < 300:
                return response
                
            logger.warning(f"请求状态码非成功: {response.status_code} - {url}")
            
            # 对于某些错误直接放弃重试
            if response.status_code in [403, 404, 500, 502, 503]:
                return None
                
            # 其他错误码重试
            time.sleep(1 + retry_count)  # 添加增量延迟
            return self.request_get(url, retry_count + 1)
            
        except Exception as e:
            logger.warning(f"请求失败 ({retry_count + 1}/{self.max_retries}): {url} 错误: {str(e)}")
            time.sleep(1 + retry_count)  # 添加增量延迟
            return self.request_get(url, retry_count + 1)
    
    def get_site_type(self, url):
        """获取网站类型，并设置是否为国内网站"""
        domain = urlparse(url).netloc.lower()
        
        # 国际网站列表
        international_domains = [
            'twitter.com', 'x.com', 't.co',
            'facebook.com', 'fb.com',
            'instagram.com',
            'youtube.com', 'youtu.be',
            'reddit.com',
            'linkedin.com',
            'medium.com',
            'github.com',
            'wikipedia.org',
            'bbc.com', 'bbc.co.uk',
            'nytimes.com',
            'cnn.com',
            'theguardian.com',
            'forbes.com',
            'amazon.com',
            'google.com'
        ]
        
        # 检查是否为国际网站
        for int_domain in international_domains:
            if int_domain in domain:
                self.is_domestic = False
                break
        else:
            # 特殊处理buzzing.cc，它汇总国际内容
            if 'buzzing.cc' in domain:
                self.is_domestic = False
            else:
                # 默认为国内网站
                self.is_domestic = True
        
        # 返回具体网站类型
        if 'baidu.com' in domain:
            return 'baidu', self.is_domestic
        elif 'weibo.com' in domain or 's.weibo.com' in domain:
            return 'weibo', self.is_domestic
        elif 'toutiao.com' in domain:
            return 'toutiao', self.is_domestic
        elif 'douyin.com' in domain:
            return 'douyin', self.is_domestic
        elif 'zhihu.com' in domain:
            return 'zhihu', self.is_domestic
        elif 'bilibili.com' in domain:
            return 'bilibili', self.is_domestic
        elif 'buzzing.cc' in domain:
            return 'buzzing', self.is_domestic
        elif 'twitter.com' in domain or 'x.com' in domain:
            return 'twitter', self.is_domestic
        elif 'youtube.com' in domain:
            return 'youtube', self.is_domestic
        elif 'sohu.com' in domain:
            return 'sohu', self.is_domestic
        elif 'sina.com.cn' in domain:
            return 'sina', self.is_domestic
        elif '163.com' in domain:
            return 'netease', self.is_domestic
        else:
            # 根据是否为国际域名设置
            if not self.is_domestic:
                return 'international', self.is_domestic
            else:
                return 'general', self.is_domestic
    
    def fetch_content(self, url):
        """获取内容"""
        try:
            # 添加日志，记录代理状态
            proxy_config = get_proxy_config()
            site_type, is_domestic = self.get_site_type(url)
            self.is_domestic = is_domestic
            
            if is_domestic:
                logger.info(f"使用国内代理设置获取内容: {url}")
                if proxy_config['domestic']['enabled']:
                    logger.info(f"国内代理已启用 - HTTP: {proxy_config['domestic']['http']}, HTTPS: {proxy_config['domestic']['https']}")
                else:
                    logger.info("国内代理未启用，直接连接")
            else:
                logger.info(f"使用国际代理设置获取内容: {url}")
                if proxy_config['international']['enabled']:
                    logger.info(f"国际代理已启用 - HTTP: {proxy_config['international']['http']}, HTTPS: {proxy_config['international']['https']}")
                else:
                    logger.info("国际代理未启用，直接连接")
                    
            # 根据网站类型选择不同的获取方法
            if site_type == 'baidu':
                return self._fetch_baidu_content(url)
            elif site_type == 'weibo':
                return self._fetch_weibo_content(url)
            elif site_type == 'toutiao':
                return self._fetch_toutiao_content(url)
            elif site_type == 'douyin':
                return self._fetch_douyin_content(url)
            else:
                # 国际网站
                return self._fetch_international_content(url)
                
        except Exception as e:
            logger.error(f"获取内容失败: {e}")
            # 出错时返回模拟内容
            return self._generate_mock_content("热点详情", url, "other")
    
    def _fetch_baidu_content(self, url):
        """获取百度相关内容"""
        response = self.request_get(url)
        if not response:
            # 返回模拟内容
            return self._generate_mock_content("百度热点详情", url, 'baidu')
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试获取标题
            title_element = soup.select_one('h3.c-title, h2.c-title, h1')
            title = title_element.text.strip() if title_element else "百度热点"
            
            # 尝试获取内容
            content_elements = soup.select('div.content-wrapper, div.c-content, div.c-row')
            content = "\n".join([elem.text.strip() for elem in content_elements if elem.text.strip()])
            
            # 如果内容为空，尝试一般性提取
            if not content:
                # 提取所有段落
                paragraphs = soup.select('p')
                content = "\n".join([p.text.strip() for p in paragraphs if len(p.text.strip()) > 30])
            
            # 仍然为空，返回提示
            if not content:
                content = "百度热点详情加载中，请点击下方原文链接查看完整内容。"
            
            return {
                'title': title,
                'content': content,
                'source_url': url,
                'images': self._extract_images(soup, url),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"解析百度内容失败: {e}")
            # 出错时返回模拟内容
            return self._generate_mock_content("百度热点详情", url, 'baidu')
    
    def _fetch_weibo_content(self, url):
        """获取微博相关内容"""
        response = self.request_get(url)
        if not response:
            # 返回模拟内容
            return self._generate_mock_content("微博热搜详情", url, 'weibo')
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试获取标题
            title_element = soup.select_one('h1, .content_title, .m-text-title')
            title = title_element.text.strip() if title_element else "微博热点"
            
            # 尝试获取内容
            content_elements = soup.select('.weibo-text, .m-text-cut, .weibo-detail')
            content = "\n".join([elem.text.strip() for elem in content_elements if elem.text.strip()])
            
            # 如果内容为空，尝试一般性提取
            if not content:
                # 提取所有段落
                paragraphs = soup.select('p')
                content = "\n".join([p.text.strip() for p in paragraphs if len(p.text.strip()) > 10])
            
            # 仍然为空，返回提示
            if not content:
                content = "微博热搜详情需要登录查看，请点击下方原文链接访问微博获取详情。"
            
            return {
                'title': title,
                'content': content,
                'source_url': url,
                'images': self._extract_images(soup, url),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"解析微博内容失败: {e}")
            # 出错时返回模拟内容
            return self._generate_mock_content("微博热搜详情", url, 'weibo')
    
    def _fetch_toutiao_content(self, url):
        """获取今日头条相关内容"""
        response = self.request_get(url)
        if not response:
            # 返回模拟内容
            return self._generate_mock_content("今日头条热点", url, 'toutiao')
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试获取标题
            title_element = soup.select_one('h1, .article-title')
            title = title_element.text.strip() if title_element else "今日头条热点"
            
            # 尝试获取内容
            content_elements = soup.select('.article-content, .content')
            content = "\n".join([elem.text.strip() for elem in content_elements if elem.text.strip()])
            
            # 如果内容为空，尝试一般性提取
            if not content:
                # 提取所有段落
                paragraphs = soup.select('p')
                content = "\n".join([p.text.strip() for p in paragraphs if len(p.text.strip()) > 20])
            
            # 仍然为空，返回提示
            if not content:
                content = "今日头条热点详情加载中，请点击下方原文链接查看完整内容。"
            
            return {
                'title': title,
                'content': content,
                'source_url': url,
                'images': self._extract_images(soup, url),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"解析今日头条内容失败: {e}")
            # 出错时返回模拟内容
            return self._generate_mock_content("今日头条热点", url, 'toutiao')
    
    def _fetch_douyin_content(self, url):
        """获取抖音相关内容"""
        response = self.request_get(url)
        if not response:
            # 返回模拟内容
            return self._generate_mock_content("抖音热点", url, 'douyin')
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试获取标题
            title_element = soup.select_one('h1, .video-title')
            title = title_element.text.strip() if title_element else "抖音热点"
            
            # 尝试获取内容
            content_elements = soup.select('.desc, .content')
            content = "\n".join([elem.text.strip() for elem in content_elements if elem.text.strip()])
            
            # 如果内容为空，尝试一般性提取
            if not content:
                # 提取所有段落
                paragraphs = soup.select('p')
                content = "\n".join([p.text.strip() for p in paragraphs if len(p.text.strip()) > 10])
            
            # 仍然为空，返回提示
            if not content:
                content = "抖音热点详情可能需要在抖音APP内查看，请点击下方原文链接了解更多。"
            
            return {
                'title': title,
                'content': content,
                'source_url': url,
                'images': self._extract_images(soup, url),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"解析抖音内容失败: {e}")
            # 出错时返回模拟内容
            return self._generate_mock_content("抖音热点", url, 'douyin')
    
    def _fetch_international_content(self, url):
        """获取国际相关内容"""
        response = self.request_get(url)
        if not response:
            # 根据URL判断类型
            site_type = 'buzzing'
            if 'twitter' in url or 'x.com' in url or 'x.buzzing' in url:
                site_type = 'twitter'
            elif 'youtube' in url:
                site_type = 'youtube'
                
            # 返回模拟内容
            return self._generate_mock_content("国际热点", url, site_type)
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试获取标题
            title_element = soup.select_one('h1, .title, .headline')
            title = title_element.text.strip() if title_element else "国际热点"
            
            # 尝试获取内容
            content_elements = soup.select('article, .content, .article-body')
            content = "\n".join([elem.text.strip() for elem in content_elements if elem.text.strip()])
            
            # 如果内容为空，尝试一般性提取
            if not content:
                # 提取所有段落
                paragraphs = soup.select('p')
                content = "\n".join([p.text.strip() for p in paragraphs if len(p.text.strip()) > 20])
            
            # 仍然为空，返回提示
            if not content:
                content = "该国际热点内容可能需要特定网络环境查看，请点击下方原文链接了解更多。"
            
            return {
                'title': title,
                'content': content,
                'source_url': url,
                'images': self._extract_images(soup, url),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"解析国际内容失败: {e}")
            # 根据URL判断类型
            site_type = 'buzzing'
            if 'twitter' in url or 'x.com' in url or 'x.buzzing' in url:
                site_type = 'twitter'
            elif 'youtube' in url:
                site_type = 'youtube'
                
            # 返回模拟内容
            return self._generate_mock_content("国际热点", url, site_type)
    
    def _extract_images(self, soup, base_url):
        """从页面提取图片"""
        images = []
        img_elements = soup.select('img')
        
        base_domain = "{uri.scheme}://{uri.netloc}".format(uri=urlparse(base_url))
        
        for img in img_elements:
            # 忽略小图标和表情
            if 'width' in img.attrs and img['width'] and int(img['width']) < 100:
                continue
            if 'height' in img.attrs and img['height'] and int(img['height']) < 100:
                continue
            
            src = None
            # 考虑多种图片属性
            for attr in ['src', 'data-src', 'data-original', 'data-actualsrc']:
                if attr in img.attrs and img[attr]:
                    src = img[attr]
                    break
            
            if not src:
                continue
                
            # 处理相对URL
            if src.startswith('//'):
                src = 'https:' + src
            elif src.startswith('/'):
                src = base_domain + src
            
            # 忽略base64图片和gif
            if 'data:image' in src or src.lower().endswith('.gif'):
                continue
                
            images.append(src)
            
            # 限制图片数量
            if len(images) >= 3:
                break
                
        return images
    
    def _generate_mock_content(self, title, url, site_type):
        """生成模拟内容，作为获取失败的备选方案"""
        mock_content = ""
        
        # 根据不同网站类型生成不同的模拟内容
        if site_type == 'baidu':
            mock_content = f"""【模拟内容】由于网络原因无法获取实际内容，以下为模拟数据：

该热点在百度热榜引起广泛关注，多家媒体对此进行了报道。
根据百度热搜数据显示，相关话题已有数百万次阅读和讨论。

可能的热点背景：
1. 该事件可能涉及社会热点、娱乐新闻或重大事件
2. 多个用户在百度相关搜索中对此表达了看法
3. 相关话题可能在其他平台也有广泛传播

您可以复制原文链接在浏览器中打开查看完整内容。
"""
        elif site_type in ['weibo', 'toutiao', 'douyin']:
            mock_content = f"""【模拟内容】由于网络原因无法获取实际内容，以下为模拟数据：

该热点在社交媒体引发大量讨论，多位用户分享了自己的观点。
话题参与度高，互动频繁，是当前网络热点之一。

可能的热点要点：
1. 该话题可能涉及时事、明星动态或社会现象
2. 存在不同观点和讨论角度
3. 相关内容可能包含图片、视频等多媒体元素

您可以复制原文链接在浏览器中打开查看完整内容。
"""
        elif site_type in ['buzzing', 'twitter', 'youtube']:
            mock_content = f"""【模拟内容】由于网络原因无法获取实际国际内容，以下为模拟数据：

该国际热点在全球范围内受到关注，多个国家的用户对此进行了讨论。
相关话题在社交媒体平台上被广泛传播和评论。

可能的热点概述：
1. 该话题可能涉及国际事务、全球新闻或跨国现象
2. 不同地区和文化背景的人们可能持有不同观点
3. 内容可能涵盖多语种信息和多元文化视角

您可以通过合适的网络环境，复制原文链接在浏览器中打开查看完整内容。
"""
        else:
            mock_content = f"""【模拟内容】由于网络原因无法获取实际内容，以下为模拟数据：

该热点在互联网上引起了一定关注，相关讨论呈现多样化特点。
根据公开数据显示，该话题具有一定的时效性和传播价值。

您可以复制原文链接在浏览器中打开查看完整内容。
"""
            
        return {
            'title': title,
            'content': mock_content,
            'source_url': url,
            'images': [],
            'success': True
        }

if __name__ == "__main__":
    # 测试代码
    fetcher = ContentFetcher()
    
    test_urls = [
        "https://www.baidu.com/s?wd=世界新闻",
        "https://s.weibo.com/weibo?q=热搜",
        "https://www.toutiao.com/search?keyword=热点新闻"
    ]
    
    for url in test_urls:
        print(f"\n测试URL: {url}")
        content = fetcher.fetch_content(url)
        print(f"标题: {content['title']}")
        print(f"内容摘要: {content['content'][:100]}...")
        print(f"图片数量: {len(content['images'])}")
        print(f"成功: {content['success']}") 