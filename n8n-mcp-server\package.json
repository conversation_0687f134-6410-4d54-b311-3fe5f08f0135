{"name": "@leonardsellem/n8n-mcp-server", "version": "0.1.8", "description": "Model Context Protocol (MCP) server for n8n workflow automation", "main": "build/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node build/index.js", "dev": "tsc -w", "lint": "eslint --ext .ts src/", "test": "node --experimental-vm-modules run-tests.js", "test:watch": "node --experimental-vm-modules run-tests.js --watch", "test:coverage": "node --experimental-vm-modules run-tests.js --coverage", "prepare": "npm run build"}, "bin": {"n8n-mcp-server": "build/index.js"}, "keywords": ["mcp", "n8n", "workflow", "automation", "ai"], "author": "<PERSON> (https://sellem.me)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/leonardsellem/n8n-mcp-server.git"}, "bugs": {"url": "https://github.com/leonardsellem/n8n-mcp-server/issues"}, "homepage": "https://github.com/leonardsellem/n8n-mcp-server#readme", "files": ["build", "README.md", "LICENSE", "package.json"], "dependencies": {"@modelcontextprotocol/sdk": "^0.7.0", "axios": "^1.6.2", "dotenv": "^16.3.1", "find-config": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@types/find-config": "^1.0.4", "@types/jest": "^29.5.14", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-jest": "^29.7.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}