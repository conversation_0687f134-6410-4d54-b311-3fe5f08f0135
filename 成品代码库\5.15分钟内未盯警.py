import pandas as pd

# 读取Excel文件
df1 = pd.read_excel('派出所名称治理后的执法记录仪日志.xlsx')
df2 = pd.read_excel('筛选签收时间和派出所后的警情.xlsx')
df_jq = pd.read_excel('筛选签收时间和派出所后的警情.xlsx')

# 将“接警单编号”列设置为文本格式
df2['接警单编号'] = df2['接警单编号'].astype(str)

# 左连接两个表
merged_df = pd.merge(df2, df1, left_on='管辖单位', right_on='操作部门', how='left')

# 将“签收时间”和“日志时间”列转换为日期时间格式
merged_df['签收时间'] = pd.to_datetime(merged_df['签收时间'])
merged_df['日志时间'] = pd.to_datetime(merged_df['日志时间'])

# 计算“日志时间”减去“签收时间”的差值
merged_df['时间差'] = merged_df['日志时间'] - merged_df['签收时间']

# 筛选时间差在0分0秒到15分0秒之间的记录
filtered_df = merged_df[(merged_df['时间差'] >= pd.Timedelta(minutes=0)) & (merged_df['时间差'] <= pd.Timedelta(minutes=15))]

# 使用.loc明确指定行和列，保留“接警单编号”列为文本格式
filtered_df.loc[:, '接警单编号'] = filtered_df['接警单编号'].astype(str)

# 保存筛选结果到新的Excel文件
filtered_df.to_excel('筛选结果.xlsx', index=False)

# 读取筛选结果
df_filtered = pd.read_excel('筛选结果.xlsx')

# 将“接警单编号”列设置为文本格式
df_filtered['接警单编号'] = df_filtered['接警单编号'].astype(str)
df_jq['接警单编号'] = df_jq['接警单编号'].astype(str)

# 找出“警情.xlsx”中“接警单编号”列中不在“筛选结果.xlsx”中的内容
result_df = df_jq[~df_jq['接警单编号'].isin(df_filtered['接警单编号'])]

# 保留“警情.xlsx”中的字段并生成新表格
result_df.to_excel('最终结果.xlsx', index=False)

print("任务完成，结果已保存到'最终结果.xlsx'")
