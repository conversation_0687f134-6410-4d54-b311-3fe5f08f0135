#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
境外情报汇总工具 - 从X平台获取特定博主的信息并生成简报
支持WIN7/WIN10/WIN11系统
"""

import sys
import os
import json
import datetime
import requests
import logging
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QCalendarWidget, QTextEdit, QProgressBar,
    QMessageBox, QDialog, QLineEdit, QCheckBox, QDialogButtonBox,
    QGroupBox, QGridLayout, QFileDialog
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QFont

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='intelligence_collector.log'
)
logger = logging.getLogger('IntelligenceCollector')

# 配置文件路径
CONFIG_FILE = 'config/proxy_config.json'

# 目标博主配置
TARGET_USERS = [
    {
        'name': '李老师不是你老师',
        'username': 'whyyoutouzhele',
        'display_name': '李老师'
    },
    {
        'name': '多伦多方脸',
        'username': 'torontobigface',
        'display_name': '方脸'
    },
    {
        'name': '蔡慎坤',
        'username': 'cskun1989',
        'display_name': '蔡慎坤'
    },
    {
        'name': 'Hu Xijin 胡锡进',
        'username': 'HuXijin_GT',
        'display_name': '胡锡进'
    },
    {
        'name': '环球时报',
        'username': 'globaltimesnews',
        'display_name': '环球时报'
    }
]

# API配置
API_CONFIG = {
    'bearer_token': '',  # X平台API的Bearer Token
    'api_key': '',       # API密钥
    'api_secret': ''     # API密钥密文
}

class ProxySettingsDialog(QDialog):
    """代理设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('代理设置')
        self.setMinimumWidth(400)
        
        layout = QVBoxLayout(self)
        
        # 代理配置组
        proxy_group = QGroupBox('代理服务器设置')
        proxy_layout = QGridLayout()
        
        self.enable_proxy = QCheckBox('启用代理')
        proxy_layout.addWidget(self.enable_proxy, 0, 0, 1, 2)
        
        proxy_layout.addWidget(QLabel('HTTP代理:'), 1, 0)
        self.http_proxy = QLineEdit()
        self.http_proxy.setPlaceholderText('http://地址:端口')
        proxy_layout.addWidget(self.http_proxy, 1, 1)
        
        proxy_layout.addWidget(QLabel('HTTPS代理:'), 2, 0)
        self.https_proxy = QLineEdit()
        self.https_proxy.setPlaceholderText('http://地址:端口')
        proxy_layout.addWidget(self.https_proxy, 2, 1)
        
        proxy_group.setLayout(proxy_layout)
        layout.addWidget(proxy_group)
        
        # 说明文本
        note = QLabel('注意：访问X平台需要使用代理服务器。\n代理格式示例：http://127.0.0.1:7890')
        note.setWordWrap(True)
        layout.addWidget(note)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        # 加载现有设置
        self.load_settings()
    
    def load_settings(self):
        """加载代理设置"""
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.enable_proxy.setChecked(config.get('enabled', False))
                    self.http_proxy.setText(config.get('http', ''))
                    self.https_proxy.setText(config.get('https', ''))
        except Exception as e:
            logger.error(f'加载代理设置失败: {e}')
    
    def save_settings(self):
        """保存代理设置"""
        config = {
            'enabled': self.enable_proxy.isChecked(),
            'http': self.http_proxy.text().strip(),
            'https': self.https_proxy.text().strip()
        }
        
        try:
            os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            logger.error(f'保存代理设置失败: {e}')
            return False

class ContentFetcher(QThread):
    """内容获取线程"""
    
    progress = pyqtSignal(int)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, date, parent=None):
        super().__init__(parent)
        self.date = date
        self.session = requests.Session()
    
    def get_proxy_config(self):
        """获取代理配置"""
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    if config.get('enabled'):
                        return {
                            'http': config.get('http'),
                            'https': config.get('https')
                        }
        except Exception as e:
            logger.error(f'读取代理配置失败: {e}')
        return None
    
    def fetch_user_tweets(self, username):
        """获取指定用户的推文"""
        try:
            # 设置X平台API的基本URL
            base_url = 'https://api.twitter.com/2/users/by/username/'
            
            # 设置请求头，包含认证信息
            headers = {
                'Authorization': 'Bearer YOUR_BEARER_TOKEN',  # 需要替换为实际的Bearer Token
                'Content-Type': 'application/json'
            }
            
            # 首先获取用户ID
            user_response = self.session.get(
                f'{base_url}{username}',
                headers=headers
            )
            user_response.raise_for_status()
            user_data = user_response.json()
            
            if 'data' not in user_data:
                logger.error(f'未找到用户: {username}')
                return []
            
            user_id = user_data['data']['id']
            
            # 获取用户的推文
            tweets_url = f'https://api.twitter.com/2/users/{user_id}/tweets'
            params = {
                'max_results': 10,  # 获取最近10条推文
                'tweet.fields': 'created_at,attachments',
                'expansions': 'attachments.media_keys',
                'media.fields': 'url,preview_image_url'
            }
            
            tweets_response = self.session.get(
                tweets_url,
                headers=headers,
                params=params
            )
            tweets_response.raise_for_status()
            tweets_data = tweets_response.json()
            
            # 处理推文数据
            tweets = []
            media_lookup = {}
            
            # 创建媒体查找表
            if 'includes' in tweets_data and 'media' in tweets_data['includes']:
                for media in tweets_data['includes']['media']:
                    media_lookup[media['media_key']] = media
            
            # 处理每条推文
            for tweet in tweets_data.get('data', []):
                tweet_obj = {
                    'text': tweet['text'],
                    'images': [],
                    'videos': []
                }
                
                # 处理媒体附件
                if 'attachments' in tweet and 'media_keys' in tweet['attachments']:
                    for media_key in tweet['attachments']['media_keys']:
                        if media_key in media_lookup:
                            media = media_lookup[media_key]
                            if media['type'] == 'photo':
                                tweet_obj['images'].append(media['url'])
                            elif media['type'] == 'video':
                                tweet_obj['videos'].append(
                                    media.get('preview_image_url', '')
                                )
                
                tweets.append(tweet_obj)
            
            return tweets
            
        except Exception as e:
            logger.error(f'获取推文失败: {e}')
            return []
    
    def run(self):
        """执行获取任务"""
        try:
            # 设置代理
            proxies = self.get_proxy_config()
            if proxies:
                self.session.proxies.update(proxies)
            
            results = {}
            total_users = len(TARGET_USERS)
            
            for i, user in enumerate(TARGET_USERS, 1):
                try:
                    tweets = self.fetch_user_tweets(user['username'])
                    results[user['display_name']] = tweets
                    self.progress.emit(int(i * 100 / total_users))
                except Exception as e:
                    logger.error(f'获取{user["name"]}的推文失败: {e}')
            
            self.finished.emit(results)
            
        except Exception as e:
            self.error.emit(f'获取内容失败: {str(e)}')

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle('境外情报汇总工具')
        self.setMinimumSize(800, 600)
        
        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 顶部控制区
        control_layout = QHBoxLayout()
        
        # 日期选择
        self.calendar = QCalendarWidget()
        self.calendar.setMaximumDate(datetime.date.today())
        control_layout.addWidget(self.calendar)
        
        # 按钮区
        button_layout = QVBoxLayout()
        
        self.fetch_button = QPushButton('获取情报')
        self.fetch_button.clicked.connect(self.fetch_content)
        button_layout.addWidget(self.fetch_button)
        
        self.save_button = QPushButton('保存简报')
        self.save_button.clicked.connect(self.save_report)
        self.save_button.setEnabled(False)
        button_layout.addWidget(self.save_button)
        
        self.proxy_button = QPushButton('代理设置')
        self.proxy_button.clicked.connect(self.show_proxy_settings)
        button_layout.addWidget(self.proxy_button)
        
        button_layout.addStretch()
        control_layout.addLayout(button_layout)
        
        layout.addLayout(control_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 内容显示区
        self.content_edit = QTextEdit()
        self.content_edit.setReadOnly(True)
        layout.addWidget(self.content_edit)
        
        # 初始化成员变量
        self.fetcher = None
        self.current_results = None
    
    def show_proxy_settings(self):
        """显示代理设置对话框"""
        dialog = ProxySettingsDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            dialog.save_settings()
    
    def fetch_content(self):
        """获取内容"""
        if self.fetcher and self.fetcher.isRunning():
            return
        
        selected_date = self.calendar.selectedDate().toPyDate()
        
        self.fetch_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.content_edit.clear()
        
        self.fetcher = ContentFetcher(selected_date)
        self.fetcher.progress.connect(self.update_progress)
        self.fetcher.finished.connect(self.show_results)
        self.fetcher.error.connect(self.show_error)
        self.fetcher.start()
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def show_results(self, results):
        """显示获取结果"""
        self.current_results = results
        
        # 生成简报内容
        report = self.generate_report(results)
        self.content_edit.setHtml(report)
        
        self.fetch_button.setEnabled(True)
        self.save_button.setEnabled(True)
        self.progress_bar.setVisible(False)
    
    def show_error(self, message):
        """显示错误信息"""
        QMessageBox.warning(self, '错误', message)
        self.fetch_button.setEnabled(True)
        self.progress_bar.setVisible(False)
    
    def generate_report(self, results):
        """生成简报内容"""
        selected_date = self.calendar.selectedDate().toString('yyyy年MM月dd日')
        
        html = f'''
        <html>
        <head>
            <style>
                body {{ font-family: Arial, 'Microsoft YaHei', sans-serif; }}
                h1 {{ color: #333; text-align: center; }}
                h2 {{ color: #666; margin-top: 20px; }}
                .content {{ margin: 10px 0; padding: 10px; background: #f5f5f5; }}
                .media {{ color: #0066cc; }}
            </style>
        </head>
        <body>
            <h1>{selected_date} 境外情报汇总</h1>
        '''
        
        for user, tweets in results.items():
            if tweets:
                html += f'<h2>{user}</h2>\n'
                for tweet in tweets:
                    html += '<div class="content">'
                    html += f'<p>{tweet["text"]}</p>'
                    
                    if tweet['images']:
                        html += '<p class="media">图片链接：</p>'
                        for img in tweet['images']:
                            html += f'<p><a href="{img}">{img}</a></p>'
                    
                    if tweet['videos']:
                        html += '<p class="media">视频链接：</p>'
                        for video in tweet['videos']:
                            html += f'<p><a href="{video}">{video}</a></p>'
                    
                    html += '</div>'
        
        html += '</body></html>'
        return html
    
    def save_report(self):
        """保存简报"""
        if not self.current_results:
            return
        
        selected_date = self.calendar.selectedDate().toString('yyyyMMdd')
        filename, _ = QFileDialog.getSaveFileName(
            self,
            '保存简报',
            f'境外情报简报_{selected_date}.html',
            'HTML文件 (*.html)'
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.content_edit.toHtml())
                QMessageBox.information(self, '成功', '简报已保存')
            except Exception as e:
                QMessageBox.warning(self, '错误', f'保存简报时出错: {str(e)}')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())