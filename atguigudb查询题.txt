#查询平均工资最低的department_id
#子查询当作表，要起别名
#方式1
SELECT department_id
FROM employees
GROUP BY department_id
HAVING AVG(salary) =
(SELECT MIN(avg_salary)
FROM (SELECT AVG(salary) avg_salary
FROM employees
WHERE department_id IS NOT NULL
GROUP BY department_id)
 dept_avg_salary)
#方式2
SELECT department_id
FROM employees
GROUP BY department_id
HAVING AVG(salary) <= ALL
(SELECT AVG(salary)
FROM employees
WHERE department_id IS NOT NULL
GROUP BY department_id)
#方式3
SELECT department_id
FROM employees
WHERE department_id IS NOT NULL
GROUP BY department_id
ORDER BY AVG(salary)
LIMIT 0,1