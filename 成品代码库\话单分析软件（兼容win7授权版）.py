import os
import sys
import hashlib
import uuid
import json

# 设置Windows DPI感知
if os.name == 'nt':
    try:
        from ctypes import windll
        windll.shcore.SetProcessDpiAwareness(1)
    except:
        pass

# 确保正确处理中文路径
if hasattr(sys, 'frozen'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

# 设置PyQt5的高DPI缩放
os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'

import sys
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QTableWidget, QTableWidgetItem,
                           QFileDialog, QComboBox, QLabel, QHeaderView, QMessageBox,
                           QDialog, QLineEdit, QGridLayout, QCheckBox, QStyledItemDelegate,
                           QMenu, QScrollArea, QWidgetAction, QGroupBox, QDateTimeEdit)
from PyQt5.QtCore import Qt, QPoint
import datetime
from functools import partial

class AuthDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('软件授权')
        self.setFixedSize(600, 400)  # 增加窗口大小
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
            }
            QLabel {
                font-size: 11pt;
                color: #333333;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
                font-size: 11pt;
                min-height: 20px;
            }
            QPushButton {
                padding: 8px 15px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setSpacing(20)  # 增加间距
        layout.setContentsMargins(30, 30, 30, 30)  # 增加边距

        # 标题
        title = QLabel('软件授权验证')
        title.setStyleSheet('font-size: 18pt; color: #1565C0; margin-bottom: 20px;')
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 机器码显示
        self.machine_code = self.get_machine_code()
        machine_code_layout = QHBoxLayout()
        machine_code_label = QLabel('机器码:')
        machine_code_label.setFixedWidth(80)  # 固定标签宽度
        self.machine_code_display = QLineEdit(self.machine_code)
        self.machine_code_display.setReadOnly(True)
        copy_btn = QPushButton('复制')
        copy_btn.setFixedWidth(80)
        copy_btn.clicked.connect(self.copy_machine_code)
        machine_code_layout.addWidget(machine_code_label)
        machine_code_layout.addWidget(self.machine_code_display)
        machine_code_layout.addWidget(copy_btn)
        layout.addLayout(machine_code_layout)

        # 授权码输入
        auth_code_layout = QHBoxLayout()
        auth_code_label = QLabel('授权码:')
        auth_code_label.setFixedWidth(80)  # 固定标签宽度
        self.auth_code_input = QLineEdit()
        self.auth_code_input.setPlaceholderText('请输入授权码')
        auth_code_layout.addWidget(auth_code_label)
        auth_code_layout.addWidget(self.auth_code_input)
        layout.addLayout(auth_code_layout)

        # 授权说明
        note_layout = QVBoxLayout()
        note = QLabel('请联系以下人员获取授权：')
        note.setStyleSheet('color: #333333; font-size: 11pt; margin-top: 20px;')
        note.setAlignment(Qt.AlignCenter)
        contact = QLabel('台州市公安局 解晟\n联系电话：587890、13616685757')
        contact.setStyleSheet('color: #000000; font-size: 12pt; font-weight: bold;')
        contact.setAlignment(Qt.AlignCenter)
        note_layout.addWidget(note)
        note_layout.addWidget(contact)
        layout.addLayout(note_layout)

        # 添加一个弹性空间
        layout.addStretch()

        # 按钮
        button_layout = QHBoxLayout()
        verify_btn = QPushButton('验证授权')
        verify_btn.setFixedWidth(120)  # 加宽按钮
        verify_btn.clicked.connect(self.verify_auth)
        cancel_btn = QPushButton('取消')
        cancel_btn.setFixedWidth(120)  # 加宽按钮
        cancel_btn.clicked.connect(self.reject)
        button_layout.addStretch()
        button_layout.addWidget(verify_btn)
        button_layout.addSpacing(20)  # 按钮之间的间距
        button_layout.addWidget(cancel_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

    def get_machine_code(self):
        try:
            # 获取多个硬件信息
            cpu_id = ''
            disk_id = ''
            
            # 获取CPU信息
            try:
                import wmi
                c = wmi.WMI()
                for cpu in c.Win32_Processor():
                    cpu_id = cpu.ProcessorId.strip()
                    break
            except:
                # 如果获取CPU ID失败，使用备用方法
                import subprocess
                cpu_info = subprocess.check_output('wmic cpu get ProcessorId').decode()
                if cpu_info:
                    cpu_id = cpu_info.split('\n')[1].strip()
            
            # 获取硬盘序列号
            try:
                c = wmi.WMI()
                for disk in c.Win32_DiskDrive():
                    if disk.SerialNumber:
                        disk_id = disk.SerialNumber.strip()
                        break
            except:
                # 如果获取硬盘序列号失败，使用备用方法
                import subprocess
                disk_info = subprocess.check_output('wmic diskdrive get SerialNumber').decode()
                if disk_info:
                    disk_id = disk_info.split('\n')[1].strip()
            
            # 组合硬件信息
            hardware_str = f"{cpu_id}:{disk_id}:{uuid.getnode()}"
            # 生成16位的机器码
            machine_code = hashlib.md5(hardware_str.encode()).hexdigest()[:16]
            # 每4位添加一个分隔符，便于阅读
            formatted_code = '-'.join([machine_code[i:i+4] for i in range(0, 16, 4)])
            return formatted_code.upper()
        except:
            # 如果以上方法都失败，使用备用方法
            backup_str = f"TZCJ{uuid.getnode()}".encode()
            machine_code = hashlib.md5(backup_str).hexdigest()[:16]
            formatted_code = '-'.join([machine_code[i:i+4] for i in range(0, 16, 4)])
            return formatted_code.upper()

    def verify_auth(self):
        auth_code = self.auth_code_input.text().strip()
        if not auth_code:
            QMessageBox.warning(self, '警告', '请输入授权码！')
            return

        if self.check_auth_code(auth_code):
            # 保存授权信息
            self.save_auth_info(auth_code)
            QMessageBox.information(self, '成功', '授权验证成功！')
            self.accept()
        else:
            QMessageBox.warning(self, '错误', '授权码无效！')

    def check_auth_code(self, auth_code):
        # 验证授权码
        key = "TaiZhouPolice2024"
        expected_code = hashlib.md5(f"{self.machine_code}:{key}".encode()).hexdigest().upper()
        return auth_code.upper() == expected_code

    def save_auth_info(self, auth_code):
        # 保存授权信息到文件
        auth_info = {
            'machine_code': self.machine_code,
            'auth_code': auth_code
        }
        try:
            with open('auth.json', 'w') as f:
                json.dump(auth_info, f)
        except Exception as e:
            print(f"保存授权信息时出错：{str(e)}")

    def copy_machine_code(self):
        clipboard = QApplication.clipboard()
        clipboard.setText(self.machine_code)
        QMessageBox.information(self, '成功', '机器码已复制到剪贴板！')

class CallRecordAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 验证授权
        if not self.check_auth():
            auth_dialog = AuthDialog(self)
            if auth_dialog.exec_() != QDialog.Accepted:
                sys.exit()
        
        self.setWindowTitle('话单分析软件')
        self.setGeometry(100, 100, 1200, 800)
        
        # 存储所有导入的数据
        self.all_data = pd.DataFrame()
        
        # 定义必需字段
        self.required_fields = {
            '己方号码': None,
            '对方号码': None,
            '呼叫日期': None,
            '呼叫时间': None,
            '截获时间': None
        }
        
        # 定义所有可能的字段
        self.all_fields = [
            '己方号码', '己方卡号', '己方机身码', '己方位置区', '己方小区',
            '己方基站名称', '对方号码', '呼叫日期', '呼叫时间', '截获时间',
            '时长', '呼叫类型','第三方号码', '己方归属地', '己方归属地名称', '对方归属地',
            '对方归属地名称','结束位置区','结束小区', '己方通话地', '己方通话地名称', '对方通话地',
            '对方通话地名称'
        ]
        
        # 添加新的属性来存储分析结果和排序状态
        self.current_display_data = None  # 当前显示的数据
        self.analysis_type = None  # 当前分析类型
        self.sort_column = -1  # 当前排序的列
        self.sort_order = Qt.AscendingOrder  # 当前排序顺序
        
        # 添加一个字典来存储每列的筛选状态
        self.column_filter_states = {}
        
        self.df = None
        self.original_df = None  # 新增:保存原始数据
        
        # 在 init_ui 方法之前添加导出按钮的状态
        self.export_btn = None
        
        # 添加新的属性来存储每个功能页面的排序状态
        self.sort_states = {
            'original': {'column': -1, 'order': Qt.AscendingOrder},  # 原始话单
            'counter': {'column': -1, 'order': Qt.AscendingOrder},   # 对方号码统计
            'common': {'column': -1, 'order': Qt.AscendingOrder},    # 共同联系人分析
            'disappeared': {'column': -1, 'order': Qt.AscendingOrder}, # 消失号码分析
            'new': {'column': -1, 'order': Qt.AscendingOrder},       # 新出现号码分析
            'area': {'column': -1, 'order': Qt.AscendingOrder},      # 区域活动分析
            'active_period': {'column': -1, 'order': Qt.AscendingOrder} # 活跃时段分析
        }
        
        self.init_ui()
    
    def check_auth(self):
        try:
            # 检查授权文件是否存在
            if not os.path.exists('auth.json'):
                return False

            # 读取授权信息
            with open('auth.json', 'r') as f:
                auth_info = json.load(f)

            # 获取当前机器码
            current_machine_code = AuthDialog.get_machine_code(self)

            # 验证机器码是否匹配
            if auth_info['machine_code'] != current_machine_code:
                return False

            # 验证授权码
            key = "TaiZhouPolice2024"
            expected_code = hashlib.md5(f"{current_machine_code}:{key}".encode()).hexdigest().upper()
            return auth_info['auth_code'].upper() == expected_code

        except Exception as e:
            print(f"验证授权时出错：{str(e)}")
            return False
    
    def init_ui(self):
        # 创建中心部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建按钮域
        button_layout = QHBoxLayout()
        
        # 添加文件按钮
        upload_btn = QPushButton('上传文件', self)
        upload_btn.clicked.connect(self.upload_files)
        button_layout.addWidget(upload_btn)
        
        # 添加分析按钮
        analyze_btn = QPushButton('对方号码统计', self)
        analyze_btn.clicked.connect(self.analyze_counter_numbers)
        button_layout.addWidget(analyze_btn)
        
        common_contacts_btn = QPushButton('共同联系人分析', self)
        common_contacts_btn.clicked.connect(self.analyze_common_contacts)
        button_layout.addWidget(common_contacts_btn)
        
        # 添加消失号码分析按钮
        disappeared_numbers_btn = QPushButton('消失号码分析', self)
        disappeared_numbers_btn.clicked.connect(self.analyze_disappeared_numbers)
        button_layout.addWidget(disappeared_numbers_btn)
        
        # 添加新出现号码析按钮
        new_numbers_btn = QPushButton('新出现号码分析', self)
        new_numbers_btn.clicked.connect(self.analyze_new_numbers)
        button_layout.addWidget(new_numbers_btn)
        
        # 添加区域活动分析按钮
        area_activity_btn = QPushButton('区域活动分析', self)
        area_activity_btn.clicked.connect(self.analyze_area_activity)
        button_layout.addWidget(area_activity_btn)
        
        # 添加活跃时段分析按钮
        active_period_btn = QPushButton('活跃时段分析', self)
        active_period_btn.clicked.connect(self.analyze_active_period)
        button_layout.addWidget(active_period_btn)
        
        # 添加自定义排序按钮（移到活跃时段分析后面）
        custom_sort_btn = QPushButton('自定义排序', self)
        custom_sort_btn.clicked.connect(self.show_custom_sort)
        button_layout.addWidget(custom_sort_btn)
        
        # 添加返回上一步按钮
        self.return_btn = QPushButton('返回上一步', self)
        self.return_btn.clicked.connect(self.return_to_previous)
        self.return_btn.setVisible(False)  # 初始时隐藏返回按钮
        button_layout.addWidget(self.return_btn)
        
        # 添加返回原始话单按钮
        self.return_to_original_btn = QPushButton('返回原始话单', self)
        self.return_to_original_btn.clicked.connect(self.return_to_original)
        self.return_to_original_btn.setVisible(False)  # 初始时隐藏返回按钮
        button_layout.addWidget(self.return_to_original_btn)
        
        # 在button_layout中添加导出按钮
        self.export_btn = QPushButton('导出数据', self)
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setVisible(False)  # 初始时隐藏导出按钮
        button_layout.addWidget(self.export_btn)
        
        main_layout.addLayout(button_layout)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setSortingEnabled(True)  # 启用排序功能
        self.table.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        # 将单击事件改为双击事件
        self.table.cellDoubleClicked.connect(self.show_call_details)
        
        # 添加筛选
        self.filter_row = QWidget()
        self.filter_layout = QHBoxLayout(self.filter_row)
        self.filter_layout.setContentsMargins(0, 0, 0, 0)
        self.filter_inputs = {}  # 存储筛选输入框
        
        main_layout.addWidget(self.filter_row)
        main_layout.addWidget(self.table)
        
        # 添加版权信息标签
        copyright_label = QLabel('版权所有：台州市公安局 解晟')
        copyright_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        # 设置样式
        copyright_label.setStyleSheet("""
            QLabel {
                color: #000000;
                padding: 5px;
                font-size: 9pt;
                font-family: "Microsoft YaHei";
            }
        """)
        main_layout.addWidget(copyright_label)
    
    def upload_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择文件",
            "",
            "All Files (*.csv *.xlsx *.xls *.txt);;CSV Files (*.csv);;Excel Files (*.xlsx *.xls);;Text Files (*.txt)"
        )
        
        if files:
            # 创建一个临时的DataFrame来存储所有文件的数据
            temp_df = pd.DataFrame()
            
            for file_path in files:
                try:
                    # 根据文件类型读取数据
                    if file_path.endswith(('.xlsx', '.xls')):
                        df = pd.read_excel(
                            file_path,
                            dtype={
                                '己方机身码': str,
                                '对方号码': str,
                                '己方卡号': str
                            }
                        )
                    elif file_path.endswith('.csv'):
                        # 尝试不同的编码格式读取CSV文件
                        encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'ansi']
                        df = None
                        
                        for encoding in encodings:
                            try:
                                df = pd.read_csv(
                                    file_path,
                                    dtype={
                                        '己方机身码': str,
                                        '对方号码': str,
                                        '己方卡号': str
                                    },
                                    encoding=encoding
                                )
                                break  # 如果成功读取，跳出循环
                            except UnicodeDecodeError:
                                continue  # 如果当前编码失败，尝试下一个
                            except Exception as e:
                                print(f"使用 {encoding} 编码读取文件时出错：{str(e)}")
                                continue
                        
                        if df is None:
                            raise ValueError(f"无法使用支持的编码格式读取文件，请检查文件编码")
                        
                    elif file_path.endswith('.txt'):
                        # 对txt文件也使用相同的编码处理方式
                        encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'ansi']
                        df = None
                        
                        for encoding in encodings:
                            try:
                                df = pd.read_csv(
                                    file_path,
                                    sep='\t',
                                    dtype={
                                        '己方机身码': str,
                                        '对方号码': str,
                                        '己方卡号': str
                                    },
                                    encoding=encoding
                                )
                                break
                            except UnicodeDecodeError:
                                continue
                            except Exception as e:
                                print(f"使用 {encoding} 编码读取文件时出错：{str(e)}")
                                continue
                        
                        if df is None:
                            raise ValueError(f"无法使用支持的编码格式读取文件，请检查文件编码")
                    
                    # 为每个文件显示字段匹配对话框
                    dialog = FieldMatchDialog(self, df, self.all_fields)
                    if dialog.exec_() == QDialog.Accepted:
                        field_mappings = dialog.field_mappings
                        
                        # 检查必要的列是否存在
                        missing_columns = [col for col in field_mappings.keys() if col not in df.columns]
                        if missing_columns:
                            raise ValueError(f"文件缺少必要的列: {', '.join(missing_columns)}")
                        
                        # 只选择需要的列并重命名
                        selected_df = df[list(field_mappings.keys())]
                        renamed_df = selected_df.rename(columns=field_mappings)
                        
                        # 统一时间格式处理
                        if '截获时间' in renamed_df.columns:
                            try:
                                # 将截获时间转换为datetime格式
                                renamed_df['截获时间'] = pd.to_datetime(renamed_df['截获时间'], errors='coerce')
                                
                                # 如果没有呼叫日期和呼叫时间列，从截获时间中提取
                                if '呼叫日期' not in renamed_df.columns:
                                    renamed_df['呼叫日期'] = renamed_df['截获时间'].dt.strftime('%Y/%m/%d')
                                if '呼叫时间' not in renamed_df.columns:
                                    renamed_df['呼叫时间'] = renamed_df['截获时间'].dt.strftime('%H:%M:%S')
                                
                                # 如果已有呼叫日期和呼叫时间列但有空值，用截获时间填充
                                if '呼叫日期' in renamed_df.columns and '呼叫时间' in renamed_df.columns:
                                    mask = renamed_df['呼叫日期'].isna() | renamed_df['呼叫时间'].isna()
                                    renamed_df.loc[mask, '呼叫日期'] = renamed_df.loc[mask, '截获时间'].dt.strftime('%Y/%m/%d')
                                    renamed_df.loc[mask, '呼叫时间'] = renamed_df.loc[mask, '截获时间'].dt.strftime('%H:%M:%S')
                            except Exception as e:
                                print(f"转换截获时间时出错：{str(e)}")
                            
                        # 合并数据
                        if temp_df.empty:
                            temp_df = renamed_df
                        else:
                            # 确保两个DataFrame有相同的列
                            missing_cols = set(temp_df.columns) - set(renamed_df.columns)
                            for col in missing_cols:
                                renamed_df[col] = None
                            missing_cols = set(renamed_df.columns) - set(temp_df.columns)
                            for col in missing_cols:
                                temp_df[col] = None
                            
                            temp_df = pd.concat([temp_df, renamed_df], ignore_index=True)
                    else:
                        continue
                    
                except Exception as e:
                    QMessageBox.warning(self, '错误', f'处理文件 {file_path} 时出错：\n{str(e)}')
                    continue
            
            # 所有文件处理完成后，更新all_data并显示
            if not temp_df.empty:
                # 最终处理：确保所有时间格式统一
                try:
                    if '截获时间' in temp_df.columns:
                        temp_df['截获时间'] = pd.to_datetime(temp_df['截获时间'], errors='coerce')
                        # 确保呼叫日期和呼叫时间列存在且有值
                        if '呼叫日期' not in temp_df.columns:
                            temp_df['呼叫日期'] = temp_df['截获时间'].dt.strftime('%Y/%m/%d')
                        if '呼叫时间' not in temp_df.columns:
                            temp_df['呼叫时间'] = temp_df['截获时间'].dt.strftime('%H:%M:%S')
                        
                        # 填充空值
                        mask = temp_df['呼叫日期'].isna() | temp_df['呼叫时间'].isna()
                        temp_df.loc[mask, '呼叫日期'] = temp_df.loc[mask, '截获时间'].dt.strftime('%Y/%m/%d')
                        temp_df.loc[mask, '呼叫时间'] = temp_df.loc[mask, '截获时间'].dt.strftime('%H:%M:%S')
                except Exception as e:
                    print(f"最终时间格式统一时出错：{str(e)}")
                
                self.all_data = temp_df
                self.display_data(self.all_data)
    
    def display_data(self, df):
        """显示数据到表格中"""
        # 创建一个数据副本，避免修改原始数据
        display_df = df.copy()
        
        # 如果存在呼叫日期列，格式化日期
        if '呼叫日期' in display_df.columns:
            try:
                # 将呼叫日转换为datetime格式
                display_df['呼叫日期'] = pd.to_datetime(display_df['呼叫日期'])
                
                # 格式化日期，保持月份和日期的前导零
                def format_date(date):
                    if pd.isna(date):
                        return ''
                    return date.strftime('%Y/%m/%d').replace('/0', '/')
                
                display_df['呼叫日期'] = display_df['呼叫日期'].apply(format_date)
            except Exception as e:
                QMessageBox.warning(self, '警告', f'格式化日期时出错：{str(e)}')
        
        # 处理对方号码，去除小数点和末尾的0
        if '对方号码' in display_df.columns:
            try:
                def format_phone_number(number):
                    if pd.isna(number) or str(number).strip() == '':
                        return ''
                    # 将数值转换为字符串并移除小数点和末尾的0
                    number_str = str(number)
                    # 如果是科学计数法格式，先转换为普通数字字符串
                    if 'e' in number_str.lower():
                        number_str = f"{float(number):.0f}"
                    # 移除小数点和任何非数字字符
                    number_str = ''.join(filter(str.isdigit, number_str))
                    return number_str
                
                display_df['对方号码'] = display_df['对方号码'].apply(format_phone_number)
            except Exception as e:
                QMessageBox.warning(self, '警告', f'处理对方号码格式时出错{str(e)}')
        
        # 处理己方机身码，保持原始格式
        if '己方机身码' in display_df.columns:
            try:
                def format_imei(imei):
                    if pd.isna(imei) or str(imei).strip() == '':
                        return ''
                    # 将数值转换为字符串并保留前导零
                    imei_str = str(imei)
                    # 移除小数点和任何非数字字符
                    imei_str = ''.join(filter(str.isdigit, imei_str))
                    return imei_str
                
                display_df['己方机身码'] = display_df['己方机身码'].apply(format_imei)
            except Exception as e:
                pass  # 如果转换失败，保持原值
        
        # 处理己方卡号，去除小数点和末尾的0
        if '己方卡号' in display_df.columns:
            try:
                def format_card_number(number):
                    if pd.isna(number) or str(number).strip() == '':
                        return ''
                    # 移除小数点和任何非数字字符
                    number_str = ''.join(filter(str.isdigit, str(number)))
                    return number_str
                
                display_df['己方卡号'] = display_df['己方卡号'].apply(format_card_number)
            except Exception as e:
                pass  # 如果转换失败，保持原值
        
        # 处理带小数点的数字字段
        numeric_location_fields = [
            '己方位置区', '己方小区', '结束位置区', '结束小区',
            '己方通话地', '对方通话地'
        ]
        for field in numeric_location_fields:
            if field in display_df.columns:
                try:
                    # 将数字转换为整数字符串，去除小数点和末尾的0
                    display_df[field] = display_df[field].apply(
                        lambda x: str(int(float(x))) if pd.notnull(x) and str(x).strip() != '' else x
                    )
                except Exception as e:
                    pass  # 如果转换失败，保持原值
        
        # 设置表格的行数和列数
        self.table.setRowCount(len(display_df))
        self.table.setColumnCount(len(display_df.columns))
        
        # 设置表头
        self.table.setHorizontalHeaderLabels(display_df.columns)
        
        # 填充数据
        for i in range(len(display_df)):
            for j in range(len(display_df.columns)):
                item = QTableWidgetItem(str(display_df.iloc[i, j]))
                # 设置数据对齐方式
                if display_df.columns[j] in ['通话次数', '总时长', '关联己方号码数', '总通话次数']:
                    item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                else:
                    item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.table.setItem(i, j, item)
        
        # 调整列宽
        for col in range(self.table.columnCount()):
            # 先自动调整列宽
            self.table.horizontalHeader().setSectionResizeMode(col, QHeaderView.ResizeToContents)
            # 获取自动调整后的列宽
            width = self.table.horizontalHeader().sectionSize(col)
            
            # 如果是区域信息列，增加列宽
            if self.table.horizontalHeaderItem(col).text() == '区域信息':
                width = max(width, 250)  # 设置最小宽度为250像素
            
            # 设置为可手动调整模式并保持当前列宽
            self.table.horizontalHeader().setSectionResizeMode(col, QHeaderView.Interactive)
            self.table.setColumnWidth(col, width)
        
        # 设置表头可拉伸
        self.table.horizontalHeader().setStretchLastSection(False)
        
        # 设置筛选行
        self.setup_filters()
        
        # 恢复当前功能的排序状态
        current_type = self.analysis_type if self.analysis_type else 'original'
        sort_state = self.sort_states[current_type]
        self.sort_column = sort_state['column']
        self.sort_order = sort_state['order']
        self.table.horizontalHeader().setSortIndicator(sort_state['column'], sort_state['order'])
        
        # 只在显示原始话单数据时更新 current_display_data
        if self.analysis_type is None:
            self.current_display_data = display_df.copy()
        
        # 如果有数据则显示导出按钮
        self.export_btn.setVisible(not df.empty)
    
    def export_data(self):
        """导出当前显示的数据"""
        if self.current_display_data is None or self.current_display_data.empty:
            QMessageBox.warning(self, '警告', '没有可导出的数据！')
            return
            
        try:
            # 创建文件保存对话框
            file_dialog = QFileDialog(self)
            file_dialog.setAcceptMode(QFileDialog.AcceptSave)
            file_dialog.setNameFilter('Excel Files (*.xlsx);;CSV Files (*.csv)')
            
            # 根据分析类型设置默认文件名
            default_filename = '话单数据'
            if self.analysis_type == 'counter':
                default_filename = '对方号码统计'
            elif self.analysis_type == 'common':
                default_filename = '共同联系人分析'
            elif self.analysis_type == 'disappeared':
                default_filename = '消失号码分析'
            elif self.analysis_type == 'new':
                default_filename = '新出现号码分析'
            elif self.analysis_type == 'area':
                default_filename = '区域活动分析'
            elif self.analysis_type == 'active_period':
                default_filename = '活跃时段分析'
            
            # 设置默认文件名和路径
            file_dialog.selectFile(default_filename)
            
            if file_dialog.exec_() == QFileDialog.Accepted:
                file_path = file_dialog.selectedFiles()[0]
                selected_filter = file_dialog.selectedNameFilter()
                
                # 确保文件扩展名正确
                if selected_filter == 'Excel Files (*.xlsx)' and not file_path.endswith('.xlsx'):
                    file_path += '.xlsx'
                elif selected_filter == 'CSV Files (*.csv)' and not file_path.endswith('.csv'):
                    file_path += '.csv'
                
                # 导出数据
                if file_path.endswith('.xlsx'):
                    # 导出为Excel文件
                    self.current_display_data.to_excel(file_path, index=False)
                else:
                    # 导出为CSV文件，使用UTF-8编码并添加BOM以支持Excel打开
                    self.current_display_data.to_csv(
                        file_path, 
                        index=False, 
                        encoding='utf-8-sig'
                    )
                
                QMessageBox.information(self, '成功', '数据导出成功！')
                
                # 询问是否打开导出的文件
                reply = QMessageBox.question(
                    self, 
                    '确认', 
                    '是否立即打开导出的文件？',
                    QMessageBox.Yes | QMessageBox.No, 
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    # 使用系统默认程序打开文件
                    os.startfile(file_path)
                    
        except Exception as e:
            QMessageBox.warning(self, '错误', f'导出数据时出错：{str(e)}')

    def analyze_counter_numbers(self):
        if self.all_data.empty:
            return
            
        try:
            # 清除所有筛选状态并重新显示原始数据
            self.column_filter_states = {}
            # 显示所有行
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
                
            # 重置当前功能的筛选状态
            self.column_filter_states['counter'] = {}
            
            # 使用原始数据进行分析
            analysis_data = self.all_data.copy()
            
            # 创建基础统计信息
            stats = pd.DataFrame()
            
            # 计算通话次数
            call_counts = analysis_data.groupby(['己方号码', '对方号码']).size().reset_index(name='通话次数')
            stats = call_counts
            
            # 如果存在时长字段，计算总时长
            if '时长' in analysis_data.columns:
                # 直接计算总秒数，不做格式转换
                time_sums = analysis_data.groupby(['己方号码', '对方号码'])['时长'].sum().reset_index(name='总时长')
                # 将总时长转换为整数
                time_sums['总时长'] = time_sums['总时长'].apply(lambda x: str(int(float(x))) if pd.notnull(x) else '0')
                stats = pd.merge(stats, time_sums, on=['己方号码', '对方号码'])
            
            # 处理通话时间段
            if '呼叫日期' in analysis_data.columns and '呼叫时间' in analysis_data.columns:
                try:
                    # 合并日期和时间
                    time_data = analysis_data.copy()
                    
                    # 处理呼叫日期，移除可能存在的时间部分
                    time_data['呼叫日期'] = pd.to_datetime(time_data['呼叫日期']).dt.date
                    
                    # 处理呼叫时间，确保格式正确
                    def format_time(time_str):
                        # 如果时间字符串包含秒，直接返回
                        if len(str(time_str).split(':')) == 3:
                            return time_str
                        # 如果只有时和分，添加秒
                        elif len(str(time_str).split(':')) == 2:
                            return f"{time_str}:00"
                        # 其他情况返回原值
                        return time_str
                    
                    time_data['呼叫时间'] = time_data['呼叫时间'].apply(format_time)
                    
                    # 将日期和时间合并为完整的日期间字符串
                    time_data['完整时间'] = time_data.apply(
                        lambda x: pd.to_datetime(f"{x['呼叫日期']} {x['呼叫时间']}"),
                        axis=1
                    )
                    
                    # 计算每个码的起止时间
                    time_stats = time_data.groupby(['己方号码', '对方号码']).agg({
                        '完整时间': ['min', 'max']
                    }).reset_index()
                    
                    # 重命名列
                    time_stats.columns = ['己方号码', '对方号码', '开始时间', '结束时间']
                    
                    # 格式化时间字符
                    time_stats['通话时间段'] = (
                        time_stats['开始时间'].dt.strftime('%Y/%m/%d %H:%M:%S') + 
                        ' 至 ' + 
                        time_stats['结束时间'].dt.strftime('%Y/%m/%d %H:%M:%S')
                    )
                    
                    # 合并时间段信息
                    stats = pd.merge(
                        stats, 
                        time_stats[['己方号码', '对方号码', '通话时间段']], 
                        on=['己方号码', '对方号码']
                    )
                    
                except Exception as e:
                    QMessageBox.warning(self, '警告', f'处理呼叫日期时间时出错：{str(e)}')
                
            elif '截获时间' in analysis_data.columns:
                try:
                    # 处理截获时间
                    time_data = analysis_data.copy()
                    time_data['截获时间'] = pd.to_datetime(time_data['截获时间'])
                    
                    # 计算每个号码的起止时间
                    time_stats = time_data.groupby(['己方号码', '对方号码']).agg({
                        '截获时间': ['min', 'max']
                    }).reset_index()
                    
                    # 重命名列
                    time_stats.columns = ['己方号码', '对方号码', '开始时间', '结束时间']
                    
                    # 格式化时间字符串
                    time_stats['通话时间段'] = (
                        time_stats['开始时间'].dt.strftime('%Y/%m/%d %H:%M:%S') + 
                        ' 至 ' + 
                        time_stats['结束时间'].dt.strftime('%Y/%m/%d %H:%M:%S')
                    )
                    
                    # 合并时间段信息
                    stats = pd.merge(
                        stats, 
                        time_stats[['己方号码', '对方号码', '通话时间段']], 
                        on=['己方号码', '对方号码']
                    )
                    
                except Exception as e:
                    QMessageBox.warning(self, '警告', f'处理获时间时出：{str(e)}')
            
            # 按通话次数降序排序
            stats = stats.sort_values('通话次数', ascending=False)
            
            # 保存分析结果
            self.current_display_data = stats.copy()
            self.analysis_type = 'counter'
            
            # 重置排序状态
            self.sort_states['counter'] = {'column': -1, 'order': Qt.AscendingOrder}
            self.sort_column = -1
            self.sort_order = Qt.AscendingOrder
            self.table.horizontalHeader().setSortIndicator(-1, Qt.AscendingOrder)
            
            # 显示统计结果
            self.display_data(stats)
            self.return_btn.setVisible(True)
            self.return_to_original_btn.setVisible(True)
            
        except Exception as e:
            QMessageBox.warning(self, '错误', f'统计分析时出错：{str(e)}')
    
    def analyze_common_contacts(self):
        if self.all_data.empty:
            return
            
        try:
            # 清除所有筛选状态并重新显示原始数据
            self.column_filter_states = {}
            # 显示所有行
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
                
            # 重置当前功能的筛选状态
            self.column_filter_states['common'] = {}
            
            # 使用原始数据进行分析
            analysis_data = self.all_data.copy()
            
            # 创建基础统计信息
            base_stats = pd.DataFrame()
            
            # 计算每个对方号码的己方号码个数和总通话次数
            stats = analysis_data.groupby('对方号码').agg(
                关联己方号码数=('己方号码', lambda x: len(set(x))),
                总通话次数=('对方号码', 'size')
            ).reset_index()
            
            # 为每个对方号码计算关联己方号码的详细信息
            def get_related_numbers(group):
                # 计算每个己方号码的通话次数
                number_counts = group['己方号码'].value_counts()
                # 格式化为"号码(次数)"的形式
                formatted_counts = [f"{num}({count}次)" for num, count in number_counts.items()]
                return '、'.join(formatted_counts)
            
            # 添加关联己方号码详情列
            related_numbers = analysis_data.groupby('对方号码').apply(get_related_numbers)
            stats['联系己方号码详情'] = stats['对方号码'].map(related_numbers)
            
            base_stats = stats
            
            # 处理时间信息
            try:
                # 创建一个统一的时间列
                analysis_data['统一时间'] = pd.NaT
                
                # 处理呼叫日期和呼叫时间
                if '呼叫日期' in analysis_data.columns and '呼叫时间' in analysis_data.columns:
                    mask = analysis_data['呼叫日期'].notna() & analysis_data['呼叫时间'].notna()
                    analysis_data.loc[mask, '统一时间'] = pd.to_datetime(
                        analysis_data.loc[mask, '呼叫日期'].astype(str) + ' ' + 
                        analysis_data.loc[mask, '呼叫时间'].astype(str),
                        errors='coerce'
                    )
                
                # 处理截获时间
                if '截获时间' in analysis_data.columns:
                    mask = analysis_data['截获时间'].notna() & analysis_data['统一时间'].isna()
                    analysis_data.loc[mask, '统一时间'] = pd.to_datetime(
                        analysis_data.loc[mask, '截获时间'],
                        errors='coerce'
                    )
                
                # 按对方号码分组计算时间范围
                time_stats = analysis_data.groupby('对方号码').agg({
                    '统一时间': ['min', 'max']
                }).reset_index()
                
                # 重命名列
                time_stats.columns = ['对方号码', '开始时间', '结束时间']
                
                # 格式化时间字符串，处理空值
                def format_time_range(row):
                    if pd.isna(row['开始时间']) or pd.isna(row['结束时间']):
                        return '时间数据不完整'
                    return (f"{row['开始时间'].strftime('%Y/%m/%d %H:%M:%S')} 至 "
                           f"{row['结束时间'].strftime('%Y/%m/%d %H:%M:%S')}")
                
                time_stats['通话时间段'] = time_stats.apply(format_time_range, axis=1)
                
                # 合并时间段信息
                base_stats = pd.merge(
                    base_stats,
                    time_stats[['对方号码', '通话时间段']],
                    on='对方号码',
                    how='left'
                )
                
            except Exception as e:
                # 如果处理时间出错，添加一个空的时间段列
                base_stats['通话时间段'] = '时间数据处理出错'
                print(f"处理时间段时出错：{str(e)}")
            
            # 按关联己方号码数和总通话次数降序排序
            base_stats = base_stats.sort_values(['关联己方号码数', '总通话次数'], ascending=[False, False])
            
            # 保存分析结果
            self.current_display_data = base_stats.copy()
            self.analysis_type = 'common'
            
            # 重置排序状态
            self.sort_states['common'] = {'column': -1, 'order': Qt.AscendingOrder}
            self.sort_column = -1
            self.sort_order = Qt.AscendingOrder
            self.table.horizontalHeader().setSortIndicator(-1, Qt.AscendingOrder)
            
            # 显示结果
            self.display_data(base_stats)
            self.return_btn.setVisible(True)
            self.return_to_original_btn.setVisible(True)
            
        except Exception as e:
            QMessageBox.warning(self, '错误', f'共同联系人分析时出错：{str(e)}')
    
    def return_to_previous(self):
        """返回到上一步的分析结果"""
        if hasattr(self, '_previous_analysis_type') and self.analysis_type == 'details':
            # 如果是从详情视图返回，恢复到之前的分析结果
            self.analysis_type = self._previous_analysis_type
            delattr(self, '_previous_analysis_type')
            if self.analysis_type == 'counter':
                self.analyze_counter_numbers()
            elif self.analysis_type == 'common':
                self.analyze_common_contacts()
        elif not self.all_data.empty:
            # 原有的返回逻辑
            self.current_display_data = self.all_data.copy()
            self.analysis_type = None
            self.sort_column = -1
            self.sort_order = Qt.AscendingOrder
            self.table.horizontalHeader().setSortIndicator(-1, Qt.AscendingOrder)
            self.display_data(self.all_data)
            self.return_btn.setVisible(False)
            self.return_to_original_btn.setVisible(False)
            # 更新导出按钮状态
            self.export_btn.setVisible(not self.all_data.empty)

    def return_to_original(self):
        """直接返回到原始话单显示"""
        if not self.all_data.empty:
            self.current_display_data = self.all_data.copy()
            self.analysis_type = None
            self.sort_column = -1
            self.sort_order = Qt.AscendingOrder
            self.table.horizontalHeader().setSortIndicator(-1, Qt.AscendingOrder)
            self.display_data(self.all_data)
            self.return_btn.setVisible(False)
            self.return_to_original_btn.setVisible(False)
            # 更新导出按钮状态
            self.export_btn.setVisible(not self.all_data.empty)

    def on_header_clicked(self, logical_index):
        """处理表头点击事件，实现排序"""
        try:
            if self.current_display_data is None:
                return
                
            # 获取当前排序顺序
            order = self.table.horizontalHeader().sortIndicatorOrder()
            ascending = order == Qt.AscendingOrder
            
            # 根据当前分析类型保存排序状态
            current_type = self.analysis_type if self.analysis_type else 'original'
            self.sort_states[current_type] = {
                'column': logical_index,
                'order': order
            }
            
            # 获取列名
            column_name = self.table.horizontalHeaderItem(logical_index).text()
            
            # 创建一个新的DataFrame只包含可见行的数据
            visible_rows = []
            visible_data = []
            for row in range(self.table.rowCount()):
                if not self.table.isRowHidden(row):
                    visible_rows.append(row)
                    row_data = {}
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        col_name = self.table.horizontalHeaderItem(col).text()
                        row_data[col_name] = item.text() if item else ''
                    visible_data.append(row_data)
            
            # 创建只包含可见行的DataFrame
            df = pd.DataFrame(visible_data)
            
            if not df.empty:
                # 根据列类型进行排序处理
                if column_name in ['通话次数', '总通话次数', '关联己方号码数', '出现次数']:
                    # 确保这些列作为整数处理
                    df[column_name] = pd.to_numeric(df[column_name].replace('', '0'), errors='coerce').fillna(0).astype(int)
                    df = df.sort_values(by=column_name, ascending=ascending, na_position='last')
                    
                elif column_name == '总时长':
                    # 总时长可能包含小数，作为浮点数处理
                    df[column_name] = pd.to_numeric(df[column_name].replace('', '0'), errors='coerce').fillna(0)
                    df = df.sort_values(by=column_name, ascending=ascending, na_position='last')
                    
                elif column_name == '通话时间':
                    df['排序时间'] = pd.to_datetime(
                        df[column_name].str.split('至').str[0].str.strip(),
                        errors='coerce'
                    )
                    df = df.sort_values('排序时间', ascending=ascending)
                    df = df.drop('排序时间', axis=1)
                    
                elif column_name == '呼叫时间':
                    if '呼叫日期' in df.columns:
                        df['排序时间'] = pd.to_datetime(
                            df['呼叫日期'] + ' ' + df[column_name],
                            errors='coerce'
                        )
                        df = df.sort_values('排序时间', ascending=ascending)
                        df = df.drop('排序时间', axis=1)
                    else:
                        df['排序时间'] = pd.to_datetime(
                            '1900-01-01 ' + df[column_name],
                            errors='coerce'
                        )
                        df = df.sort_values('排序时间', ascending=ascending)
                        df = df.drop('排序时间', axis=1)
                        
                elif column_name == '呼叫日期':
                    df[column_name] = pd.to_datetime(df[column_name], errors='coerce')
                    df = df.sort_values(by=column_name, ascending=ascending, na_position='last')
                    
                elif '时间' in column_name and '时间段' not in column_name:
                    df[column_name] = pd.to_datetime(df[column_name], errors='coerce')
                    df = df.sort_values(by=column_name, ascending=ascending, na_position='last')
                    
                else:
                    # 其他列按字符串排序
                    df = df.sort_values(by=column_name, ascending=ascending, na_position='last')
                
                # 更新表格显示，只更新可见行
                self.table.setSortingEnabled(False)
                for new_row, row_data in enumerate(df.to_dict('records')):
                    old_row = visible_rows[new_row]
                    for col, value in row_data.items():
                        col_index = next(i for i in range(self.table.columnCount()) 
                                       if self.table.horizontalHeaderItem(i).text() == col)
                        item = QTableWidgetItem()
                        
                        # 根据列类型设置数据
                        if col in ['通话次数', '总通话次数', '关联己方号码数', '出现次数']:
                            # 设置为数值类型
                            try:
                                num_value = int(float(str(value).replace(',', '')))
                                item.setData(Qt.DisplayRole, num_value)
                            except (ValueError, TypeError):
                                item.setData(Qt.DisplayRole, 0)
                        elif col == '总时长':
                            try:
                                num_value = float(str(value).replace(',', ''))
                                item.setData(Qt.DisplayRole, num_value)
                            except (ValueError, TypeError):
                                item.setData(Qt.DisplayRole, 0.0)
                        else:
                            item.setText(str(value))
                        
                        # 设置对齐方式
                        if col in ['通话次数', '总时长', '关联己方号码数', '总通话次数', '出现次数']:
                            item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                        else:
                            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                        
                        self.table.setItem(old_row, col_index, item)
                
                self.table.setSortingEnabled(True)
                
        except Exception as e:
            QMessageBox.warning(self, '警告', f'排序时出错：{str(e)}')

    def display_sorted_data(self, df):
        """显示排序后的数据，但不改变原始数据"""
        try:
            self.table.setSortingEnabled(False)
            
            # 存当前的列宽
            column_widths = [self.table.columnWidth(col) for col in range(self.table.columnCount())]
            
            # 更新表格数据
            self.table.setRowCount(len(df))
            
            # 填充数据
            for i in range(len(df)):
                for j in range(len(df.columns)):
                    value = df.iloc[i, j]
                    column_name = df.columns[j]
                    
                    # 根据列类型处理数据
                    if column_name in ['通话次数', '总通话次数', '关联己方号码数']:
                        # 确保这些列作为整数显示
                        try:
                            numeric_value = int(float(value))
                            item = QTableWidgetItem()
                            # 设置数据为数值类型，确保正确排序
                            item.setData(Qt.DisplayRole, numeric_value)
                        except (ValueError, TypeError):
                            item = QTableWidgetItem('0')
                            item.setData(Qt.DisplayRole, 0)
                    
                    elif column_name == '时长':
                        # 总时长作为浮点数处理
                        try:
                            numeric_value = float(value)
                            item = QTableWidgetItem()
                            # 设置数据为数值类型，确保正确排序
                            item.setData(Qt.DisplayRole, numeric_value)
                        except (ValueError, TypeError):
                            item = QTableWidgetItem('0')
                            item.setData(Qt.DisplayRole, 0)
                    
                    else:
                        # 其他列按字符串处理
                        value_str = str(value)
                        if value_str == 'nan':
                            value_str = ''
                        item = QTableWidgetItem(value_str)
                    
                    # 设置数据对齐方式
                    if column_name in ['通话次数', '总时长', '关联己方号码数', '总通话次数']:
                        item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    else:
                        item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                        
                    self.table.setItem(i, j, item)
            
            # 恢复列宽
            for col in range(min(len(column_widths), self.table.columnCount())):
                self.table.setColumnWidth(col, column_widths[col])
            
            self.table.setSortingEnabled(True)
            
        except Exception as e:
            QMessageBox.warning(self, '警告', f'更新表格数据时出错：{str(e)}')

    def show_custom_filter(self):
        if not self.all_data.empty:
            dialog = CustomFilterDialog(self, list(self.all_data.columns))
            dialog.exec_()

    def setup_filters(self):
        """设置筛选行"""
        # 安全地清除现有的筛选输入框
        while self.filter_layout.count():
            item = self.filter_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        self.filter_inputs.clear()
        
        # 创建一个新的水平布局，设置边距为0
        self.filter_layout.setContentsMargins(0, 0, 0, 0)
        self.filter_layout.setSpacing(0)  # 设置间距为0
        
        # 获取表格视图的左边距
        table_margin = self.table.verticalHeader().width()
        
        # 添加一个空的widget来匹配表格的左边
        spacer = QWidget()
        spacer.setFixedWidth(table_margin)
        self.filter_layout.addWidget(spacer)
        
        # 为每列创建筛选按钮
        for col in range(self.table.columnCount()):
            filter_btn = QPushButton('▼', self)  # 使用普通文本字符替代emoji
            # 设置按钮宽度与列宽完全一致
            column_width = self.table.columnWidth(col)
            filter_btn.setFixedWidth(column_width)
            # 设置按钮样式，移除边框并添加悬停效果
            filter_btn.setStyleSheet("""
                QPushButton {
                    border: none;
                    padding: 0px;
                    text-align: center;
                    font-size: 8pt;  /* 调整字体大小 */
                    color: #666666;  /* 设置颜色为灰色 */
                }
                QPushButton:hover {
                    background-color: #f0f0f0;
                    color: #000000;  /* 悬停时变为黑色 */
                }
            """)
            # 使用functools.partial正确传递列索引
            filter_btn.clicked.connect(partial(self.show_filter_menu, col))
            self.filter_layout.addWidget(filter_btn)
            self.filter_inputs[col] = filter_btn
        
        # 添加一个弹性空间，确保按钮不会被拉伸
        self.filter_layout.addStretch()

    def show_filter_menu(self, column):
        """显示筛选菜单"""
        # 获取当前列的所有唯一值
        unique_values = set()
        visible_values = set()  # 存储当前可见的值
        
        for row in range(self.table.rowCount()):
            item = self.table.item(row, column)
            if item and item.text().strip():
                unique_values.add(item.text())
                # 如果行是可见的，将值添加到visible_values
                if not self.table.isRowHidden(row):
                    visible_values.add(item.text())
        
        # 创建单
        menu = QMenu(self)
        
        # 添加搜索框
        search_action = QWidgetAction(menu)
        search_widget = QWidget()
        search_layout = QVBoxLayout(search_widget)
        search_input = QLineEdit()
        search_input.setPlaceholderText('搜索...')
        search_layout.addWidget(search_input)
        search_action.setDefaultWidget(search_widget)
        menu.addAction(search_action)
        
        # 添加全选/取消全选选
        select_all_checkbox = QCheckBox('全选')
        select_all_action = QWidgetAction(menu)
        select_all_action.setDefaultWidget(select_all_checkbox)
        menu.addAction(select_all_action)
        
        menu.addSeparator()
        
        # 添加值列表
        value_dict = {}  # 存储复选框状态
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 排序唯一值
        sorted_values = sorted(unique_values)
        
        # 获取该列的已保存状态
        saved_states = self.column_filter_states.get(column, visible_values)
        
        # 创建复选框
        for value in sorted_values:
            checkbox = QCheckBox(str(value))
            # 根据保存的状态设置选中状态
            checkbox.setChecked(value in saved_states)
            value_dict[value] = checkbox
            scroll_layout.addWidget(checkbox)
            
            # 连接每个复选框的状态变化信号
            checkbox.stateChanged.connect(lambda state, cb=checkbox: update_select_all_state())
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(300)  # 限制最大高度
        
        scroll_action = QWidgetAction(menu)
        scroll_action.setDefaultWidget(scroll_area)
        menu.addAction(scroll_action)
        
        # 添加确定和取消按钮
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        apply_btn = QPushButton('确定')
        cancel_btn = QPushButton('取消')
        button_layout.addWidget(apply_btn)
        button_layout.addWidget(cancel_btn)
        
        button_action = QWidgetAction(menu)
        button_action.setDefaultWidget(button_widget)
        menu.addAction(button_action)
        
        # 更新全选框状态的函数
        def update_select_all_state():
            all_checked = all(cb.isChecked() for cb in value_dict.values() if cb.isVisible())
            any_checked = any(cb.isChecked() for cb in value_dict.values() if cb.isVisible())
            
            # 阻止信号循环
            select_all_checkbox.blockSignals(True)
            if all_checked:
                select_all_checkbox.setCheckState(Qt.Checked)
            elif any_checked:
                select_all_checkbox.setCheckState(Qt.PartiallyChecked)
            else:
                select_all_checkbox.setCheckState(Qt.Unchecked)
            select_all_checkbox.blockSignals(False)
        
        # 连接全选框信号
        def on_select_all_changed(state):
            # 只处理完全选中和完全取消选中的状态
            if state != Qt.PartiallyChecked:
                for checkbox in value_dict.values():
                    if checkbox.isVisible():  # 只更改可见的选框
                        checkbox.setChecked(state == Qt.Checked)
        
        def on_search_changed(text):
            for value, checkbox in value_dict.items():
                checkbox.setVisible(text.lower() in str(value).lower())
            # 更新搜索后的全选状态
            update_select_all_state()
        
        def apply_filter():
            # 获取选中的值
            selected_values = {value for value, checkbox in value_dict.items() 
                             if checkbox.isChecked()}
            
            # 保存筛选状态
            self.column_filter_states[column] = selected_values
            
            # 应用筛选
            for row in range(self.table.rowCount()):
                item = self.table.item(row, column)
                if item is None:
                    self.table.setRowHidden(row, True)
                    continue
                
                value = item.text()
                self.table.setRowHidden(row, value not in selected_values)
            
            menu.close()
        
        def cancel_filter():
            menu.close()
        
        # 设置初始全选框状态
        update_select_all_state()
        
        # 接信号
        select_all_checkbox.stateChanged.connect(on_select_all_changed)
        search_input.textChanged.connect(on_search_changed)
        apply_btn.clicked.connect(apply_filter)
        cancel_btn.clicked.connect(cancel_filter)
        
        # 显示菜单
        button_pos = self.filter_inputs[column].mapToGlobal(QPoint(0, 0))
        menu.exec_(button_pos)

    def show_custom_sort(self):
        """显示自定义排序对话框"""
        if not self.all_data.empty:
            try:
                # 清除当前的排序状态
                self.table.horizontalHeader().setSortIndicator(-1, Qt.AscendingOrder)
                
                # 恢复数据到未排序状态
                if self.analysis_type is None:
                    # 如果是原始话单，使用 all_data
                    unsorted_data = self.all_data.copy()
                else:
                    # 如果是分析结果，使用当前显示的数据
                    unsorted_data = self.current_display_data.copy()
                
                # 重置排序状态
                current_type = self.analysis_type if self.analysis_type else 'original'
                self.sort_states[current_type] = {
                    'column': -1,
                    'order': Qt.AscendingOrder
                }
                self.sort_column = -1
                self.sort_order = Qt.AscendingOrder
                
                # 重新显示数据
                self.display_data(unsorted_data)
                
                # 显示自定义排序对话框
                current_columns = list(unsorted_data.columns)
                dialog = CustomSortDialog(self, current_columns)
                dialog.exec_()
                
            except Exception as e:
                QMessageBox.warning(self, '错误', f'自定义排序时出错：{str(e)}')

    def load_data(self, file_path):
        # 加载数据时同时保存原始数据
        self.df = pd.read_excel(file_path)
        self.original_df = self.df.copy()  # 保存一份原始数据的副本
        
    def update_filtered_data(self, filtered_df):
        # 更新筛选后的数据,但保持原始数据不变
        self.df = filtered_df
        
    def calculate_contact_stats(self):
        # 使用original_df而不是df来计算统计信息
        if self.original_df is None:
            return pd.DataFrame()
            
        stats = self.original_df['对方号码'].value_counts().reset_index()
        stats.columns = ['对方号码', '通话次数']
        # ... 其余代码保持不变 ...

    def find_common_contacts(self):
        # 同样使用original_df来查找共同联系人
        if self.original_df is None:
            return pd.DataFrame()
            
        contact_groups = self.original_df.groupby('对方号码')
        # ... 其余代码保持不变 ...

    def show_call_details(self, row, column):
        """显示指定己方号码和对方号码的通话详情"""
        try:
            header_text = self.table.horizontalHeaderItem(column).text()
            # 检查是否是需要处理的情况
            if ((self.analysis_type == 'counter' and header_text == '通话次数') or
                (self.analysis_type == 'common' and header_text == '总通话次数')):
                
                # 获取己方号码和对方号码的列索引
                own_number_col = -1
                counter_number_col = -1
                
                # 安全地查找列索引
                for col in range(self.table.columnCount()):
                    header_item = self.table.horizontalHeaderItem(col)
                    if header_item:
                        if header_item.text() == '己方号码':
                            own_number_col = col
                        elif header_item.text() == '对方号码':
                            counter_number_col = col
                
                # 确保找到了对方号码列
                if counter_number_col == -1:
                    QMessageBox.warning(self, '错误', '无法找到对方号码列')
                    return
                
                # 获取对方号码
                counter_number = self.table.item(row, counter_number_col).text().strip()
                
                # 确保原始数据中的号码是字符串类型
                filtered_data = self.all_data.copy()
                filtered_data['对方号码'] = filtered_data['对方号码'].astype(str).str.strip()
                
                # 根据分析类型确定筛选条件
                if self.analysis_type == 'counter' and own_number_col != -1:
                    # 对方号码统计：筛选特定己方号码和对方号码的记录
                    own_number = self.table.item(row, own_number_col).text().strip()
                    filtered_data['己方号码'] = filtered_data['己方号码'].astype(str).str.strip()
                    filtered_records = filtered_data[
                        (filtered_data['己方号码'] == own_number) & 
                        (filtered_data['对方号码'] == counter_number)
                    ].copy()
                else:
                    # 共同联系人分析：筛选该对方号码的所有记录
                    filtered_records = filtered_data[
                        filtered_data['对方号码'] == counter_number
                    ].copy()
                
                # 打印调试信息
                print(f"找到记录数: {len(filtered_records)}")
                
                if not filtered_records.empty:
                    # 显示详细记录
                    self.display_data(filtered_records)
                    self.return_btn.setVisible(True)
                    self.return_to_original_btn.setVisible(True)
                    # 更新当前显示的数据
                    self.current_display_data = filtered_records
                    # 临时保存分析类，以便返回时恢复
                    self._previous_analysis_type = self.analysis_type
                    self.analysis_type = 'details'
                else:
                    # 显示更详细的误信息
                    error_msg = f'未找到相关通话记录\n对方号码: {counter_number}'
                    if self.analysis_type == 'counter':
                        error_msg += f'\n己方号码: {own_number}'
                    error_msg += '\n请检查号码格式是否一致'
                    QMessageBox.warning(self, '提示', error_msg)
                
        except Exception as e:
            QMessageBox.warning(self, '错误', f'显示通话详情时出错：{str(e)}')

    def analyze_disappeared_numbers(self):
        """分析消失号码"""
        if self.all_data.empty:
            return
        
        try:
            # 清除所有筛选状态并重新显示原始数据
            self.column_filter_states = {}
            # 显示所有行
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
                
            # 重置当前功能的筛选状态
            self.column_filter_states['disappeared'] = {}
            
            # 显示选择对话框
            dialog = TimePointDialog(self, self.all_data)
            if dialog.exec_() != QDialog.Accepted:
                return
            
            # 获取选择的参数
            selected_numbers = dialog.selected_numbers
            time_point = dialog.selected_time
            
            # 准备数据
            analysis_data = self.all_data.copy()
            
            # 只分析选中的己方号码
            analysis_data = analysis_data[analysis_data['己方号码'].astype(str).isin(selected_numbers)]
            
            # 根据可用的时间字段确定完整时间
            if '呼叫日期' in analysis_data.columns and '呼叫时间' in analysis_data.columns:
                analysis_data['完整时间'] = pd.to_datetime(
                    analysis_data['呼叫日期'].astype(str) + ' ' + 
                    analysis_data['呼叫时间'].astype(str)
                )  # 添加缺失的右括号
            else:
                analysis_data['完整时间'] = pd.to_datetime(analysis_data['截获时间'])
            
            # 分别获取时间点前后的对方号码
            before_numbers = set(analysis_data[analysis_data['完整时间'] < time_point]['对方号码'])
            after_numbers = set(analysis_data[analysis_data['完整时间'] >= time_point]['对方号码'])
            
            # 找出消失的号码
            disappeared = before_numbers - after_numbers
            
            if not disappeared:
                QMessageBox.information(self, '提示', '未发现消失号码')
                return
            
            # 创建结果DataFrame
            results = []
            for number in disappeared:
                # 获取该号码的所有通话记录
                number_data = analysis_data[
                    (analysis_data['对方号码'] == number) & 
                    (analysis_data['完整时间'] < time_point)
                ]
                
                # 计算统计信息
                stats = {
                    '对方号码': number,
                    '最后通话时间': number_data['完整时间'].max(),
                    '通话次数': len(number_data),
                    '关联己方号码': '、'.join(sorted(number_data['己方号码'].unique().astype(str)))
                }
                
                results.append(stats)
            
            # 转换为DataFrame并排序
            results_df = pd.DataFrame(results)
            results_df = results_df.sort_values('最后通话时间', ascending=False)
            
            # 格式化时间
            results_df['最后通话时间'] = results_df['最后通话时间'].dt.strftime('%Y/%m/%d %H:%M:%S')
            
            # 保存分析结果
            self.current_display_data = results_df.copy()
            self.analysis_type = 'disappeared'
            
            # 显示果
            self.display_data(results_df)
            self.return_btn.setVisible(True)
            self.return_to_original_btn.setVisible(True)
            
        except Exception as e:
            QMessageBox.warning(self, '错误', f'分析消失号码时出错：{str(e)}')

    def analyze_new_numbers(self):
        """分析新出现号码"""
        if self.all_data.empty:
            return
        
        try:
            # 清除所有筛选状态并重新显示原始数据
            self.column_filter_states = {}
            # 显示所有行
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
                
            # 重置当前功能的筛选状态
            self.column_filter_states['new'] = {}
            
            # 显示选择对话框
            dialog = TimePointDialog(self, self.all_data)
            if dialog.exec_() != QDialog.Accepted:
                return
            
            # 获取选择的参数
            selected_numbers = dialog.selected_numbers
            time_point = dialog.selected_time
            
            # 准备数据
            analysis_data = self.all_data.copy()
            
            # 只分析选中的己方号码
            analysis_data = analysis_data[analysis_data['己方号码'].astype(str).isin(selected_numbers)]
            
            # 根据可用的时间字段确定完整时间
            if '呼叫日期' in analysis_data.columns and '呼叫时间' in analysis_data.columns:
                analysis_data['完整时间'] = pd.to_datetime(
                    analysis_data['呼叫日期'].astype(str) + ' ' + 
                    analysis_data['呼叫时间'].astype(str)
                )
            else:
                analysis_data['完整时间'] = pd.to_datetime(analysis_data['截获时间'])
            
            # 分别获取时间点前后的对方号码
            before_numbers = set(analysis_data[analysis_data['完整时间'] < time_point]['对方号码'])
            after_numbers = set(analysis_data[analysis_data['完整时间'] >= time_point]['对方号码'])
            
            # 找出新出现的号码
            new_numbers = after_numbers - before_numbers
            
            if not new_numbers:
                QMessageBox.information(self, '提示', '未发现新出现号码')
                return
            
            # 创建结果DataFrame
            results = []
            for number in new_numbers:
                # 获取该号码的所有通话记录
                number_data = analysis_data[
                    (analysis_data['对方号码'] == number) & 
                    (analysis_data['完整时间'] >= time_point)
                ]
                
                # 计算统计信息
                stats = {
                    '对方号码': number,
                    '首次通话时间': number_data['完整时间'].min(),
                    '通话次数': len(number_data),
                    '关联己方号码': '、'.join(sorted(number_data['己方号码'].unique().astype(str)))
                }
                
                results.append(stats)
            
            # 转换为DataFrame并排序
            results_df = pd.DataFrame(results)
            results_df = results_df.sort_values('首次通话时间')
            
            # 格式化时间
            results_df['首次通话时间'] = results_df['首次通话时间'].dt.strftime('%Y/%m/%d %H:%M:%S')
            
            # 保存分析结果
            self.current_display_data = results_df.copy()
            self.analysis_type = 'new'
            
            # 显示结果
            self.display_data(results_df)
            self.return_btn.setVisible(True)
            self.return_to_original_btn.setVisible(True)
            
        except Exception as e:
            QMessageBox.warning(self, '错误', f'分析新出现号码时出错：{str(e)}')

    def analyze_area_activity(self):
        """分析区域活动情况"""
        if self.all_data.empty:
            return
            
        try:
            # 清除所有筛选状态并重新显示原始数据
            self.column_filter_states = {}
            # 显示所有行
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
                
            # 重置当前功能的筛选状态
            self.column_filter_states['area'] = {}
            
            # 检查必要字段是否存在
            location_fields = []
            if '己方位置区' in self.all_data.columns and '己方小区' in self.all_data.columns:
                location_fields.extend(['己方位置区', '己方小区'])
            if '己方基站名称' in self.all_data.columns:
                location_fields.append('己方基站名称')
                
            if not location_fields:
                QMessageBox.warning(self, '警告', '未找到位置相关字段（己方位置区和己方小区，或己方基站名称）')
                return
                
            # 显示选择对话框
            dialog = AreaActivityDialog(self, self.all_data, location_fields)
            if dialog.exec_() != QDialog.Accepted:
                return
                
            # 获取选择的参数
            selected_numbers = dialog.selected_numbers
            start_time = dialog.start_time
            end_time = dialog.end_time
            
            # 准备数据
            analysis_data = self.all_data.copy()
            
            # 只分析选中的己方号码
            if selected_numbers:
                analysis_data = analysis_data[analysis_data['己方号码'].astype(str).isin(selected_numbers)]
            
            # 确定时间字段并筛选时间范围
            if '呼叫日期' in analysis_data.columns and '呼叫时间' in analysis_data.columns:
                analysis_data['完整时间'] = pd.to_datetime(
                    analysis_data['呼叫日期'].astype(str) + ' ' + 
                    analysis_data['呼叫时间'].astype(str)
                )
            else:
                analysis_data['完整时间'] = pd.to_datetime(analysis_data['截获时间'])
            
            # 筛选时间范围
            analysis_data = analysis_data[
                (analysis_data['完整时间'] >= start_time) & 
                (analysis_data['完整时间'] <= end_time)
            ]
            
            if analysis_data.empty:
                QMessageBox.warning(self, '提示', '选定条件下无数据')
                return
            
            # 统计结果
            results = []
            
            if '己方位置区' in location_fields:
                # 统计位置区和小区组合的出现次数
                # 先处理位置区和小区的数据格式
                analysis_data['己方位置区'] = analysis_data['己方位置区'].apply(
                    lambda x: str(int(float(x))) if pd.notnull(x) and str(x).strip() != '' else x
                )
                analysis_data['己方小区'] = analysis_data['己方小区'].apply(
                    lambda x: str(int(float(x))) if pd.notnull(x) and str(x).strip() != '' else x   
                )
                
                location_stats = analysis_data.groupby(['己方位置区', '己方小区']).size().reset_index(name='出现次数')
                location_stats['区域信息'] = location_stats.apply(
                    lambda x: f"位置区：{x['己方位置区']}\n小区：{x['己方小区']}", 
                    axis=1
                )
                location_stats = location_stats.sort_values('出现次数', ascending=False)
                
                # 添加结果中
                for _, row in location_stats.iterrows():
                    results.append({
                        '区域信息': row['区域信息'],
                        '出现次数': row['出现次数']
                    })
            
            if '己方基站名称' in location_fields:
                # 统计基站名称的出现次数
                station_stats = analysis_data['己方基站名称'].value_counts().reset_index()
                station_stats.columns = ['基站名称', '出现次数']
                
                # 添加到结果中
                for _, row in station_stats.iterrows():
                    results.append({
                        '区域信息': f"基站名称：{row['基站名称']}",
                        '出现次数': row['出现次数']
                    })
            
            # 转换为DataFrame并排序
            results_df = pd.DataFrame(results)
            results_df = results_df.sort_values('出现次数', ascending=False)
            
            # 保存分析结果
            self.current_display_data = results_df.copy()
            self.analysis_type = 'area'
            
            # 显示结果
            self.display_data(results_df)
            self.return_btn.setVisible(True)
            self.return_to_original_btn.setVisible(True)
            
        except Exception as e:
            QMessageBox.warning(self, '错误', f'分析区域活动时出错：{str(e)}')

    def analyze_active_period(self):
        """分析活跃时段"""
        if self.all_data.empty:
            return
            
        try:
            # 清除所有筛选状态并重新显示原始数据
            self.column_filter_states = {}
            # 显示所有行
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
                
            # 重置当前功能的筛选状态
            self.column_filter_states['active_period'] = {}
            
            # 检查必要字段是否存在
            time_field = None
            if '呼叫时间' in self.all_data.columns:
                time_field = '呼叫时间'
            elif '截获时间' in self.all_data.columns:
                time_field = '截获时间'
            
            if not time_field:
                QMessageBox.warning(self, '警告', '未找到时间相关字段（呼叫时间或截获时间）')
                return
                
            # 显示选择对话框
            dialog = AreaActivityDialog(self, self.all_data, [])  # 复用区域活动对话框
            if dialog.exec_() != QDialog.Accepted:
                return
                
            # 获取选择的参数
            selected_numbers = dialog.selected_numbers
            start_time = dialog.start_time
            end_time = dialog.end_time
            
            # 准备数据
            analysis_data = self.all_data.copy()
            
            # 只分析选中的己方号码
            if selected_numbers:
                analysis_data = analysis_data[analysis_data['己方号码'].astype(str).isin(selected_numbers)]
            
            # 确定时间字段并筛选时间范围
            if '呼叫日期' in analysis_data.columns and '呼叫时间' in analysis_data.columns:
                analysis_data['完整时间'] = pd.to_datetime(
                    analysis_data['呼叫日期'].astype(str) + ' ' + 
                    analysis_data['呼叫时间'].astype(str)
                )  # 添加缺失的右括号
            else:
                analysis_data['完整时间'] = pd.to_datetime(analysis_data['截获时间'])
            
            # 筛选时间范围
            analysis_data = analysis_data[
                (analysis_data['完整时间'] >= start_time) & 
                (analysis_data['完整时间'] <= end_time)
            ]
            
            if analysis_data.empty:
                QMessageBox.warning(self, '提示', '选定条件下无数据')
                return
            
            # 提取小时信息并统计
            analysis_data['时段'] = analysis_data['完整时间'].dt.hour
            hour_stats = analysis_data['时段'].value_counts().reset_index()
            hour_stats.columns = ['时段', '出现次数']
            
            # 格式化时段显示
            hour_stats['时段'] = hour_stats['时段'].apply(lambda x: f"{x:02d}时")
            
            # 按出现次数降序排列
            hour_stats = hour_stats.sort_values('出现次数', ascending=False)
            
            # 保存分析结果
            self.current_display_data = hour_stats.copy()
            self.analysis_type = 'active_period'
            
            # 显示结果
            self.display_data(hour_stats)
            self.return_btn.setVisible(True)
            self.return_to_original_btn.setVisible(True)
            
        except Exception as e:
            QMessageBox.warning(self, '错误', f'分析活跃时段时出错：{str(e)}')

class FieldMatchDialog(QDialog):
    def __init__(self, parent, df, all_fields):
        super().__init__(parent)
        self.parent = parent
        self.df = df
        self.all_fields = all_fields
        self.field_mappings = {}
        
        self.setWindowTitle('字段匹配')
        self.setGeometry(200, 200, 600, 400)
        
        layout = QVBoxLayout()
        
        # 处理重复的列名
        column_counts = {}
        processed_columns = []
        for col in df.columns:
            if col in column_counts:
                column_counts[col] += 1
                processed_columns.append(f"{col}_{column_counts[col]}")
            else:
                column_counts[col] = 1
                processed_columns.append(col)
        
        # 使用处理后的列名创建新的DataFrame
        self.df.columns = processed_columns
        
        # 创建字段匹配的下拉框
        for field in processed_columns:
            row_layout = QHBoxLayout()
            # 显示原始字段名（去除可能添加的后缀）
            original_field = field.split('_')[0] if '_' in field else field
            label = QLabel(f"文件字段: {original_field}")
            combo = QComboBox()
            combo.addItem('--请选择--')
            combo.addItems(all_fields)
            
            # 如果字段名完全匹配（不考虑后缀），自动选择
            if original_field in all_fields:
                combo.setCurrentText(original_field)
                self.field_mappings[field] = original_field
            
            combo.currentTextChanged.connect(lambda text, field=field: self.on_combo_changed(field, text))
            
            row_layout.addWidget(label)
            row_layout.addWidget(combo)
            layout.addLayout(row_layout)
        
        # 添加确认按钮
        confirm_btn = QPushButton('确认', self)
        confirm_btn.clicked.connect(self.confirm_mapping)
        layout.addWidget(confirm_btn)
        
        self.setLayout(layout)
    
    def on_combo_changed(self, original_field, mapped_field):
        if mapped_field != '--请选--':
            self.field_mappings[original_field] = mapped_field
        else:
            self.field_mappings.pop(original_field, None)
    
    def confirm_mapping(self):
        # 检查必需字段是否已匹配
        required_fields = {'己方号码', '对方号码'}
        time_fields = {'呼叫日期', '截获时间'}
        
        mapped_fields = set(self.field_mappings.values())
        
        if not required_fields.issubset(mapped_fields):
            QMessageBox.warning(self, '警告', '必须匹配"己方号码"和"对方号码"字段！')
            return
            
        if not any(field in mapped_fields for field in time_fields):
            QMessageBox.warning(self, '警告', '必须匹配"呼叫日期"或"截获时间"字段！')
            return
        
        try:
            # 创建一个数据副本
            df_copy = self.df.copy()
            
            # 特殊处理己方机身码字段 - 直接使用原文本格式
            if '己方机身码' in self.field_mappings.values():
                # 找到对应的原始列名
                original_imei_field = [k for k, v in self.field_mappings.items() if v == '己方机身码'][0]
                # 保持原始文本格式
                df_copy[original_imei_field] = self.df[original_imei_field].copy()
                # 将 nan 替换为空字串
                df_copy[original_imei_field] = df_copy[original_imei_field].replace({pd.NA: '', pd.NaT: '', 'nan': '', 'NaN': ''})
            
            # 特殊处理己方卡号字段
            if '己方卡号' in self.field_mappings.values():
                # 找到对应的原始列名
                original_card_field = [k for k, v in self.field_mappings.items() if v == '己方卡号'][0]
                # 将该列转换为字符串并去除小数点等
                df_copy[original_card_field] = df_copy[original_card_field].astype(str).apply(
                    lambda x: ''.join(filter(str.isdigit, x)) if pd.notnull(x) and x != 'nan' else ''
                )
            
            # 重命名列并选择需要的列
            renamed_df = df_copy.rename(columns=self.field_mappings)
            selected_columns = list(self.field_mappings.values())
            renamed_df = renamed_df[selected_columns]
            
            # 更新父窗的数据
            if self.parent.all_data.empty:
                self.parent.all_data = renamed_df
            else:
                # 合并新数据和现有数据
                self.parent.all_data = pd.concat([self.parent.all_data, renamed_df], ignore_index=True)
            
            # 显示合并后的数据
            self.parent.display_data(self.parent.all_data)
            self.accept()
        
        except Exception as e:
            QMessageBox.warning(self, '错误', f'处理字段匹配时出错：{str(e)}')

class CustomFilterDialog(QDialog):
    def __init__(self, parent, columns):
        super().__init__(parent)
        self.parent = parent
        self.columns = columns
        self.filter_conditions = {}
        self.sort_conditions = []
        
        self.setWindowTitle('自定义排序和筛')
        self.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout()
        
        # 创建筛选部分
        filter_group = QWidget()
        filter_layout = QGridLayout()
        filter_layout.addWidget(QLabel('筛选条件：'), 0, 0)
        
        # 添加筛选字段
        for i, column in enumerate(columns):
            checkbox = QCheckBox(column)
            filter_layout.addWidget(checkbox, i//3 + 1, i%3)
            
            # 添加筛选输入框
            filter_input = QLineEdit()
            filter_input.setPlaceholderText(f'输入{column}的筛选条件')
            filter_layout.addWidget(filter_input, i//3 + 1, i%3 + 1)
            
            # 保存checkbox和input的引用
            self.filter_conditions[column] = {
                'checkbox': checkbox,
                'input': filter_input
            }
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # 创建排序部分
        sort_group = QWidget()
        sort_layout = QVBoxLayout()
        sort_layout.addWidget(QLabel('排序条件：'))
        
        # 添加三级排序选择
        for i in range(3):
            sort_row = QHBoxLayout()
            
            # 排序字段选择
            field_combo = QComboBox()
            field_combo.addItem('--请选择--')
            field_combo.addItems(columns)
            sort_row.addWidget(field_combo)
            
            # 排序方式选择
            order_combo = QComboBox()
            order_combo.addItems(['升序', '降序'])
            sort_row.addWidget(order_combo)
            
            sort_layout.addLayout(sort_row)
            
            # 保存combo的引用
            self.sort_conditions.append({
                'field': field_combo,
                'order': order_combo
            })
        
        sort_group.setLayout(sort_layout)
        layout.addWidget(sort_group)
        
        # 添加确认和取消按钮
        button_layout = QHBoxLayout()
        confirm_btn = QPushButton('确认')
        confirm_btn.clicked.connect(self.apply_filter_and_sort)
        cancel_btn = QPushButton('取消')
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(confirm_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def apply_filter_and_sort(self):
        # 获取当前数据
        df = self.parent.all_data.copy()
        
        # 应筛选条件
        for column, condition in self.filter_conditions.items():
            if condition['checkbox'].isChecked() and condition['input'].text():
                filter_text = condition['input'].text().strip()
                try:
                    # 尝试数值比较
                    if any(op in filter_text for op in ['>', '<', '=']):
                        for op in ['>=', '<=', '>', '<', '=']:
                            if op in filter_text:
                                value = float(filter_text.replace(op, ''))
                                if op == '>=':
                                    df = df[df[column].astype(float) >= value]
                                elif op == '<=':
                                    df = df[df[column].astype(float) <= value]
                                elif op == '>':
                                    df = df[df[column].astype(float) > value]
                                elif op == '<':
                                    df = df[df[column].astype(float) < value]
                                elif op == '=':
                                    df = df[df[column].astype(float) == value]
                                break
                    else:
                        # 文本包含
                        df = df[df[column].astype(str).str.contains(filter_text, case=False, na=False)]
                except Exception as e:
                    QMessageBox.warning(self, '警告', f'应用筛选条件时出错：{str(e)}')
        
        # 应用排序条件
        sort_fields = []
        sort_ascending = []
        
        for condition in self.sort_conditions:
            field = condition['field'].currentText()
            if field != '--请选择--':
                sort_fields.append(field)
                sort_ascending.append(condition['order'].currentText() == '升序')
        
        if sort_fields:
            df = df.sort_values(by=sort_fields, ascending=sort_ascending)
        
        # 显示结果
        self.parent.display_data(df)
        self.accept()

class CustomSortDialog(QDialog):
    def __init__(self, parent, columns):
        super().__init__(parent)
        self.parent = parent
        self.columns = columns
        self.sort_conditions = []
        
        self.setWindowTitle('自定义排序')
        self.setGeometry(200, 200, 600, 400)
        
        layout = QVBoxLayout()
        
        # 创建排序说明标题
        description = QLabel('请选择排序字段（从上到下优先级依次降低）：')
        layout.addWidget(description)
        
        # 创建5个排序条件行
        priority_names = ['第一优先级', '第二优先级', '第三优先级', '第四优先级', '第五优先级']
        
        # 添加五级排序选择
        for i, priority in enumerate(priority_names):
            # 创建组框来容纳每个优先级的控件
            group_box = QGroupBox(priority)
            sort_row = QHBoxLayout()
            
            # 排序字段选择
            field_label = QLabel('排序字段：')
            field_combo = QComboBox()
            field_combo.addItem('--请选择--')
            field_combo.addItems(columns)
            
            # 排序方式选择
            order_label = QLabel('排序方式：')
            order_combo = QComboBox()
            order_combo.addItems(['升序', '降序'])
            
            # 添加到行布局
            sort_row.addWidget(field_label)
            sort_row.addWidget(field_combo)
            sort_row.addWidget(order_label)
            sort_row.addWidget(order_combo)
            
            # 设置组框的布局
            group_box.setLayout(sort_row)
            layout.addWidget(group_box)
            
            # 保存combo的引用
            self.sort_conditions.append({
                'field': field_combo,
                'order': order_combo
            })
        
        # 添加确认和取消按钮
        button_layout = QHBoxLayout()
        confirm_btn = QPushButton('确认')
        confirm_btn.clicked.connect(self.apply_sort)
        cancel_btn = QPushButton('取消')
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(confirm_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def apply_sort(self):
        try:
            # 获取当前数据
            if self.parent.analysis_type is None:
                df = self.parent.all_data.copy()
            else:
                df = self.parent.current_display_data.copy()
            
            # 应用排序条件
            sort_fields = []
            sort_ascending = []
            
            # 收集有效的排序条件
            for condition in self.sort_conditions:
                field = condition['field'].currentText()
                if field != '--请选择--':
                    sort_fields.append(field)
                    sort_ascending.append(condition['order'].currentText() == '升序')
            
            if sort_fields:
                # 对数值类型的列进行特殊处理
                numeric_columns = ['通话次数', '总时长', '关联己方号码数', '总通话次数']
                for field in sort_fields:
                    if field in numeric_columns and field in df.columns:
                        df[field] = pd.to_numeric(df[field], errors='coerce')
                
                # 应用排序
                df = df.sort_values(by=sort_fields, ascending=sort_ascending)
                
                # 显示结果
                self.parent.display_data(df)
                
                # 更新当前显示的数据
                if self.parent.analysis_type is not None:
                    self.parent.current_display_data = df.copy()
                
                self.accept()
            else:
                QMessageBox.warning(self, '警告', '至少选择一个排序字段！')
                
        except Exception as e:
            QMessageBox.warning(self, '错误', f'应用排序时出错：{str(e)}')

class TimePointDialog(QDialog):
    def __init__(self, parent, all_data):
        super().__init__(parent)
        self.parent = parent
        self.all_data = all_data
        self.selected_time = None
        self.selected_numbers = set()
        
        self.setWindowTitle('选择分参数')
        self.setGeometry(200, 200, 600, 400)
        
        layout = QVBoxLayout()
        
        # 添加己方号码选择区域
        numbers_group = QGroupBox('选择己方号码')
        numbers_layout = QVBoxLayout()
        
        # 添加搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('搜索己方号码...')
        self.search_input.textChanged.connect(self.filter_numbers)
        numbers_layout.addWidget(self.search_input)
        
        # 创建号码列表
        self.numbers_list = QWidget()
        self.numbers_layout = QVBoxLayout(self.numbers_list)
        
        # 创滚动区域
        scroll = QScrollArea()
        scroll.setWidget(self.numbers_list)
        scroll.setWidgetResizable(True)
        numbers_layout.addWidget(scroll)
        
        # 添加全选按钮
        select_all_btn = QPushButton('全选/取消全选')
        select_all_btn.clicked.connect(self.toggle_select_all)
        numbers_layout.addWidget(select_all_btn)
        
        numbers_group.setLayout(numbers_layout)
        layout.addWidget(numbers_group)
        
        # 添加时间选择区域
        time_group = QGroupBox('选择时间节点')
        time_layout = QVBoxLayout()
        
        if '呼叫日期' in all_data.columns and '呼叫时间' in all_data.columns:
            # 使用日期时间选择器
            self.date_picker = QDateTimeEdit()
            self.date_picker.setDisplayFormat("yyyy/MM/dd HH:mm:ss")
            self.date_picker.setCalendarPopup(True)
            
            # 设置时间范围
            time_data = all_data.copy()
            time_data['完整时间'] = pd.to_datetime(
                time_data['呼叫日期'].astype(str) + ' ' + 
                time_data['呼叫时间'].astype(str)
            )  # 添加缺失的右括号
            min_time = time_data['完整时间'].min()
            max_time = time_data['完整时间'].max()
            
            self.date_picker.setDateTime(min_time)
            self.date_picker.setMinimumDateTime(min_time)
            self.date_picker.setMaximumDateTime(max_time)
            
            time_layout.addWidget(self.date_picker)
        elif '截获时间' in all_data.columns:
            # 使用截获时间选择器
            self.date_picker = QDateTimeEdit()
            self.date_picker.setDisplayFormat("yyyy/MM/dd HH:mm:ss")
            self.date_picker.setCalendarPopup(True)
            
            # 设置时间范围
            time_data = pd.to_datetime(all_data['截获时间'])
            min_time = time_data.min()
            max_time = time_data.max()
            
            self.date_picker.setDateTime(min_time)
            self.date_picker.setMinimumDateTime(min_time)
            self.date_picker.setMaximumDateTime(max_time)
            
            time_layout.addWidget(self.date_picker)
        
        time_group.setLayout(time_layout)
        layout.addWidget(time_group)
        
        # 添加确认和取按钮
        buttons_layout = QHBoxLayout()
        confirm_btn = QPushButton('确认')
        confirm_btn.clicked.connect(self.confirm_selection)
        cancel_btn = QPushButton('取消')
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # 初始化号码列表
        self.init_numbers_list()
    
    def init_numbers_list(self):
        # 获取唯一的己方号码
        unique_numbers = sorted(self.all_data['己方号码'].unique())
        
        # 创建复选框
        for number in unique_numbers:
            checkbox = QCheckBox(str(number))
            self.numbers_layout.addWidget(checkbox)
    
    def filter_numbers(self, text):
        # 过滤号码表
        for i in range(self.numbers_layout.count()):
            widget = self.numbers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox):
                widget.setVisible(text.lower() in widget.text().lower())
    
    def toggle_select_all(self):
        # 获取当前是否有全选状态
        all_checked = True
        for i in range(self.numbers_layout.count()):
            widget = self.numbers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isVisible():
                if not widget.isChecked():
                    all_checked = False
                    break
        
        # 切换选中状态
        for i in range(self.numbers_layout.count()):
            widget = self.numbers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isVisible():
                widget.setChecked(not all_checked)
    
    def confirm_selection(self):
        # 获取选中的号码
        self.selected_numbers.clear()
        for i in range(self.numbers_layout.count()):
            widget = self.numbers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isChecked():
                self.selected_numbers.add(widget.text())
        
        # 获取选中的时间
        self.selected_time = self.date_picker.dateTime().toPyDateTime()
        
        # 直接接受选择，不需要验证时间范围
        self.accept()

class AreaActivityDialog(QDialog):
    def __init__(self, parent, all_data, location_fields):
        super().__init__(parent)
        self.parent = parent
        self.all_data = all_data
        self.location_fields = location_fields
        self.selected_numbers = set()
        self.start_time = None
        self.end_time = None
        
        self.setWindowTitle('区域活动分析参数')
        self.setGeometry(200, 200, 600, 500)
        
        layout = QVBoxLayout()
        
        # 添加己方号码选择区域
        numbers_group = QGroupBox('选择己方号码（可选）')
        numbers_layout = QVBoxLayout()
        
        # 添加搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('搜索己方号码...')
        self.search_input.textChanged.connect(self.filter_numbers)
        numbers_layout.addWidget(self.search_input)
        
        # 创建号码列表
        self.numbers_list = QWidget()
        self.numbers_layout = QVBoxLayout(self.numbers_list)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidget(self.numbers_list)
        scroll.setWidgetResizable(True)
        numbers_layout.addWidget(scroll)
        
        # 添加全选按钮
        select_all_btn = QPushButton('全选/取消全选')
        select_all_btn.clicked.connect(self.toggle_select_all)
        numbers_layout.addWidget(select_all_btn)
        
        numbers_group.setLayout(numbers_layout)
        layout.addWidget(numbers_group)
        
        # 添加时间围选择区域
        time_group = QGroupBox('选择时间范围')
        time_layout = QGridLayout()
        
        # 开始时间选择器
        time_layout.addWidget(QLabel('开始时间：'), 0, 0)
        self.start_picker = QDateTimeEdit()
        self.start_picker.setDisplayFormat("yyyy/MM/dd HH:mm:ss")
        self.start_picker.setCalendarPopup(True)
        time_layout.addWidget(self.start_picker, 0, 1)
        
        # 结束时间选择器
        time_layout.addWidget(QLabel('结束时间：'), 1, 0)
        self.end_picker = QDateTimeEdit()
        self.end_picker.setDisplayFormat("yyyy/MM/dd HH:mm:ss")
        self.end_picker.setCalendarPopup(True)
        time_layout.addWidget(self.end_picker, 1, 1)
        
        # 设置时间范围
        if '呼叫日期' in all_data.columns and '呼叫时间' in all_data.columns:
            time_data = all_data.copy()
            time_data['完整时间'] = pd.to_datetime(
                time_data['呼叫日期'].astype(str) + ' ' + 
                time_data['呼叫时间'].astype(str)
            )  # 添加缺失的右括号
            min_time = time_data['完整时间'].min()
            max_time = time_data['完整时间'].max()
        else:
            time_data = pd.to_datetime(all_data['截获时'])
            min_time = time_data.min()
            max_time = time_data.max()
        
        self.start_picker.setDateTime(min_time)
        self.start_picker.setMinimumDateTime(min_time)
        self.start_picker.setMaximumDateTime(max_time)
        
        self.end_picker.setDateTime(max_time)
        self.end_picker.setMinimumDateTime(min_time)
        self.end_picker.setMaximumDateTime(max_time)
        
        time_group.setLayout(time_layout)
        layout.addWidget(time_group)
        
        # 添加确认和取消按钮
        buttons_layout = QHBoxLayout()
        confirm_btn = QPushButton('确认')
        confirm_btn.clicked.connect(self.confirm_selection)
        cancel_btn = QPushButton('取消')
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # 初始化号码列表
        self.init_numbers_list()
    
    def init_numbers_list(self):
        # 获取唯一的己方号码
        unique_numbers = sorted(self.all_data['己方号码'].unique())
        
        # 创建复框
        for number in unique_numbers:
            checkbox = QCheckBox(str(number))
            self.numbers_layout.addWidget(checkbox)
    
    def filter_numbers(self, text):
        # 过滤号码列表
        for i in range(self.numbers_layout.count()):
            widget = self.numbers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox):
                widget.setVisible(text.lower() in widget.text().lower())
    
    def toggle_select_all(self):
        # 获取当前是否有全选状态
        all_checked = True
        for i in range(self.numbers_layout.count()):
            widget = self.numbers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isVisible():
                if not widget.isChecked():
                    all_checked = False
                    break
        
        # 切换选中状态
        for i in range(self.numbers_layout.count()):
            widget = self.numbers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isVisible():
                widget.setChecked(not all_checked)
    
    def confirm_selection(self):
        # 获取选中的号码
        self.selected_numbers.clear()
        for i in range(self.numbers_layout.count()):
            widget = self.numbers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isChecked():
                self.selected_numbers.add(widget.text())
        
        # 获取时间范围
        self.start_time = self.start_picker.dateTime().toPyDateTime()
        self.end_time = self.end_picker.dateTime().toPyDateTime()
        
        # 验证时间范围
        if self.start_time > self.end_time:
            QMessageBox.warning(self, '警告', '开始时间不能晚于结束时间！')
            return
        
        self.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = CallRecordAnalyzer()
    window.show()
    sys.exit(app.exec_())
