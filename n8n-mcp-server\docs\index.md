# n8n MCP Server Documentation

Welcome to the n8n MCP Server documentation. This documentation provides comprehensive information about setting up, configuring, and using the n8n MCP Server.

## Table of Contents

- [Setup and Configuration](./setup/index.md)
  - [Installation](./setup/installation.md)
  - [Configuration](./setup/configuration.md)
  - [Troubleshooting](./setup/troubleshooting.md)

- [API Reference](./api/index.md)
  - [Tools](./api/tools.md)
    - [Workflow Tools](./api/workflow-tools.md)
    - [Execution Tools](./api/execution-tools.md)
  - [Resources](./api/resources.md)
    - [Static Resources](./api/static-resources.md)
    - [Dynamic Resources](./api/dynamic-resources.md)

- [Usage Examples](./examples/index.md)
  - [Basic Examples](./examples/basic-examples.md)
  - [Advanced Scenarios](./examples/advanced-scenarios.md)
  - [Integration Examples](./examples/integration-examples.md)

- [Development](./development/index.md)
  - [Architecture](./development/architecture.md)
  - [Extending the Server](./development/extending.md)
  - [Testing](./development/testing.md)

## Quick Links

- [GitHub Repository](https://github.com/yourusername/n8n-mcp-server)
- [n8n Documentation](https://docs.n8n.io/)
- [Model Context Protocol Documentation](https://modelcontextprotocol.github.io/)
