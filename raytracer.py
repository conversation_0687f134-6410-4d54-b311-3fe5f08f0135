from PIL import Image
import math

class Vector3:
    def __init__(self, x, y, z):
        self.x = x
        self.y = y
        self.z = z

    def __add__(self, other):
        return Vector3(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other):
        return Vector3(self.x - other.x, self.y - other.y, self.z - other.z)

    def __mul__(self, scalar):
        return Vector3(self.x * scalar, self.y * scalar, self.z * scalar)

    def dot(self, other):
        return self.x * other.x + self.y * other.y + self.z * other.z

    def norm(self):
        magnitude = math.sqrt(self.x**2 + self.y**2 + self.z**2)
        return self * (1.0 / magnitude) if magnitude != 0 else self

    def length(self):
        return math.sqrt(self.x**2 + self.y**2 + self.z**2)

    def __repr__(self):
        return f"Vector3({self.x}, {self.y}, {self.z})"

class Ray:
    def __init__(self, origin, direction):
        self.origin = origin
        self.direction = direction.norm()

class Sphere:
    def __init__(self, center, radius, material):
        self.center = center
        self.radius = radius
        self.material = material

    def intersect(self, ray):
        oc = ray.origin - self.center
        a = ray.direction.dot(ray.direction)
        b = 2.0 * oc.dot(ray.direction)
        c = oc.dot(oc) - self.radius**2
        discriminant = b**2 - 4 * a * c

        if discriminant < 0:
            return None
        sqrt_d = math.sqrt(discriminant)
        t1 = (-b - sqrt_d) / (2 * a)
        t2 = (-b + sqrt_d) / (2 * a)

        t = min(t1, t2)
        if t < 0:
            t = max(t1, t2)
        return t if t >= 0 else None

class Material:
    def __init__(self, color, diffuse=1.0, specular=1.0, shininess=50):
        self.color = color
        self.diffuse = diffuse
        self.specular = specular
        self.shininess = shininess

class Light:
    def __init__(self, position, color):
        self.position = position
        self.color = color

def reflect(v, normal):
    return v - normal * (2 * v.dot(normal))

def trace_ray(ray, scene_objects, lights, ambient_color):
    closest_t = None
    closest_obj = None

    for obj in scene_objects:
        t = obj.intersect(ray)
        if t is not None and (closest_t is None or t < closest_t):
            closest_t = t
            closest_obj = obj

    if closest_obj is None:
        return Vector3(0, 0, 0)

    hit_point = ray.origin + ray.direction * closest_t
    normal = (hit_point - closest_obj.center).norm()
    material = closest_obj.material

    color = material.color * ambient_color

    for light in lights:
        vec_to_light = light.position - hit_point
        distance_to_light = vec_to_light.length()
        light_dir = vec_to_light.norm()

        shadow_ray_origin = hit_point + normal * 1e-5
        shadow_ray = Ray(shadow_ray_origin, light_dir)
        in_shadow = False

        for obj in scene_objects:
            t = obj.intersect(shadow_ray)
            if t is not None and t > 1e-5 and t < distance_to_light:
                in_shadow = True
                break

        if not in_shadow:
            # Diffuse
            diffuse = max(0.0, normal.dot(light_dir)) * material.diffuse
            diffuse_color = light.color * diffuse

            # Specular
            view_dir = (ray.origin - hit_point).norm()
            reflect_dir = reflect(-light_dir, normal)
            specular = max(0.0, view_dir.dot(reflect_dir)) ** material.shininess * material.specular
            specular_color = light.color * specular

            color += material.color * diffuse_color + specular_color

    color.x = min(max(color.x, 0), 1)
    color.y = min(max(color.y, 0), 1)
    color.z = min(max(color.z, 0), 1)
    return color

# Scene setup
width = 800
height = 600
aspect_ratio = width / height
ambient = Vector3(0.1, 0.1, 0.1)

# Materials
red = Material(Vector3(1, 0.2, 0.2))
green = Material(Vector3(0.2, 1, 0.2))
blue = Material(Vector3(0.2, 0.2, 1))
yellow = Material(Vector3(1, 1, 0.2))
floor_material = Material(Vector3(0.8, 0.8, 0.8), diffuse=0.8, specular=0.3)

# Scene objects
scene = [
    Sphere(Vector3(0, -100.5, -1), 100, floor_material),
    Sphere(Vector3(-1, 0, -2), 0.5, red),
    Sphere(Vector3(0, 0, -2), 0.5, green),
    Sphere(Vector3(1, 0, -2), 0.5, blue),
    Sphere(Vector3(0, 1, -2), 0.5, yellow),
]

lights = [
    Light(Vector3(-2, 2, 0), Vector3(1, 0, 0)),
    Light(Vector3(2, 2, 0), Vector3(0, 1, 0)),
    Light(Vector3(0, 5, -1), Vector3(0, 0, 1)),
    Light(Vector3(0, 0, 1), Vector3(1, 1, 1)),
]

# Render image
image = Image.new("RGB", (width, height))
pixels = image.load()

for y in range(height):
    for x in range(width):
        # Convert pixel coordinates to normalized device coordinates
        ndc_x = (2 * (x + 0.5) / width - 1) * aspect_ratio
        ndc_y = 1 - 2 * (y + 0.5) / height
        
        ray_dir = Vector3(ndc_x, ndc_y, -1).norm()
        ray = Ray(Vector3(0, 0, 0), ray_dir)
        
        color = trace_ray(ray, scene, lights, ambient)
        
        # Convert to 0-255 RGB values
        r = int(color.x * 255)
        g = int(color.y * 255)
        b = int(color.z * 255)
        pixels[x, y] = (r, g, b)

image.save("render.png")
print("Rendering complete! Saved as render.png")