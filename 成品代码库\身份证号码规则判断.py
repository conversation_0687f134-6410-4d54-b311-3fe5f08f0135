import pandas as pd
import re
import xlwings as xw

# 读取Excel文件
df_excel = pd.read_excel('身份证号.xlsx')

# 清理身份证号码
def clean_id(id_str):
    # 移除非数字字符，但保留最后一位可能的X
    id_str = str(id_str).strip()
    if len(id_str) == 18 and id_str[-1].upper() == 'X':
        return re.sub(r'\D', '', id_str[:-1]) + 'X'
    else:
        return re.sub(r'\D', '', id_str)

df_excel['身份证号码'] = df_excel['身份证号码'].apply(clean_id)

# 校验身份证号码
def validate_id(id_number):
    if len(id_number) != 18:
        return "错误"
    
    # 校验码计算
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = '10X98765432'
    sum_value = sum(int(id_number[i]) * weights[i] for i in range(17))
    check_digit = check_codes[sum_value % 11]
    
    return "正确" if id_number[-1].upper() == check_digit else "错误"

# 更新Excel文件中的第二列
df_excel['是否正确'] = df_excel['身份证号码'].apply(validate_id)

# 保存结果
df_excel.to_excel('身份证号_结果.xlsx', index=False)

print("处理完成,结果已保存到 '身份证号_结果.xlsx'")

def validate_id_number(id_number):
    # 检查长度是否为18位
    if len(id_number) != 18:
        return "错误"
    
    # 身份证号码的前17位权重因子
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    # 校验码对应的值
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    # 计算校验和
    sum_value = sum(int(id_number[i]) * weights[i] for i in range(17))
    # 计算校验码
    check_code = check_codes[sum_value % 11]
    
    # 验证第18位校验码
    if id_number[-1].upper() == check_code:
        return "正确"
    else:
        return "错误"

# 将函数应用到Excel中
def apply_validation():
    wb = xw.Book.caller()
    sheet = wb.sheets[0]
    # 从A2开始读取身份证号码，并在B2开始写入结果
    for i in range(1, sheet.range('A1').end('down').row):
        id_number = sheet.range(f'A{i+1}').value
        result = validate_id_number(id_number)
        sheet.range(f'B{i+1}').value = result

# 运行函数
if __name__ == "__main__":
    xw.Book("你的Excel文件名.xlsx").set_mock_caller()
    apply_validation()
