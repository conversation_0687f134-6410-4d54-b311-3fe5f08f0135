{"name": "文生图", "nodes": [{"parameters": {"content": "## Start: Collect Image Prompt (Form Trigger)\n\nEnglish:\n*   **Node:** `On form submission`\n*   **Purpose:** This node serves as the starting point of the workflow. It presents a simple web form to the user to collect the text description (prompt) for the image they want to generate.\n*   **Configuration:** Configured with a form titled \"圖像提示詞 / Image generating prompt\" and a required textarea field labeled \"prompt\" for user input.\n*   **Output:** The node outputs the submitted data, including the text prompt, which is then passed to the next node.\n\n---\n\n## 開始：收集圖片提示詞 (表單觸發器)\n\n繁體中文:\n*   **節點：** `On form submission` (表單觸發器)\n*   **目的：** 此節點作為工作流程的起點。它向使用者展示一個簡單的網路表單，用於收集他們想要生成的圖片的文字描述（提示詞）。\n*   **設定：** 配置了一個標題為「圖像提示詞 / Image generating prompt」的表單，以及一個標記為「prompt」的必填文字區域欄位用於使用者輸入。\n*   **輸出：** 節點輸出提交的資料，包括文字提示詞，這些資料會傳遞給下一個節點。", "height": 840, "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1000, 660], "id": "13cec3a3-e1d8-4e77-952b-b646949bfb61", "name": "Sticky Note - <PERSON> Trigger"}, {"parameters": {"content": "## Step 2: Generate Image (HTTP Request - Gemini API)\n\nEnglish:\n*   **Node:** `textToimage` (HTTP Request)\n*   **Purpose:** This node sends the text prompt collected from the form to the Google Gemini API's image generation endpoint to create the image.\n*   **Configuration:**\n    *   `Method`: POST\n    *   `URL`: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp-image-generation:generateContent`\n    *   `Authentication`: Uses an `httpQueryAuth` credential (named `outGemini` in this example) to add the API key as a query parameter (e.g., `key=YOUR_API_KEY`).\n    *   `Send Body`: Enabled, `Specify Body` set to `json`.\n    *   `JSON Body`: Contains the prompt from the previous node (`{{ $json.prompt }}`) within the required Gemini API request structure, specifying that both TEXT and IMAGE response modalities are desired.\n*   **Output:** The API response containing the generated image data (typically base64 encoded) and potentially text.\n\n---\n\n## 步驟 2：生成圖片 (HTTP 請求 - Gemini API)\n\n繁體中文:\n*   **節點：** `textToimage` (HTTP 請求)\n*   **目的：** 此節點將從表單收集的文字提示詞發送到 Google Gemini API 的圖片生成端點，以創建圖片。\n*   **設定：**\n    *   `Method` (方法)：POST\n    *   `URL`：`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp-image-generation:generateContent`\n    *   `Authentication` (驗證)：使用 `httpQueryAuth` 憑證（在此範例中名為 `outGemini`）將 API 金鑰添加為查詢參數（例如，`key=YOUR_API_KEY`）。\n    *   `Send Body` (發送主體)：啟用，`Specify Body` (指定主體) 設定為 `json`。\n    *   `JSON Body` (JSON 主體)：包含來自上一個節點的提示詞 (`{{ $json.prompt }}`)，位於所需的 Gemini API 請求結構內，並指定需要 TEXT 和 IMAGE 兩種回應模式。\n*   **輸出：** 包含生成的圖片資料（通常為 base64 編碼）以及可能的文字的 API 回應。", "height": 840, "width": 880, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1700, 660], "id": "18f5f74d-4069-4e38-ad24-acdf53dd3fa0", "name": "Sticky Note - HTTP Request"}, {"parameters": {"content": "## Overall Process - Image Generation\n(See main documentation in Sticky Note - HTTP Request)\n\nEnglish:\nThis specific flow shows Text-to-Image generation.\n*   The `textToimage` HTTP Request node sends a predefined text prompt to the Gemini image generation API.\n*   The API is expected to return an image based on this prompt.\n\n---\n\n## 整體流程 - 圖片生成\n（詳細文檔請參見 便利貼 - HTTP 請求）\n\n繁體中文:\n此特定流程展示了文字轉圖片的生成。\n*   `textToimage` HTTP 請求節點將預定義的文字提示詞發送到 Gemini 圖片生成 API。\n*   預期 API 會根據此提示詞返回一張圖片。\n\n\n---\n\n**Limitations / 限制:**\n\nEnglish:\n*   For best performance, use the following languages: EN, es-MX, ja-JP, zh-CN, hi-IN.\n*   Image generation does not support audio or video inputs.\n*   Image generation may not always trigger:\n    *   The model may output text only. Try asking for image outputs explicitly (e.g. \"generate an image\", \"provide images as you go along\", \"update the image\").\n    *   The model may stop generating partway through. Try again or try a different prompt.\n    *   When generating text for an image, Gemini works best if you first generate the text and then ask for an image with the text.\n\n繁體中文:\n*   為了獲得最佳效能，請使用以下語言：EN, es-MX, ja-JP, zh-CN, hi-IN。\n*   圖片生成不支援音訊或視訊輸入。\n*   圖片生成可能不一定會被觸發：\n    *   模型可能只輸出文字。嘗試明確要求圖片輸出（例如「生成一張圖片」、「請在進行過程中提供圖片」、「更新圖片」）。\n    *   模型可能會中途停止生成。請重試或嘗試不同的提示詞。\n    *   為圖片生成文字時，如果先生成文字，然後再要求生成帶有該文字的圖片，Gemini 的效果最好。", "height": 1260, "width": 580, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [400, 240], "id": "fea94693-1b9e-487d-b9dd-62a352bb17b0", "name": "Sticky Note - Image Generation Overview"}, {"parameters": {"content": "## Step 3: Process Image Output (Convert to File)\n\nEnglish:\n*   **Node:** `Convert to File`\n*   **Purpose:** The previous HTTP Request node receives the generated image data from the Gemini API, often in base64 encoded format within a JSON structure. This node takes that specific piece of data and converts it into a standard binary file format recognized by n8n, making it ready for subsequent nodes (e.g., saving the file, sending it).\n*   **Configuration:**\n    *   `Operation`: Set to `To Binary`.\n    *   `Source Property`: An expression (`={{ $if( $json && $json.candidates[0].content.parts[1].inlineData.data, \"candidates[0].content.parts[1].inlineData.data\", \"candidates[0].content.parts[0].inlineData.data\") }}`) that dynamically finds the base64 data string within the complex Gemini API response structure, checking multiple potential locations.\n*   **Output:** A binary file object representing the generated image.\n\n---\n\n## 步驟 3：處理圖片輸出 (轉換為檔案)\n\n繁體中文:\n*   **節點：** `Convert to File` (轉換為檔案)\n*   **目的：** 前面的 HTTP 請求節點從 Gemini API 接收生成的圖片資料，通常以 base64 編碼格式包含在 JSON 結構中。此節點提取該特定資料，並將其轉換為 n8n 識別的標準二進位檔案格式，使其準備好供後續節點使用（例如，儲存檔案、發送檔案）。\n*   **設定：**\n    *   `Operation` (操作)：設定為 `To Binary` (轉換為二進位)。\n    *   `Source Property` (來源屬性)：一個表達式 (`={{ $if( $json && $json.candidates[0].content.parts[1].inlineData.data, \"candidates[0].content.parts[1].inlineData.data\", \"candidates[0].content.parts[0].inlineData.data\") }}`)，用於在複雜的 Gemini API 回應結構中動態尋找 base64 資料字串，檢查多個可能的位址。\n*   **輸出：** 一個代表生成圖片的二進位檔案物件。", "height": 840, "width": 700, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2600, 660], "id": "73eb5ea7-b74a-4ad7-9991-183821d14c63", "name": "Sticky Note - Convert to File"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp-image-generation:generateContent", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"{{ $json.prompt }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"responseModalities\": [\n    \"TEXT\",  \n    \"IMAGE\"\n    ]\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1500, 440], "id": "884c5fd8-f24b-4ec4-8be3-4a896159f554", "name": "textToimage2"}, {"parameters": {"formTitle": "图片提示词 / Image generating prompt", "formDescription": "输入对图片的描述", "formFields": {"values": [{"fieldLabel": "prompt", "fieldType": "textarea", "placeholder": "您好，請問您可以製作一隻長著翅膀、戴著高帽的豬，在一個充滿綠意的未來科幻城市上空飛翔的 3d 渲染圖像嗎？", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [1180, 440], "id": "73ec5580-6170-4757-a35c-4dbb56af603e", "name": "On form submission10", "webhookId": "7ebf40d0-7682-4da3-89be-37610a2590ab"}, {"parameters": {"operation": "toBinary", "sourceProperty": "={{ $if( $json && $json.candidates[0].content.parts[1].inlineData.data, \"candidates[0].content.parts[1].inlineData.data\", \"candidates[0].content.parts[0].inlineData.data\") }}", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1740, 440], "id": "e7cadf3d-3f51-48e6-8a8c-339ffdfe1d1f", "name": "Convert to File2"}, {"parameters": {"content": "# text2image", "height": 80, "width": 2300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1000, 240], "id": "70e741c2-02ab-4540-95e2-be1623ac14cb", "name": "Sticky Note (Form Trigger)3"}, {"parameters": {"content": "# Workflow", "height": 300, "width": 2300, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1000, 340], "id": "aeb2e4dd-efb3-4db2-9fcc-52ed1509701f", "name": "Sticky Note (Form Trigger)4"}], "pinData": {}, "connections": {"textToimage2": {"main": [[{"node": "Convert to File2", "type": "main", "index": 0}]]}, "On form submission10": {"main": [[{"node": "textToimage2", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "19ed3c4a-8602-4845-ad05-afe2a8c5d06b", "meta": {"instanceId": "e91fe71fc41c70f3cd8d49a32329efea986f840395719f4b74ff7ced73716ab0"}, "id": "Me8YgN6uy9zWXmoZ", "tags": []}