import pandas as pd
import datetime
from dateutil.relativedelta import relativedelta
import os

def main():
    # 设置文件路径
    file_path = 'C:/Users/<USER>/Downloads/AI写代码/全市近三年被行政处罚人员.xlsx'
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return
    
    # 读取Excel文件
    try:
        print(f"正在读取文件: {file_path}")
        # 尝试读取Excel文件，不指定特定列名
        data = pd.read_excel(file_path, sheet_name='总表')
        print("文件读取成功")
        
        # 输出列名以检查实际列名
        print("Excel文件列名:", data.columns.tolist())
        
        # 检查必要的列是否存在
        required_columns = ['姓名', '身份证号码', '处罚日期']
        
        # 查找实际列名 - 可能有不同的命名方式
        actual_columns = []
        id_column = None  # 身份证号码列
        date_column = None  # 处罚日期列
        
        for col in data.columns:
            col_str = str(col).lower()
            if '身份证' in col_str or '证件' in col_str or '证号' in col_str:
                id_column = col
                actual_columns.append(col)
            elif '处罚' in col_str and ('日期' in col_str or '时间' in col_str):
                date_column = col
                actual_columns.append(col)
            elif '姓名' in col_str or '名字' in col_str:
                actual_columns.append(col)
        
        if not id_column or not date_column:
            print("错误：无法找到必要的列（身份证号码和处罚日期）")
            return
        
        print(f"找到的关键列: {actual_columns}")
        print(f"身份证号码列: {id_column}")
        print(f"处罚日期列: {date_column}")
        
        # 确保数据类型正确
        data[id_column] = data[id_column].astype(str)
        data[date_column] = pd.to_datetime(data[date_column], errors='coerce')
        
        # 筛选掉无效的身份证号码和处罚日期
        valid_data = data.dropna(subset=[id_column, date_column])
        print(f"有效数据行数: {len(valid_data)}")
        
        # 计算处罚时的年龄
        valid_data['出生日期'] = valid_data[id_column].apply(extract_birth_date)
        valid_data = valid_data.dropna(subset=['出生日期'])
        print(f"有效出生日期的数据行数: {len(valid_data)}")
        
        # 计算处罚时的年龄
        valid_data['处罚时年龄'] = valid_data.apply(
            lambda x: calculate_age(x['出生日期'], x[date_column]), axis=1
        )
        
        # 筛选未成年人数据（年龄小于18岁）
        minor_data = valid_data[valid_data['处罚时年龄'] < 18]
        print(f"未成年人处罚记录数: {len(minor_data)}")
        
        # 统计每个未成年人的处罚次数
        punishment_counts = minor_data[id_column].value_counts().reset_index()
        punishment_counts.columns = ['身份证号码', '处罚次数']
        
        # 获取受处罚两次及以上的未成年人
        multiple_punishment = punishment_counts[punishment_counts['处罚次数'] >= 2]
        
        # 计算统计结果
        total_minor_count = minor_data[id_column].nunique()  # 未成年人总数
        multiple_punishment_count = len(multiple_punishment)  # 多次受处罚未成年人数
        
        # 计算比例
        if total_minor_count > 0:
            percentage = (multiple_punishment_count / total_minor_count) * 100
        else:
            percentage = 0
        
        # 输出结果
        print("\n===== 统计结果 =====")
        print(f"近三年受处罚未成年人总数: {total_minor_count}人")
        print(f"其中受处罚两次及以上的未成年人数: {multiple_punishment_count}人")
        print(f"多次受处罚未成年人占比: {percentage:.2f}%")
        
        # 查看多次处罚未成年人的详细分布
        distribution = multiple_punishment['处罚次数'].value_counts().sort_index()
        print("\n多次受处罚未成年人分布:")
        for times, count in distribution.items():
            print(f"  受处罚{times}次的未成年人: {count}人")
        
        # 导出多次受处罚未成年人明细
        if not multiple_punishment.empty:
            # 获取多次处罚未成年人的所有记录
            multiple_ids = multiple_punishment['身份证号码'].tolist()
            multiple_records = minor_data[minor_data[id_column].isin(multiple_ids)]
            
            # 导出为Excel文件
            multiple_records.to_excel('多次受处罚未成年人明细.xlsx', index=False)
            print("\n已导出多次受处罚未成年人明细到：多次受处罚未成年人明细.xlsx")
            
            # 导出统计结果
            summary = punishment_counts.sort_values('处罚次数', ascending=False)
            summary.to_excel('未成年人受处罚统计.xlsx', index=False)
            print("已导出未成年人受处罚统计到：未成年人受处罚统计.xlsx")
    
    except Exception as e:
        print(f"处理数据时出错: {str(e)}")


def extract_birth_date(id_card):
    """从身份证号码中提取出生日期"""
    try:
        id_card = str(id_card).strip()
        
        # 处理标准18位身份证
        if len(id_card) == 18:
            birth_str = id_card[6:14]
            return datetime.datetime.strptime(birth_str, '%Y%m%d')
        
        # 处理老式15位身份证
        elif len(id_card) == 15:
            birth_str = '19' + id_card[6:12]
            return datetime.datetime.strptime(birth_str, '%Y%m%d')
        
        return None
    except:
        return None


def calculate_age(birth_date, punish_date):
    """精确计算到天的年龄"""
    if birth_date is None or punish_date is None:
        return None
    
    # 计算年龄（考虑闰年等因素）
    age = relativedelta(punish_date, birth_date).years
    
    return age


if __name__ == "__main__":
    main()
