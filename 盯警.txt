文件目录中有一个名为“执法记录仪日志.xlsx”的文件，其中第一列为标题，包含“日志时间”“操作部门”两列内容。文件目录中还有一个名为“警情.xlsx”的文件，包含“接警单编号”“签收时间”“管辖单位”三列内容。“操作部门”与“管辖单位”可以进行关联匹配。现在我有一个需求，即如果“警情.xlsx”中的某“管辖单位”的某一条“接警单编号”对应的“签收时间”之后的0到900秒内，如果能在“执法记录仪日志.xlsx”中找到对应的“操作部门”中相应“日志时间”记录，则认为该条记录符合要求；若找不到，则认为不符合要求。现要求生成最后的不符合要求的“接警单编号”记录，去重后匹配“警情.xlsx”中的所有字段内容。请帮我写一个Python代码。

文件目录中有一个名为“执法记录仪日志.xlsx”的文件，其中第一列为标题，包含“日志时间”“操作部门”两列内容。文件目录中还有一个名为“警情.xlsx”的文件，包含“接警单编号”“签收时间”“管辖单位”三列内容。“操作部门”与“管辖单位”可以进行关联匹配。现在我有一个需求，要求通过“操作部门”与“管辖单位”进行关联，找到某“接警单编号”对应的“签收时间”后0到900秒内，有无能够对应的“日志时间”，如果有，则认为该“接警单编号”记录符合要求；如果没有，则认为不符合要求，现要求找到不符合要求的所有“接警单编号”记录，并展示其在“警情.xlsx”中的所有字段，新成一个新的文件。请帮我写一个Python代码。

文件目录中有一个名为“执法记录仪日志.xlsx”的文件，其中第一列为标题，包含“日志时间”“操作部门”两列内容。文件目录中还有一个名为“警情.xlsx”的文件，包含“接警单编号”“签收时间”“管辖单位”三列内容。现在要求以“警情.xlsx”为主表，用“操作部门”与“管辖单位”关联两张表，用“日志时间”减去“签收时间”，得到0到900秒之间的“接警单编号”内容，再用“警情.xlsx”中的“接警单编号”内容减去上述得到的接警单编号内容，得到最终的接警单编号，并匹配“警情.xlsx”中的其他字段，展示完整的内容，帮我写一个Python脚本。