document.addEventListener('DOMContentLoaded', () => {
    // 获取Canvas元素和上下文
    const canvas = document.getElementById('gameCanvas');
    const ctx = canvas.getContext('2d');
    
    // 设置Canvas大小
    canvas.width = 600;
    canvas.height = 400;
    
    // 游戏变量
    const gridSize = 20;
    const initialSpeed = 150; // 初始速度（毫秒）
    let speed = initialSpeed;
    let snake = [];
    let food = {};
    let direction = 'right';
    let nextDirection = 'right';
    let score = 0;
    let gameRunning = false;
    let gameLoop;
    
    // 霓虹灯颜色
    const colors = {
        snakeHead: '#ff2a6d', // 霓虹粉
        snakeBody: '#05d9e8', // 霓虹蓝
        food: '#d300c5',      // 霓虹紫
        grid: 'rgba(5, 217, 232, 0.1)',
        background: '#0d0221' // 深色背景
    };
    
    // 粒子系统
    let particles = [];
    
    // 初始化游戏
    function initGame() {
        snake = [
            {x: 5 * gridSize, y: 10 * gridSize},
            {x: 4 * gridSize, y: 10 * gridSize},
            {x: 3 * gridSize, y: 10 * gridSize}
        ];
        
        generateFood();
        score = 0;
        direction = 'right';
        nextDirection = 'right';
        speed = initialSpeed;
        updateScore();
        
        document.getElementById('gameOver').style.display = 'none';
        document.getElementById('startBtn').style.display = 'none';
        document.getElementById('restartBtn').style.display = 'none';
    }
    
    // 生成食物
    function generateFood() {
        // 确保食物不会生成在蛇身上
        let validPosition = false;
        while (!validPosition) {
            food = {
                x: Math.floor(Math.random() * (canvas.width / gridSize)) * gridSize,
                y: Math.floor(Math.random() * (canvas.height / gridSize)) * gridSize
            };
            
            validPosition = true;
            for (let segment of snake) {
                if (segment.x === food.x && segment.y === food.y) {
                    validPosition = false;
                    break;
                }
            }
        }
        
        // 生成食物粒子效果
        createFoodParticles();
    }
    
    // 创建食物粒子效果
    function createFoodParticles() {
        for (let i = 0; i < 10; i++) {
            particles.push({
                x: food.x + gridSize / 2,
                y: food.y + gridSize / 2,
                size: Math.random() * 3 + 1,
                speedX: (Math.random() - 0.5) * 3,
                speedY: (Math.random() - 0.5) * 3,
                color: colors.food,
                life: 30
            });
        }
    }
    
    // 更新粒子
    function updateParticles() {
        for (let i = 0; i < particles.length; i++) {
            particles[i].x += particles[i].speedX;
            particles[i].y += particles[i].speedY;
            particles[i].life--;
            
            if (particles[i].life <= 0) {
                particles.splice(i, 1);
                i--;
            }
        }
    }
    
    // 绘制粒子
    function drawParticles() {
        for (let particle of particles) {
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fillStyle = particle.color;
            ctx.globalAlpha = particle.life / 30;
            ctx.fill();
            ctx.globalAlpha = 1;
        }
    }
    
    // 更新游戏状态
    function update() {
        // 更新方向
        direction = nextDirection;
        
        // 移动蛇
        const head = {x: snake[0].x, y: snake[0].y};
        
        switch (direction) {
            case 'up':
                head.y -= gridSize;
                break;
            case 'down':
                head.y += gridSize;
                break;
            case 'left':
                head.x -= gridSize;
                break;
            case 'right':
                head.x += gridSize;
                break;
        }
        
        // 检查碰撞
        if (
            head.x < 0 || head.x >= canvas.width ||
            head.y < 0 || head.y >= canvas.height ||
            checkCollision(head)
        ) {
            gameOver();
            return;
        }
        
        // 添加新的头部
        snake.unshift(head);
        
        // 检查是否吃到食物
        if (head.x === food.x && head.y === food.y) {
            score += 10;
            updateScore();
            generateFood();
            
            // 每得50分加快速度
            if (score % 50 === 0 && speed > 50) {
                speed -= 10;
                clearInterval(gameLoop);
                gameLoop = setInterval(gameStep, speed);
            }
        } else {
            // 如果没有吃到食物，移除尾部
            snake.pop();
        }
        
        // 更新粒子
        updateParticles();
    }
    
    // 检查蛇身碰撞
    function checkCollision(position) {
        for (let i = 1; i < snake.length; i++) {
            if (position.x === snake[i].x && position.y === snake[i].y) {
                return true;
            }
        }
        return false;
    }
    
    // 绘制游戏
    function draw() {
        // 清除画布
        ctx.fillStyle = colors.background;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 绘制网格
        drawGrid();
        
        // 绘制蛇
        drawSnake();
        
        // 绘制食物
        drawFood();
        
        // 绘制粒子
        drawParticles();
    }
    
    // 绘制网格
    function drawGrid() {
        ctx.strokeStyle = colors.grid;
        ctx.lineWidth = 0.5;
        
        for (let x = 0; x < canvas.width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvas.height);
            ctx.stroke();
        }
        
        for (let y = 0; y < canvas.height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(canvas.width, y);
            ctx.stroke();
        }
    }
    
    // 绘制蛇
    function drawSnake() {
        for (let i = 0; i < snake.length; i++) {
            const segment = snake[i];
            
            // 蛇头使用不同颜色
            if (i === 0) {
                drawNeonRect(segment.x, segment.y, gridSize, gridSize, colors.snakeHead);
            } else {
                // 蛇身使用渐变色
                const colorIndex = i % 5;
                const alpha = 1 - (colorIndex * 0.15);
                drawNeonRect(segment.x, segment.y, gridSize, gridSize, colors.snakeBody, alpha);
            }
        }
    }
    
    // 绘制带霓虹效果的矩形
    function drawNeonRect(x, y, width, height, color, alpha = 1) {
        // 绘制发光效果
        ctx.shadowColor = color;
        ctx.shadowBlur = 10;
        
        // 绘制填充
        ctx.fillStyle = color;
        ctx.globalAlpha = alpha;
        ctx.fillRect(x, y, width, height);
        
        // 绘制边框
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 1;
        ctx.strokeRect(x, y, width, height);
        
        // 重置阴影和透明度
        ctx.shadowBlur = 0;
        ctx.globalAlpha = 1;
    }
    
    // 绘制食物
    function drawFood() {
        // 绘制发光效果
        ctx.shadowColor = colors.food;
        ctx.shadowBlur = 15;
        
        // 绘制食物
        ctx.fillStyle = colors.food;
        ctx.beginPath();
        ctx.arc(food.x + gridSize / 2, food.y + gridSize / 2, gridSize / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // 添加内部细节
        ctx.fillStyle = 'white';
        ctx.beginPath();
        ctx.arc(food.x + gridSize / 2, food.y + gridSize / 2, gridSize / 4, 0, Math.PI * 2);
        ctx.fill();
        
        // 重置阴影
        ctx.shadowBlur = 0;
    }
    
    // 游戏步骤
    function gameStep() {
        update();
        draw();
    }
    
    // 开始游戏
    function startGame() {
        if (!gameRunning) {
            initGame();
            gameRunning = true;
            gameLoop = setInterval(gameStep, speed);
        }
    }
    
    // 游戏结束
    function gameOver() {
        clearInterval(gameLoop);
        gameRunning = false;
        
        document.getElementById('finalScore').textContent = score;
        document.getElementById('gameOver').style.display = 'block';
        document.getElementById('restartBtn').style.display = 'inline-block';
    }
    
    // 更新分数显示
    function updateScore() {
        document.getElementById('score').textContent = score;
    }
    
    // 键盘控制
    document.addEventListener('keydown', (e) => {
        switch (e.key) {
            case 'ArrowUp':
                if (direction !== 'down') nextDirection = 'up';
                break;
            case 'ArrowDown':
                if (direction !== 'up') nextDirection = 'down';
                break;
            case 'ArrowLeft':
                if (direction !== 'right') nextDirection = 'left';
                break;
            case 'ArrowRight':
                if (direction !== 'left') nextDirection = 'right';
                break;
        }
    });
    
    // 按钮事件监听
    document.getElementById('startBtn').addEventListener('click', startGame);
    document.getElementById('restartBtn').addEventListener('click', startGame);
    
    // 初始绘制
    draw();
});