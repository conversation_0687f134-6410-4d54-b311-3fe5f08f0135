import pytesseract
from PIL import Image, ImageTk, ImageEnhance
import openai
import requests
from bs4 import BeautifulSoup
import os
import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
import numpy as np
import cv2
import base64
import json
import datetime
import sys

# 设置Tesseract路径
pytesseract.pytesseract.tesseract_cmd = r'E:\Program Files\Tesseract-OCR\tesseract.exe'

class 试题处理系统GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("错题训练集自动生成器")
        self.root.geometry("1200x800")
        
        # 设置窗口样式
        self.root.configure(bg='#f0f0f0')
        self.style = ttk.Style()
        self.style.configure('TButton', padding=6, relief="flat", background="#2196F3")
        self.style.configure('TLabel', font=('微软雅黑', 10))
        
        # 获取程序运行目录
        self.程序目录 = os.path.dirname(os.path.abspath(sys.argv[0]))
        
        # 配置文件路径（使用绝对路径）
        self.配置文件 = os.path.join(self.程序目录, "config.json")
        self.训练集文件 = os.path.join(self.程序目录, "错题训练集.json")
        
        print(f"配置文件路径: {self.配置文件}")  # 调试信息
        
        # 训练集相关初始化
        self.训练集 = []
        
        # 初始化界面元素变量
        self.url_entry = None
        self.model_entry = None
        self.key_entry = None
        self.ocr_provider = None
        self.ocr_api_frame = None
        
        # 初始化图片相关变量
        self.current_image = None
        self.display_image = None
        self.photo = None
        self.image_x = 0
        self.image_y = 0
        self.displayed_size = (0, 0)
        self.scale_factor = 1.0
        
        # 初始化框选相关变量
        self.start_x = None
        self.start_y = None
        self.rect_id = None
        self.selection_coords = None
        
        # 初始化AI配置
        self.ai_config = {
            'api_url': '',
            'model_name': '',
            'api_key': ''
        }
        
        # 初始化OCR配置，为每个服务商创建独立的配置
        self.ocr_config = {
            'current_provider': 'baidu',  # 默认选择百度OCR
            'local': {},  # 本地识别配置
            'baidu': {    # 百度OCR配置
                'api_key': '',
                'secret_key': '',
                'api_type': '高精度版'  # 默认选择高精度版
            },
            'youdao': {   # 有道OCR配置
                'api_key': ''
            },
            'tencent': {  # 腾讯OCR配置
                'api_key': '',
                'secret_key': '',
                'app_id': ''
            }
        }
        
        # 先加载配置和训练集
        self.加载配置()
        self.加载训练集()
        
        # 创建界面
        self.创建界面()
        
        # 填充配置到界面
        self.root.after(100, self.填充配置到界面)  # 延迟100ms执行填充

    def 填充配置到界面(self):
        # 填充AI配置
        if hasattr(self, 'url_entry') and self.url_entry:
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, self.ai_config.get('api_url', ''))
            
        if hasattr(self, 'model_entry') and self.model_entry:
            self.model_entry.delete(0, tk.END)
            self.model_entry.insert(0, self.ai_config.get('model_name', ''))
            
        if hasattr(self, 'key_entry') and self.key_entry:
            self.key_entry.delete(0, tk.END)
            self.key_entry.insert(0, self.ai_config.get('api_key', ''))
        
        # 填充OCR配置
        if hasattr(self, 'ocr_provider') and self.ocr_provider:
            # 获取当前选择的服务商
            current_provider = self.ocr_config.get('current_provider', 'baidu')
            provider_name = {
                'local': '本地识别',
                'baidu': '百度OCR',
                'youdao': '有道OCR',
                'tencent': '腾讯OCR'
            }.get(current_provider, '百度OCR')
            
            self.ocr_provider.set(provider_name)
            # 确保在设置服务商后再触发切换事件
            self.root.after(200, self.切换OCR服务商)

    def 加载配置(self):
        try:
            if os.path.exists(self.配置文件):
                with open(self.配置文件, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    if 'ai_config' in config:
                        self.ai_config.update(config['ai_config'])
                    if 'ocr_config' in config:
                        self.ocr_config.update(config['ocr_config'])
                    print("加载的配置:", self.ocr_config)  # 调试信息
        except Exception as e:
            print(f"加载配置失败: {str(e)}")

    def 保存配置(self):
        try:
            config = {
                'ai_config': self.ai_config,
                'ocr_config': self.ocr_config
            }
            with open(self.配置文件, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print("配置保存成功:", config)  # 调试信息
        except Exception as e:
            print(f"保存配置失败: {str(e)}")

    def 创建界面(self):
        # 创建主滚动画布
        main_canvas = tk.Canvas(self.root)
        main_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加垂直滚动条
        main_scrollbar = ttk.Scrollbar(self.root, orient=tk.VERTICAL, command=main_canvas.yview)
        main_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置画布
        main_canvas.configure(yscrollcommand=main_scrollbar.set)
        
        # 创建内容框架
        content_frame = ttk.Frame(main_canvas)
        canvas_window = main_canvas.create_window((0, 0), window=content_frame, anchor=tk.NW, tags="content")
        
        # 创建左右分栏
        self.paned = ttk.PanedWindow(content_frame, orient=tk.HORIZONTAL)
        self.paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧图片区域
        left_frame = ttk.Frame(self.paned)
        self.paned.add(left_frame, weight=1)
        
        # 图片画布
        self.canvas = tk.Canvas(left_frame, bg='white')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind("<ButtonPress-1>", self.开始框选)
        self.canvas.bind("<B1-Motion>", self.框选中)
        self.canvas.bind("<ButtonRelease-1>", self.结束框选)
        
        # 右侧控制和显示区域
        right_frame = ttk.Frame(self.paned)
        self.paned.add(right_frame, weight=1)
        
        # AI配置区域
        ai_config_frame = ttk.LabelFrame(right_frame, text="AI配置", padding=5)
        ai_config_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # API URL配置
        url_frame = ttk.Frame(ai_config_frame)
        url_frame.pack(fill=tk.X, pady=2)
        ttk.Label(url_frame, text="API接口链接：").pack(side=tk.LEFT)
        self.url_entry = ttk.Entry(url_frame, width=40)
        self.url_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # 模型名称配置
        model_frame = ttk.Frame(ai_config_frame)
        model_frame.pack(fill=tk.X, pady=2)
        ttk.Label(model_frame, text="模型名称：    ").pack(side=tk.LEFT)
        self.model_entry = ttk.Entry(model_frame, width=40)
        self.model_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # API密配置
        key_frame = ttk.Frame(ai_config_frame)
        key_frame.pack(fill=tk.X, pady=2)
        ttk.Label(key_frame, text="API密钥：      ").pack(side=tk.LEFT)
        self.key_entry = ttk.Entry(key_frame, width=40)
        self.key_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # 保存配置按钮
        ttk.Button(ai_config_frame, text="保存AI配置", command=self.保存AI配置).pack(pady=5)
        
        # OCR配置区域
        ocr_config_frame = ttk.LabelFrame(right_frame, text="OCR配置", padding=5)
        ocr_config_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # OCR服务商选择
        provider_frame = ttk.Frame(ocr_config_frame)
        provider_frame.pack(fill=tk.X, pady=2)
        ttk.Label(provider_frame, text="OCR服务商：").pack(side=tk.LEFT)
        self.ocr_provider = ttk.Combobox(
            provider_frame, 
            values=['本地识别', '百度OCR', '有道OCR', '腾讯OCR'],
            width=37
        )
        self.ocr_provider.set('本地识别')
        self.ocr_provider.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.ocr_provider.bind('<<ComboboxSelected>>', self.切换OCR服务商)
        
        # OCR API配置框架
        self.ocr_api_frame = ttk.Frame(ocr_config_frame)
        self.ocr_api_frame.pack(fill=tk.X, pady=2)
        
        # 保存OCR配置按钮
        ttk.Button(ocr_config_frame, text="保存OCR配置", command=self.保存OCR配置).pack(pady=5)
        
        # 控制按钮
        control_frame = ttk.Frame(right_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(control_frame, text="选择图片", command=self.选择图片).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="识别选区", command=self.识别选区).pack(side=tk.LEFT, padx=5)
        
        # 识别结果显示
        ttk.Label(right_frame, text="识别到的题目：", font=('微软雅黑', 10, 'bold')).pack(pady=5)
        self.题目文本框 = scrolledtext.ScrolledText(
            right_frame, 
            width=50, 
            height=10,
            font=('微软雅黑', 10),
            wrap=tk.WORD
        )
        self.题目文本框.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 生成按钮
        ttk.Button(right_frame, text="生成相似题目", command=self.生成相似题目).pack(pady=5)
        
        # 生成结果显示
        ttk.Label(right_frame, text="生成的相似题目：", font=('微软雅黑', 10, 'bold')).pack(pady=5)
        self.结果文本框 = scrolledtext.ScrolledText(
            right_frame,
            width=50,
            height=15,
            font=('微软雅黑', 10),
            wrap=tk.WORD
        )
        self.结果文本框.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 在右侧框架添加错题训练集按钮
        训练集按钮框架 = ttk.Frame(right_frame)
        训练集按钮框架.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(
            训练集按钮框架, 
            text="添加到错题训练集", 
            command=self.添加到训练集
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            训练集按钮框架, 
            text="查看错题训练集", 
            command=self.打开训练集窗口
        ).pack(side=tk.LEFT, padx=5)
        
        # 设置右侧框架的权重，使文本框能够合理分配空间
        right_frame.grid_rowconfigure(0, weight=1)
        right_frame.grid_rowconfigure(1, weight=2)  # 给生成结果分配更多空间
        
        # 配置画布滚动区域
        def configure_scroll_region(event):
            # 更新画布的滚动区域
            main_canvas.configure(scrollregion=main_canvas.bbox("all"))
            # 设置内容框架的宽度与画布相同
            main_canvas.itemconfig("content", width=main_canvas.winfo_width())
            
        content_frame.bind('<Configure>', configure_scroll_region)
        
        # 绑定鼠标滚轮事件
        def on_mousewheel(event):
            main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            
        main_canvas.bind_all("<MouseWheel>", on_mousewheel)
        
        # 绑定画布大小变化事件
        def on_canvas_configure(event):
            # 更新内容框架的宽度
            main_canvas.itemconfig("content", width=event.width)
            
        main_canvas.bind('<Configure>', on_canvas_configure)

    def 选择图片(self):
        图片路径 = filedialog.askopenfilename(
            title="选择试卷图片",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp")]
        )
        if 图片路径:
            # 打开并显示图片
            self.current_image = Image.open(图片路径)
            # 调整图片大小以适应画布
            self.显示图片()
    
    def 显示图片(self):
        if self.current_image:
            # 获取画布大小
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            # 计算缩放比例
            img_width, img_height = self.current_image.size
            scale = min(canvas_width/img_width, canvas_height/img_height)
            
            # 缩放图片
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            self.display_image = self.current_image.resize((new_width, new_height))
            self.photo = ImageTk.PhotoImage(self.display_image)
            
            # 清除画布并显示图片
            self.canvas.delete("all")
            
            # 计算图片在画布上的实际位置
            self.image_x = (canvas_width - new_width) // 2
            self.image_y = (canvas_height - new_height) // 2
            
            self.canvas.create_image(
                self.image_x,
                self.image_y,
                image=self.photo,
                anchor=tk.NW  # 改为左上角锚点
            )
            
            # 保存图片信息
            self.scale_factor = scale
            self.displayed_size = (new_width, new_height)
    
    def 开始框选(self, event):
        # 获取鼠标相对于画布的位置
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        # 检查是否在图片范围内
        if self.在图片范围内(canvas_x, canvas_y):
            self.start_x = canvas_x
            self.start_y = canvas_y
            
            # 删除之前的选框
            if self.rect_id:
                self.canvas.delete(self.rect_id)
    
    def 框选中(self, event):
        if self.start_x is not None and self.start_y is not None:
            if self.rect_id:
                self.canvas.delete(self.rect_id)
            
            # 获取当前鼠标位置
            curX = self.canvas.canvasx(event.x)
            curY = self.canvas.canvasy(event.y)
            
            # 限制框选范围在图片内
            curX = self.限制坐标范围(curX, self.image_x, self.image_x + self.displayed_size[0])
            curY = self.限制坐标范围(curY, self.image_y, self.image_y + self.displayed_size[1])
            
            self.rect_id = self.canvas.create_rectangle(
                self.start_x, self.start_y, curX, curY,
                outline='red', width=2
            )
    
    def 结束框选(self, event):
        if self.start_x is not None and self.start_y is not None:
            end_x = self.canvas.canvasx(event.x)
            end_y = self.canvas.canvasy(event.y)
            
            # 限制结束坐标在图片范围
            end_x = self.限制坐标范围(end_x, self.image_x, self.image_x + self.displayed_size[0])
            end_y = self.限制坐标范围(end_y, self.image_y, self.image_y + self.displayed_size[1])
            
            # 保存相对于图片左上角的坐标
            self.selection_coords = (
                min(self.start_x, end_x) - self.image_x,
                min(self.start_y, end_y) - self.image_y,
                max(self.start_x, end_x) - self.image_x,
                max(self.start_y, end_y) - self.image_y
            )
    
    def 在图片范围内(self, x, y):
        """检查坐标是否在图片范围内"""
        return (self.image_x <= x <= self.image_x + self.displayed_size[0] and
                self.image_y <= y <= self.image_y + self.displayed_size[1])
    
    def 限制坐标范围(self, value, min_value, max_value):
        """限制坐标在指定范围内"""
        return max(min_value, min(value, max_value))

    def 切换OCR服务商(self, event=None):
        # 清除当前的API配置框架中的所有组件
        for widget in self.ocr_api_frame.winfo_children():
            widget.destroy()
        
        provider = self.ocr_provider.get()
        provider_key = {
            '本地识别': 'local',
            '百度OCR': 'baidu',
            '有道OCR': 'youdao',
            '腾讯OCR': 'tencent'
        }[provider]
        
        # 获取当前服务商的配置
        current_config = self.ocr_config.get(provider_key, {})
        
        if provider != '本地识别':
            # API Key 输入框
            key_frame = ttk.Frame(self.ocr_api_frame)
            key_frame.pack(fill=tk.X, pady=2)
            ttk.Label(key_frame, text="API Key：    ").pack(side=tk.LEFT)
            self.ocr_api_key_entry = ttk.Entry(key_frame, width=40)
            self.ocr_api_key_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
            
            # 填充保存的API Key
            if current_config.get('api_key'):
                self.ocr_api_key_entry.delete(0, tk.END)
                self.ocr_api_key_entry.insert(0, current_config['api_key'])
            
            if provider in ['百度OCR', '腾讯OCR']:
                # Secret Key 输入框
                secret_frame = ttk.Frame(self.ocr_api_frame)
                secret_frame.pack(fill=tk.X, pady=2)
                ttk.Label(secret_frame, text="Secret Key：").pack(side=tk.LEFT)
                self.ocr_secret_key_entry = ttk.Entry(secret_frame, width=40)
                self.ocr_secret_key_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
                
                # 填充保存的Secret Key
                if current_config.get('secret_key'):
                    self.ocr_secret_key_entry.delete(0, tk.END)
                    self.ocr_secret_key_entry.insert(0, current_config['secret_key'])
                
                if provider == '百度OCR':
                    # 添加接口类型选择
                    type_frame = ttk.Frame(self.ocr_api_frame)
                    type_frame.pack(fill=tk.X, pady=2)
                    ttk.Label(type_frame, text="接口类型：    ").pack(side=tk.LEFT)
                    self.baidu_api_type = ttk.Combobox(
                        type_frame,
                        values=['标准版', '高精度版'],
                        width=37,
                        state='readonly'
                    )
                    # 设置保存的接口类型
                    self.baidu_api_type.set(current_config.get('api_type', '高精度版'))
                    self.baidu_api_type.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
            
            if provider == '腾讯OCR':
                # App ID 输入框
                app_id_frame = ttk.Frame(self.ocr_api_frame)
                app_id_frame.pack(fill=tk.X, pady=2)
                ttk.Label(app_id_frame, text="App ID：    ").pack(side=tk.LEFT)
                self.ocr_app_id_entry = ttk.Entry(app_id_frame, width=40)
                self.ocr_app_id_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
                
                # 填充保存的App ID
                if current_config.get('app_id'):
                    self.ocr_app_id_entry.delete(0, tk.END)
                    self.ocr_app_id_entry.insert(0, current_config['app_id'])

    def 保存OCR配置(self):
        provider = self.ocr_provider.get()
        provider_key = {
            '本地识别': 'local',
            '百度OCR': 'baidu',
            '有道OCR': 'youdao',
            '腾讯OCR': 'tencent'
        }[provider]
        
        # 更新当前选择的服务商
        self.ocr_config['current_provider'] = provider_key
        
        # 获取当前服务商的配置
        current_config = self.ocr_config.get(provider_key, {})
        
        if provider != '本地识别':
            # 更新当前服务商的配置
            current_config['api_key'] = self.ocr_api_key_entry.get().strip()
            
            if provider in ['百度OCR', '腾讯OCR']:
                current_config['secret_key'] = self.ocr_secret_key_entry.get().strip()
                
                if provider == '百度OCR':
                    current_config['api_type'] = self.baidu_api_type.get()
            
            if provider == '腾讯OCR':
                current_config['app_id'] = self.ocr_app_id_entry.get().strip()
            
            # 更新配置字典
            self.ocr_config[provider_key] = current_config
        
        # 保存配置
        try:
            config = {
                'ai_config': self.ai_config,
                'ocr_config': self.ocr_config
            }
            with open(self.配置文件, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"OCR配置已保存: {config}")  # 调试信息
            messagebox.showinfo("成功", "OCR配置已保存")
        except Exception as e:
            print(f"保存OCR配置失败: {str(e)}")  # 调试信息
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def 识别选区(self):
        if not self.selection_coords or not self.current_image:
            messagebox.showwarning("警告", "请先选择区域")
            return
            
        try:
            # 将显示坐标转换为原图坐标
            x1, y1, x2, y2 = self.selection_coords
            原图_x1 = int(x1 / self.scale_factor)
            原图_y1 = int(y1 / self.scale_factor)
            原图_x2 = int(x2 / self.scale_factor)
            原图_y2 = int(y2 / self.scale_factor)
            
            # 裁剪选中区域
            crop_image = self.current_image.crop((原图_x1, 原图_y1, 原图_x2, 原图_y2))
            
            # 获取当前服务商
            current_provider = self.ocr_config.get('current_provider', 'local')
            
            # 根据不同的OCR服务商调用不同的识别方法
            if current_provider == 'local':
                文字内容 = self.本地识别(crop_image)
            elif current_provider == 'baidu':
                文字内容 = self.百度OCR识别(crop_image)
            elif current_provider == 'youdao':
                文字内容 = self.有道OCR识别(crop_image)
            elif current_provider == 'tencent':
                文字内容 = self.腾讯OCR识别(crop_image)
            
            # 显示识别结果
            self.题目文本框.delete(1.0, tk.END)
            self.题目文本框.insert(tk.END, 文字内容)
            
        except Exception as e:
            messagebox.showerror("错误", f"识别出错: {str(e)}")

    def 本地识别(self, image):
        # 放大图
        width, height = image.size
        scale_factor = 2
        image = image.resize((width * scale_factor, height * scale_factor), Image.Resampling.LANCZOS)
        
        # 识别文字
        custom_config = r'--oem 3 --psm 6 -c preserve_interword_spaces=1'
        文字内容 = pytesseract.image_to_string(image, lang='chi_sim', config=custom_config)
        return self.清理识别文本(文字内容)

    def 百度OCR识别(self, image):
        from aip import AipOcr
        
        # 保存图片到临时文件
        temp_path = "temp.jpg"
        image.save(temp_path)
        
        # 获取百度OCR的配置
        baidu_config = self.ocr_config.get('baidu', {})
        if not baidu_config.get('api_key') or not baidu_config.get('secret_key'):
            raise Exception("请先配置百度OCR的API Key和Secret Key")
        
        # 初始化百度OCR客户端
        client = AipOcr(
            self.ocr_config.get('app_id', ''),  # 如果没有app_id，使用空字符串
            baidu_config.get('api_key', ''),    # 从百度配置中获取api_key
            baidu_config.get('secret_key', '')  # 从百度配置中获取secret_key
        )
        
        try:
            # 读取图片
            with open(temp_path, 'rb') as f:
                image = f.read()
                
            # 根据选择的接口类型调用不同的识别方法
            if baidu_config.get('api_type') == '高精度版':
                result = client.basicAccurate(image)  # 调用高精度版接口
            else:
                result = client.basicGeneral(image)   # 调用标准版接口
                
            # 删除临时文件
            os.remove(temp_path)
            
            # 提取文字
            if 'words_result' in result:
                文字内容 = '\n'.join([item['words'] for item in result['words_result']])
                return self.清理识别文本(文字内容)
            return ''
            
        except Exception as e:
            # 确保临时文件被删除
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise Exception(f"百度OCR识别失败：{str(e)}")

    def 有道OCR识别(self, image):
        import uuid
        import time
        import hashlib
        
        # 有道OCR API地址
        url = 'https://openapi.youdao.com/ocrapi'
        
        # 保存图片到临时文件
        temp_path = "temp.jpg"
        image.save(temp_path)
        
        # 读取图片并base64编码
        with open(temp_path, 'rb') as f:
            q = base64.b64encode(f.read()).decode('utf-8')
        
        # 删除临时文件
        os.remove(temp_path)
        
        # 生成签名
        app_key = self.ocr_config['api_key']
        salt = str(uuid.uuid1())
        curtime = str(int(time.time()))
        sign = hashlib.sha256((app_key + q + salt + curtime).encode('utf-8')).hexdigest()
        
        # 请数据
        data = {
            'img': q,
            'langType': 'zh-CHS',
            'detectType': '10012',
            'imageType': '1',
            'appKey': app_key,
            'salt': salt,
            'sign': sign,
            'curtime': curtime
        }
        
        # 发送请求
        response = requests.post(url, data=data)
        result = response.json()
        
        # 提取文字
        if 'Result' in result:
            文字内容 = '\n'.join([region['text'] for region in result['Result']['regions']])
            return self.清理识别文本(文字内容)
        return ''

    def 腾讯OCR识别(self, image):
        from tencentcloud.common import credential
        from tencentcloud.common.profile.client_profile import ClientProfile
        from tencentcloud.common.profile.http_profile import HttpProfile
        from tencentcloud.ocr.v20181119 import ocr_client, models
        import base64
        
        # 保存图片到临时文件
        temp_path = "temp.jpg"
        image.save(temp_path)
        
        # 读取图片并base64编码
        with open(temp_path, 'rb') as f:
            img_base64 = base64.b64encode(f.read()).decode('utf-8')
        
        # 删除临时文件
        os.remove(temp_path)
        
        try:
            # 实例化认证对象
            cred = credential.Credential(
                self.ocr_config['api_key'],
                self.ocr_config['secret_key']
            )
            
            # 实例化http选项
            httpProfile = HttpProfile()
            httpProfile.endpoint = "ocr.tencentcloudapi.com"
            
            # 实例化client选项
            clientProfile = ClientProfile()
            clientProfile.httpProfile = httpProfile
            
            # 实例化OCR client对象
            client = ocr_client.OcrClient(cred, "ap-guangzhou", clientProfile)
            
            # 实例化请求对象
            req = models.GeneralBasicOCRRequest()
            req.ImageBase64 = img_base64
            
            # 发起请求
            resp = client.GeneralBasicOCR(req)
            
            # 提取文字
            文字内容 = '\n'.join([text.DetectedText for text in resp.TextDetections])
            return self.清理识别文本(文字内容)
            
        except Exception as e:
            raise Exception(f"腾讯OCR识别失败：{str(e)}")

    def 清理识别文本(self, 文本):
        # 清理常见的识别错误
        清理后文本 = 文本.replace('|', '')  # 删除误识别的竖线
        清理后文本 = 清理后文本.replace('_', '')  # 删除下划线
        清理后文本 = 清理后文本.replace('①', '1')  # 替换特殊字符
        清理后文本 = 清理后文本.replace('②', '2')
        清理后文本 = 清理后文本.replace('③', '3')
        
        # 处理换行
        lines = 清理后文本.splitlines()
        有效行 = []
        
        for line in lines:
            line = line.strip()
            if line:  # 只保留非空行
                有效行.append(line)
        
        # 合并行，保持适当的换行
        清理后文本 = '\n'.join(有效行)
        
        return 清理后文本

    def 保存AI配置(self):
        api_url = self.url_entry.get().strip()
        model_name = self.model_entry.get().strip()
        api_key = self.key_entry.get().strip()
        
        if not all([api_url, model_name, api_key]):
            messagebox.showwarning("警告", "请填写完整的AI配置信息")
            return
            
        self.ai_config['api_url'] = api_url
        self.ai_config['model_name'] = model_name
        self.ai_config['api_key'] = api_key
        
        # 保存配置
        try:
            config = {
                'ai_config': self.ai_config,
                'ocr_config': self.ocr_config
            }
            with open(self.配置文件, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"AI配置已保存: {config}")  # 调试信息
            messagebox.showinfo("成功", "AI配置已保存")
        except Exception as e:
            print(f"保存AI配置失败: {str(e)}")  # 调试信息
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def 生成相似题目(self):
        if not all(self.ai_config.values()):
            messagebox.showwarning("警告", "请先完成AI配置")
            return
            
        try:
            # 获取识别的文本内容
            识别内容 = self.题目文本框.get(1.0, tk.END).strip()
            
            if not 识别内容:
                messagebox.showwarning("警告", "请先识别题目内容")
                return
            
            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.ai_config['api_key']}",
                "Content-Type": "application/json"
            }
            
            # 构建请求数据
            data = {
                "model": self.ai_config['model_name'],
                "messages": [
                    {"role": "system", "content": "你是一个教育专家，请根据给定的题目生成3道相似的题目。生成时不要使用markdown格式。"},
                    {"role": "user", "content": f"请根据这道题目生成3道相似的题目：{识别内容}"}
                ]
            }
            
            # 发送请求到AI API
            response = requests.post(
                self.ai_config['api_url'],
                headers=headers,
                json=data
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            相似题目 = result['choices'][0]['message']['content']
            
            # 清理markdown格式
            相似题目 = 相似题目.replace('**', '')
            
            # 显示结果
            self.结果文本框.delete(1.0, tk.END)
            self.结果文本框.insert(tk.END, 相似题目)
            
        except requests.exceptions.RequestException as e:
            if "429" in str(e):
                messagebox.showerror("错误", "API请求次数超限")
            elif "401" in str(e):
                messagebox.showerror("错误", "API密钥无效")
            else:
                messagebox.showerror("错误", f"API请求失败: {str(e)}")
        except Exception as e:
            messagebox.showerror("错误", f"生成题目出错: {str(e)}")

    def 加载训练集(self):
        try:
            if os.path.exists(self.训练集文件):
                with open(self.训练集文件, 'r', encoding='utf-8') as f:
                    self.训练集 = json.load(f)
            else:
                # 如果文件不存在，创建空文件
                with open(self.训练集文件, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                self.训练集 = []
        except Exception as e:
            print(f"加载训练集失败: {str(e)}")
            self.训练集 = []

    def 保存训练集(self):
        try:
            with open(self.训练集文件, 'w', encoding='utf-8') as f:
                json.dump(self.训练集, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存训练集失败: {str(e)}")

    def 添加到训练集(self):
        原题 = self.题目文本框.get(1.0, tk.END).strip()
        相似题目 = self.结果文本框.get(1.0, tk.END).strip()
        
        if not 原题 or not 相似题目:
            messagebox.showwarning("警告", "请先识别题目并生成相似题目")
            return
        
        # 创建题目记录
        题目记录 = {
            "原题": 原题,
            "相似题目": 相似题目,
            "添加时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.训练集.append(题目记录)
        self.保存训练集()
        messagebox.showinfo("成功", "已添加到训练集")

    def 打开训练集窗口(self):
        # 创建新窗口
        训练集窗口 = tk.Toplevel(self.root)
        训练集窗口.title("错题训练集")
        训练集窗口.geometry("1200x800")  # 增加窗口大小
        
        # 创建主框架
        main_frame = ttk.Frame(训练集窗口)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏
        工具栏 = ttk.Frame(main_frame)
        工具栏.pack(fill=tk.X, pady=5)
        
        ttk.Button(
            工具栏, 
            text="删除选中", 
            command=lambda: self.删除训练集题目(题目列表)
        ).pack(side=tk.LEFT, padx=5)
        
        # 导出按钮框架
        导出框架 = ttk.Frame(工具栏)
        导出框架.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            导出框架,
            text="导出为文本",
            command=lambda: self.导出训练集('txt')
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            导出框架,
            text="导出为Word",
            command=lambda: self.导出训练集('docx')
        ).pack(side=tk.LEFT, padx=2)
        
        # 创建列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建题目列表
        题目列表 = ttk.Treeview(
            list_frame,
            columns=("序号", "原题", "添加时间"),
            show="headings",
            height=20  # 设置显示的行数
        )
        
        # 设置列标题
        题目列表.heading("序号", text="序号")
        题目列表.heading("原题", text="原题")
        题目列表.heading("添加时间", text="添加时间")
        
        # 设置列宽
        题目列表.column("序号", width=50, anchor=tk.CENTER)
        题目列表.column("原题", width=800)
        题目列表.column("添加时间", width=150, anchor=tk.CENTER)
        
        # 添加垂直滚动条
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=题目列表.yview)
        题目列表.configure(yscrollcommand=v_scrollbar.set)
        
        # 添加水平滚动条
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=题目列表.xview)
        题目列表.configure(xscrollcommand=h_scrollbar.set)
        
        # 放置列表和滚动条
        题目列表.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # 配置grid权重
        list_frame.grid_columnconfigure(0, weight=1)
        list_frame.grid_rowconfigure(0, weight=1)
        
        # 填充数据
        for i, 题目 in enumerate(self.训练集, 1):
            题目列表.insert("", tk.END, values=(
                i,
                题目["原题"].replace("\n", " "),  # 将换行替换为空格
                题目["添加时间"]
            ))
        
        # 添加双击查看详情事件
        题目列表.bind("<Double-1>", lambda e: self.查看题目详情(题目列表))

    def 删除训练集题目(self, 题目列表):
        选中项 = 题目列表.selection()
        if not 选中项:
            messagebox.showwarning("警告", "请先选择要删除的题目")
            return
            
        if messagebox.askyesno("确认", "确定要删除选中的题目吗？"):
            for item in 选中项:
                索引 = 题目列表.index(item)
                del self.训练集[索引]
                题目列表.delete(item)
            self.保存训练集()

    def 查看题目详情(self, 题目列表):
        选中项 = 题目列表.selection()
        if not 选中项:
            return
            
        索引 = 题目列表.index(选中项[0])
        题目 = self.训练集[索引]
        
        # 创建详情窗口
        详情窗口 = tk.Toplevel(self.root)
        详情窗口.title("题目详情")
        详情窗口.geometry("1000x800")  # 增加窗口大小
        
        # 创建主框架并添加内边距
        main_frame = ttk.Frame(详情窗口, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 原题显示
        ttk.Label(main_frame, text="原题：", font=('微软雅黑', 10, 'bold')).pack(anchor=tk.W, pady=(0, 5))
        原题文本框 = scrolledtext.ScrolledText(
            main_frame, 
            width=80, 
            height=15,
            font=('微软雅黑', 10),
            wrap=tk.WORD
        )
        原题文本框.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        原题文本框.insert(tk.END, 题目["原题"])
        原题文本框.configure(state='disabled')
        
        # 相似题目显示
        ttk.Label(main_frame, text="相似题目：", font=('微软雅黑', 10, 'bold')).pack(anchor=tk.W, pady=(0, 5))
        相似题目文本框 = scrolledtext.ScrolledText(
            main_frame, 
            width=80, 
            height=20,
            font=('微软雅黑', 10),
            wrap=tk.WORD
        )
        相似题目文本框.pack(fill=tk.BOTH, expand=True)
        相似题目文本框.insert(tk.END, 题目["相似题目"])
        相似题目文本框.configure(state='disabled')

    def 导出训练集(self, 格式='txt'):
        if 格式 == 'txt':
            文件类型 = [("文本文件", "*.txt")]
            默认扩展名 = ".txt"
        else:
            文件类型 = [("Word文档", "*.docx")]
            默认扩展名 = ".docx"
        
        文件路径 = filedialog.asksaveasfilename(
            defaultextension=默认扩展名,
            filetypes=文件类型,
            title="导出训练集"
        )
        
        if 文件路径:
            try:
                if 格式 == 'txt':
                    self.导出为文本(文件路径)
                else:
                    self.导出为Word(文件路径)
                messagebox.showinfo("成功", "训练集导出成功")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def 导出为文本(self, 文件路径):
        with open(文件路径, 'w', encoding='utf-8') as f:
            for i, 题目 in enumerate(self.训练集, 1):
                f.write(f"题目 {i}\n")
                f.write("原题：\n")
                f.write(f"{题目['原题']}\n\n")
                f.write("相似题目：\n")
                f.write(f"{题目['相似题目']}\n")
                f.write("\n" + "="*50 + "\n\n")

    def 导出为Word(self, 文件路径):
        from docx import Document
        from docx.shared import Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.oxml.ns import qn
        
        # 创建Word文档
        doc = Document()
        
        # 设置默认字体
        doc.styles['Normal'].font.name = 'Times New Roman'
        doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        
        # 设置标题
        标题 = doc.add_heading('错题训练集', level=0)
        标题.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 设置标题字体
        for run in 标题.runs:
            run.font.name = 'Times New Roman'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        
        # 添加题目
        for i, 题目 in enumerate(self.训练集, 1):
            # 添加题目编号
            题目标题 = doc.add_heading(f'题目 {i}', level=1)
            for run in 题目标题.runs:
                run.font.name = 'Times New Roman'
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            
            # 添加原题标签
            原题段落 = doc.add_paragraph()
            原题标签 = 原题段落.add_run('原题：')
            原题标签.font.name = 'Times New Roman'
            原题标签._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            原题标签.bold = True
            
            # 添加原题内容
            原题段落 = doc.add_paragraph()
            原题段落.paragraph_format.first_line_indent = Pt(20)
            原题文本 = 原题段落.add_run(题目['原题'])
            原题文本.font.name = 'Times New Roman'
            原题文本._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            
            # 添加相似题目标签
            相似题目段落 = doc.add_paragraph()
            相似题目标签 = 相似题目段落.add_run('相似题目：')
            相似题目标签.font.name = 'Times New Roman'
            相似题目标签._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            相似题目标签.bold = True
            
            # 添加相似题目内容
            相似题目段落 = doc.add_paragraph()
            相似题目段落.paragraph_format.first_line_indent = Pt(20)
            相似题目文本 = 相似题目段落.add_run(题目['相似题目'])
            相似题目文本.font.name = 'Times New Roman'
            相似题目文本._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            
            # 添加分隔线
            分隔线段落 = doc.add_paragraph()
            分隔线 = 分隔线段落.add_run('='*50)
            分隔线.font.name = 'Times New Roman'
            
            # 添加空行
            doc.add_paragraph()
        
        # 保存文档
        doc.save(文件路径)

def main():
    root = tk.Tk()
    app = 试题处理系统GUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
