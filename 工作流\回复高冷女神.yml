app:
  description: 你是一个情场高手、恋爱专家，你面对的是一个高冷的女神，她经常已读不回，你要根据对方发过来的话，生成一个回复，不能让对方感觉出来是AI回复的，要自然且能够拿捏住对方，不能做舔狗。你只需要直接告诉我应该和对方说什么，不需要任何解释。
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: chat
  name: 回复高冷女神
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/hunyuan:0.0.5@956d1ee1a8e4153aa942ea4c2650f9b15f7d11d890004dc4423733bfc3f583bb
kind: app
model_config:
  agent_mode:
    enabled: false
    max_iteration: 5
    strategy: function_call
    tools: []
  annotation_reply:
    enabled: false
  chat_prompt_config: {}
  completion_prompt_config: {}
  dataset_configs:
    datasets:
      datasets: []
    reranking_enable: false
    retrieval_model: multiple
    top_k: 4
  dataset_query_variable: ''
  external_data_tools: []
  file_upload:
    allowed_file_extensions:
    - .JPG
    - .JPEG
    - .PNG
    - .GIF
    - .WEBP
    - .SVG
    - .MP4
    - .MOV
    - .MPEG
    - .WEBM
    allowed_file_types: []
    allowed_file_upload_methods:
    - remote_url
    - local_file
    enabled: false
    image:
      detail: high
      enabled: false
      number_limits: 3
      transfer_methods:
      - remote_url
      - local_file
    number_limits: 3
  model:
    completion_params:
      stop: []
    mode: chat
    name: hunyuan-turbo-latest
    provider: langgenius/hunyuan/hunyuan
  more_like_this:
    enabled: false
  opening_statement: ''
  pre_prompt: 你是一个情场高手、恋爱专家，你面对的是一个高冷的女神，她经常已读不回，你要根据对方发过来的话，生成一个回复，不能让对方感觉出来是AI回复的，要自然且能够拿捏住对方，不能做舔狗。你需要联系上下文来综合判断如何回答。你只需要直接告诉我应该和对方说什么，不需要任何解释。
  prompt_type: simple
  retriever_resource:
    enabled: true
  sensitive_word_avoidance:
    configs: []
    enabled: false
    type: ''
  speech_to_text:
    enabled: false
  suggested_questions: []
  suggested_questions_after_answer:
    enabled: false
  text_to_speech:
    enabled: false
    language: ''
    voice: ''
  user_input_form: []
version: 0.3.0
