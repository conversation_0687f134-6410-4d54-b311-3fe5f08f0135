import pandas as pd
from datetime import datetime, timedelta
import re

def calculate_age(id_number):
    try:
        # 提取出生日期
        birth_date = datetime.strptime(id_number[6:14], '%Y%m%d')
        # 获取今天的日期减一天
        today = datetime.now() - timedelta(days=1)
        # 计算年龄
        age = today.year - birth_date.year
        # 检查是否已经过了今年的生日
        if (today.month, today.day) < (birth_date.month, birth_date.day):
            age -= 1
        return age
    except:
        return None

def verify_id(id_number):
    if len(id_number) != 18:
        return "校验错误"
    
    # 身份证号码前17位对应的权重
    weight = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    # 校验码对应表
    check_code_dict = {
        0: '1', 1: '0', 2: 'X', 3: '9', 4: '8',
        5: '7', 6: '6', 7: '5', 8: '4', 9: '3', 10: '2'
    }
    
    try:
        # 计算前17位与权重的乘积和
        total = sum(int(id_number[i]) * weight[i] for i in range(17))
        # 计算校验码
        check = check_code_dict[total % 11]
        # 比较计算的校验码与实际的校验码
        if str(check).upper() == id_number[17].upper():
            return "校验正确"
        else:
            return "校验错误"
    except:
        return "校验错误"

def main():
    try:
        # 读取Excel文件
        df_match = pd.read_excel('待匹配.xlsx')
        df_area = pd.read_excel('县以上行政区划代码历史大全.xlsx')
        
        # 确保身份证号码列为字符串类型
        df_match['身份证号码'] = df_match['身份证号码'].astype(str)
        
        # 计算年龄
        df_match['年龄'] = df_match['身份证号码'].apply(calculate_age)
        
        # 提取前6位并匹配地区
        df_match['区划代码'] = df_match['身份证号码'].str[:6]
        # 将行政区划代码转为字符串类型进行匹配
        df_area['行政区划代码'] = df_area['行政区划代码'].astype(str)
        # 合并数据
        df_result = pd.merge(df_match, 
                           df_area[['行政区划代码', '省市县']], 
                           left_on='区划代码',
                           right_on='行政区划代码',
                           how='left')
        
        # 验证身份证号码
        df_result['校验状态'] = df_result['身份证号码'].apply(verify_id)
        
        # 选择需要的列并按原顺序排序
        final_columns = ['姓名', '身份证号码', '年龄', '省市县', '校验状态']
        df_result = df_result[final_columns]
        
        # 保存结果
        output_filename = '身份证号码匹配结果_' + datetime.now().strftime('%Y%m%d_%H%M%S') + '.xlsx'
        df_result.to_excel(output_filename, index=False)
        print(f'处理完成，结果已保存至：{output_filename}')
        
    except Exception as e:
        print(f'发生错误：{str(e)}')

if __name__ == '__main__':
    main()