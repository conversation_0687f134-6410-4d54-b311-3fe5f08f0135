{"name": "OCR（20M以下文件）", "nodes": [{"parameters": {"method": "POST", "url": "=https://generativelanguage.googleapis.com/v1beta/models/{{ $('On form submission11').item.json.model }}:generateContent", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyB7ukmdjzMW55MxMTpMQuBsU57aGqJEq40"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"inline_data\": {\n            \"mime_type\": \"{{ $('On form submission11').item.json.data.mimetype }}\",\n            \"data\": \"{{$json.data}}\"\n          }\n        },\n        {\n          \"text\": \"{{ $('On form submission11').item.json.prompt }}\"\n        }\n      ]\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [760, 220], "id": "8bd54fe7-3ba7-4f85-bd94-6efb67a02c42", "name": "HTTP Request1"}, {"parameters": {"content": "# OCR 20mb 以下檔案", "height": 80, "width": 3260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "cb1811b9-dda9-43ea-9049-34104f18c6dd", "name": "Sticky Note (Form Trigger)17"}, {"parameters": {"content": "# Workflow", "height": 300, "width": 3260, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 120], "id": "3d22ca7e-235c-437a-b34b-c7e2befbc3c8", "name": "Sticky Note (Form Trigger)18"}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [460, 220], "id": "f7101a86-5a08-4008-affd-fcdce359626e", "name": "Extract from File"}, {"parameters": {"formTitle": "OCR", "formDescription": "上传图片或文件（20M以内）", "formFields": {"values": [{"fieldLabel": "data", "fieldType": "file", "multipleFiles": false}, {"fieldLabel": "model", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "gemini-2.5-flash-preview-05-20"}, {"option": "gemini-2.5-flash-preview-04-17"}, {"option": "gemini-2.0-flash"}]}}, {"fieldLabel": "prompt", "fieldType": "textarea", "placeholder": "提取图中的内容并保持原有格式输出"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [160, 220], "id": "da17cf3b-f93b-40dc-ab89-e6e04ccc514a", "name": "On form submission11", "webhookId": "edd9ba69-bd52-44c7-ba24-0728a4230300"}, {"parameters": {"operation": "toText", "sourceProperty": "candidates[0].content.parts[0].text", "options": {"encoding": "utf8"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [980, 220], "id": "cbeb8879-5c4d-47d2-9d2b-9566a8b08ea3", "name": "Convert to File"}], "pinData": {}, "connections": {"Extract from File": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "On form submission11": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c24db383-60f8-497e-acda-b0e97120a64b", "meta": {"instanceId": "04695fa805d662a7ca811a5d9568e6cc080c07e6ec9f81360f8146eccb701cc6"}, "id": "d9W944UzSVxWKv1N", "tags": []}