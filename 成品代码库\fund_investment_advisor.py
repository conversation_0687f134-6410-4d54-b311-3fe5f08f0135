import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
import json
import time
import random
from bs4 import BeautifulSoup
import logging
import re
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='fund_advisor.log'
)

class FundCrawler:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'http://fund.eastmoney.com/data/fundranking.html',
            'Connection': 'keep-alive'
        }
        self.base_url = "http://fund.eastmoney.com"
        self.list_url = "http://fund.eastmoney.com/js/fundcode_search.js"
        self.fund_rank_url = "http://fund.eastmoney.com/data/rankhandler.aspx"
        self.fund_value_url = "http://fund.eastmoney.com/Data/Fund_JJJZ_Data.aspx"
        self.fund_real_time_url = "http://fundgz.1234567.com.cn/js/{}.js"

    def _get_fund_real_time_data(self, fund_code):
        """获取基金实时数据"""
        try:
            logging.info(f"开始获取基金{fund_code}的数据")
            
            # 获取基金实时净值和日涨幅
            url = f"http://fundgz.1234567.com.cn/js/{fund_code}.js"
            session = requests.Session()
            session.trust_env = False  # 禁用系统代理
            
            max_retries = 3
            retry_delay = 1
            
            for attempt in range(max_retries):
                try:
                    response = session.get(url, headers=self.headers, timeout=5)
                    response.raise_for_status()
                    
                    # 提取JSON数据
                    text = response.text
                    match = re.search(r'jsonpgz\((.*?)\)', text)
                    if not match:
                        logging.error(f"基金{fund_code}实时数据解析失败，原始数据: {text[:200]}")
                        raise ValueError("无法解析基金实时数据")
                        
                    data = json.loads(match.group(1))
                    logging.info(f"基金{fund_code}实时数据获取成功: {data}")
                    
                    # 获取历史净值数据
                    history_url = f"http://fund.eastmoney.com/f10/F10DataApi.aspx?type=lsjz&code={fund_code}&page=1&per=20"
                    time.sleep(0.5)  # 添加延时，避免频率限制
                    history_response = session.get(history_url, headers=self.headers, timeout=5)
                    history_response.raise_for_status()
                    
                    # 解析历史净值数据
                    soup = BeautifulSoup(history_response.text, 'html.parser')
                    table = soup.find('table', {'class': 'w782 comm lsjz'})
                    
                    if not table:
                        # 尝试使用备用API
                        backup_url = f"http://fund.eastmoney.com/pingzhongdata/{fund_code}.js"
                        time.sleep(0.5)
                        backup_response = session.get(backup_url, headers=self.headers, timeout=5)
                        backup_response.raise_for_status()
                        
                        # 解析备用API数据
                        backup_text = backup_response.text
                        net_worth_match = re.search(r'var Data_netWorthTrend = ([^;]+);', backup_text)
                        if net_worth_match:
                            history_data = json.loads(net_worth_match.group(1))
                            history_data = [
                                {
                                    'date': datetime.fromtimestamp(item['x']/1000),
                                    'nav': item['y'],
                                    'acc_nav': item.get('equityReturn', 0),
                                    'daily_gain': item.get('unitMoney', 0)
                                }
                                for item in history_data
                            ]
                        else:
                            raise ValueError("无法从备用API获取历史净值数据")
                    else:
                        rows = table.find_all('tr')[1:]  # 跳过表头
                        history_data = []
                        
                        for row in rows:
                            cols = row.find_all('td')
                            if len(cols) >= 4:
                                try:
                                    date = cols[0].text.strip()
                                    nav = float(cols[1].text.strip() or 0)
                                    acc_nav = float(cols[2].text.strip() or 0)
                                    daily_gain = float(cols[3].text.strip().replace('%', '') or 0)
                                    history_data.append({
                                        'date': datetime.strptime(date, '%Y-%m-%d'),
                                        'nav': nav,
                                        'acc_nav': acc_nav,
                                        'daily_gain': daily_gain
                                    })
                                except Exception as e:
                                    logging.warning(f"基金{fund_code}历史数据行解析失败: {str(e)}, 行数据: {[col.text.strip() for col in cols]}")
                                    continue
                    
                    logging.info(f"基金{fund_code}历史净值数据获取成功，共{len(history_data)}条记录")
                    
                    # 计算各期涨幅
                    today = datetime.now()
                    week_ago = today - timedelta(days=7)
                    month_ago = today - timedelta(days=30)
                    three_months_ago = today - timedelta(days=90)
                    six_months_ago = today - timedelta(days=180)
                    year_ago = today - timedelta(days=365)
                    
                    def calculate_gain(start_date):
                        start_nav = next((item['nav'] for item in history_data if item['date'] <= start_date), None)
                        latest_nav = history_data[0]['nav'] if history_data else float(data.get('dwjz', 0))
                        if start_nav and latest_nav and start_nav != 0:
                            return ((latest_nav - start_nav) / start_nav) * 100
                        return 0.0
                    
                    gains = {
                        'gain_1w': calculate_gain(week_ago),
                        'gain_1m': calculate_gain(month_ago),
                        'gain_3m': calculate_gain(three_months_ago),
                        'gain_6m': calculate_gain(six_months_ago),
                        'gain_1y': calculate_gain(year_ago)
                    }
                    
                    logging.info(f"基金{fund_code}涨幅计算完成: {gains}")
                    
                    result = {
                        'net_value': float(data.get('dwjz', 0)),
                        'daily_gain': float(data.get('gszzl', 0)),
                        **gains
                    }
                    
                    logging.info(f"基金{fund_code}数据处理完成: {result}")
                    return result
                    
                except (requests.exceptions.RequestException, ValueError, json.JSONDecodeError) as e:
                    if attempt < max_retries - 1:
                        logging.warning(f"基金{fund_code}数据获取失败，第{attempt + 1}次重试: {str(e)}")
                        time.sleep(retry_delay * (attempt + 1))
                    else:
                        raise
                        
        except Exception as e:
            logging.error(f"获取基金{fund_code}实时数据失败: {str(e)}", exc_info=True)
            return {
                'net_value': 0.0,
                'daily_gain': 0.0,
                'gain_1w': 0.0,
                'gain_1m': 0.0,
                'gain_3m': 0.0,
                'gain_6m': 0.0,
                'gain_1y': 0.0
            }

    def get_fund_list(self):
        """获取基金列表"""
        try:
            # 获取基金基本信息
            response = requests.get(self.list_url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            # 解析基金基本信息
            text = response.text
            text = text.replace('var r = ', '').replace(';', '')
            
            try:
                fund_basic_data = json.loads(text)
                logging.info(f"成功获取{len(fund_basic_data)}只基金的基本信息")
            except json.JSONDecodeError:
                start_index = text.find('[')
                end_index = text.rfind(']')
                if start_index == -1 or end_index == -1:
                    raise ValueError("无效的基金基本信息数据格式")
                data_text = text[start_index:end_index+1]
                fund_basic_data = json.loads(data_text)
            
            # 获取净值和涨跌数据
            value_map = {}
            total_funds = len(fund_basic_data)
            processed_count = 0
            
            # 使用线程池加速数据获取
            with ThreadPoolExecutor(max_workers=10) as executor:
                future_to_fund = {executor.submit(self._get_fund_real_time_data, str(item[0]).strip()): item[0] 
                                for item in fund_basic_data}
                
                for future in as_completed(future_to_fund):
                    fund_code = future_to_fund[future]
                    try:
                        value_map[str(fund_code)] = future.result()
                        processed_count += 1
                        if processed_count % 100 == 0:
                            logging.info(f"已处理 {processed_count}/{total_funds} 只基金的净值数据")
                    except Exception as e:
                        logging.warning(f"处理基金{fund_code}数据失败: {str(e)}")
            
            logging.info(f"成功获取{len(value_map)}只基金的净值数据")
            
            # 合并基金信息
            fund_list = []
            for item in fund_basic_data:
                try:
                    fund_code = str(item[0]).strip()
                    fund_info = {
                        'fund_code': fund_code,
                        'fund_name': str(item[2]).strip(),
                        'fund_type': str(item[3]).strip() if len(item) > 3 else '其他',
                        'net_value': value_map.get(fund_code, {}).get('net_value', 0.0),
                        'daily_gain': value_map.get(fund_code, {}).get('daily_gain', 0.0),
                        'gain_1w': value_map.get(fund_code, {}).get('gain_1w', 0.0),
                        'gain_1m': value_map.get(fund_code, {}).get('gain_1m', 0.0),
                        'gain_3m': value_map.get(fund_code, {}).get('gain_3m', 0.0),
                        'gain_6m': value_map.get(fund_code, {}).get('gain_6m', 0.0),
                        'gain_1y': value_map.get(fund_code, {}).get('gain_1y', 0.0),
                        'rank_in_type': 0,
                        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    fund_list.append(fund_info)
                except Exception as e:
                    logging.error(f"处理基金{item[0] if item else 'unknown'}数据失败: {str(e)}")
                    continue
            
            # 计算同类排名
            fund_list = self._calculate_rank_in_type(fund_list)
            logging.info(f"数据获取完成，共处理{len(fund_list)}只基金")
            return fund_list
            
        except Exception as e:
            logging.error(f"获取基金列表失败: {str(e)}")
            raise

    def _get_fund_type_code(self, fund_type):
        """转换基金类型到API所需的代码"""
        type_map = {
            '股票型': 'gp',
            '混合型': 'hh',
            '债券型': 'zq',
            '指数型': 'zs',
            'QDII': 'qdii',
            'LOF': 'lof',
            'FOF': 'fof'
        }
        return type_map.get(fund_type, 'all')

    def get_fund_detail(self, fund_code):
        """获取基金详细信息"""
        try:
            url = f"http://fund.eastmoney.com/Data/FundDataPortfolio_Interface.aspx?type=0&code={fund_code}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            # 解析JavaScript数据
            text = response.text
            data = {}
            
            # 提取基金信息
            for line in text.split(';'):
                if 'var' in line:
                    try:
                        key = line.split('var ')[1].split('=')[0].strip()
                        value = line.split('=', 1)[1].strip()
                        if value.startswith('"') or value.startswith("'"):
                            value = value[1:-1]
                        data[key] = value
                    except:
                        continue
                        
            return data
            
        except Exception as e:
            logging.error(f"获取基金{fund_code}详情失败: {str(e)}")
            raise
            
    def get_fund_ranks(self, fund_code):
        """获取基金排名信息"""
        try:
            detail_data = self.get_fund_detail(fund_code)
            ranks = {
                'rank_1d': 0,
                'rank_3d': 0,
                'rank_1w': 0,
                'rank_2w': 0,
                'rank_1m': 0,
                'rank_3m': 0,
                'rank_6m': 0,
                'rank_1y': 0
            }
            
            # 解析排名数据
            if 'Data_performanceEvaluation' in detail_data:
                try:
                    rank_data = json.loads(detail_data['Data_performanceEvaluation'])
                    for item in rank_data:
                        period = item.get('title', '')
                        rank = item.get('rank', 0)
                        
                        if '1日' in period:
                            ranks['rank_1d'] = rank
                        elif '3日' in period:
                            ranks['rank_3d'] = rank
                        elif '1周' in period:
                            ranks['rank_1w'] = rank
                        elif '2周' in period:
                            ranks['rank_2w'] = rank
                        elif '1月' in period:
                            ranks['rank_1m'] = rank
                        elif '3月' in period:
                            ranks['rank_3m'] = rank
                        elif '6月' in period:
                            ranks['rank_6m'] = rank
                        elif '1年' in period:
                            ranks['rank_1y'] = rank
                except:
                    pass
                    
            return ranks
            
        except Exception as e:
            logging.error(f"获取基金{fund_code}排名失败: {str(e)}")
            raise
            
    def crawl_all_data(self):
        """爬取所有基金数据"""
        try:
            # 获取基金列表
            fund_list = self.get_fund_list()
            result = []
            
            # 获取每个基金的详细信息和排名
            for fund in fund_list:
                try:
                    # 添加随机延时，避免被封IP
                    time.sleep(random.uniform(0.5, 1.5))
                    
                    # 获取排名信息
                    ranks = self.get_fund_ranks(fund['fund_code'])
                    fund.update(ranks)
                    
                    result.append(fund)
                    logging.info(f"成功获取基金{fund['fund_code']}的数据")
                    
                except Exception as e:
                    logging.error(f"处理基金{fund['fund_code']}时出错: {str(e)}")
                    continue
                    
            return result
            
        except Exception as e:
            logging.error(f"爬取所有基金数据失败: {str(e)}")
            raise

    def _calculate_rank_in_type(self, fund_list):
        """计算同类排名"""
        # 按基金类型分组
        type_groups = {}
        for fund in fund_list:
            fund_type = fund['fund_type']
            if fund_type not in type_groups:
                type_groups[fund_type] = []
            type_groups[fund_type].append(fund)
        
        # 计算每个类型内的排名
        for fund_type, funds in type_groups.items():
            # 按综合得分排序
            sorted_funds = sorted(funds, 
                                key=lambda x: (x['gain_1m'] * 0.3 + 
                                             x['gain_3m'] * 0.3 + 
                                             x['gain_6m'] * 0.2 + 
                                             x['gain_1y'] * 0.2), 
                                reverse=True)
            
            # 更新排名
            for rank, fund in enumerate(sorted_funds, 1):
                fund['rank_in_type'] = rank
                
        return fund_list

class FundAnalyzer:
    def __init__(self):
        # 不同类型基金的权重配置
        self.type_weights = {
            '股票型': {'gain_1w': 0.1, 'gain_1m': 0.2, 'gain_3m': 0.3, 'gain_6m': 0.25, 'gain_1y': 0.15},
            '混合型': {'gain_1w': 0.15, 'gain_1m': 0.25, 'gain_3m': 0.25, 'gain_6m': 0.2, 'gain_1y': 0.15},
            '债券型': {'gain_1w': 0.2, 'gain_1m': 0.3, 'gain_3m': 0.2, 'gain_6m': 0.15, 'gain_1y': 0.15},
            '指数型': {'gain_1w': 0.1, 'gain_1m': 0.2, 'gain_3m': 0.3, 'gain_6m': 0.25, 'gain_1y': 0.15},
            'QDII': {'gain_1w': 0.15, 'gain_1m': 0.25, 'gain_3m': 0.25, 'gain_6m': 0.2, 'gain_1y': 0.15},
            'FOF': {'gain_1w': 0.1, 'gain_1m': 0.2, 'gain_3m': 0.3, 'gain_6m': 0.25, 'gain_1y': 0.15}
        }
        
    def calculate_comprehensive_score(self, fund_data):
        """计算基金综合得分"""
        try:
            df = pd.DataFrame(fund_data)
            
            # 按基金类型分组计算得分
            df['comprehensive_score'] = 0.0
            for fund_type, weights in self.type_weights.items():
                mask = df['fund_type'] == fund_type
                if not mask.any():
                    continue
                    
                # 计算各时期得分
                for period, weight in weights.items():
                    if period in df.columns:
                        df.loc[mask, 'comprehensive_score'] += df.loc[mask, period] * weight
                        
            # 计算波动率（使用日涨幅的标准差）
            df['volatility'] = df[['daily_gain', 'gain_1w', 'gain_1m']].std(axis=1)
            
            # 计算风险调整后的得分
            df['risk_adjusted_score'] = df['comprehensive_score'] / (df['volatility'] + 1)  # 加1避免除以0
            
            return df
            
        except Exception as e:
            logging.error(f"计算综合得分失败: {str(e)}")
            raise
            
    def analyze_funds(self, fund_data, fund_type=None, top_n=10):
        """分析基金并给出推荐"""
        try:
            # 转换为DataFrame并计算综合得分
            df = self.calculate_comprehensive_score(fund_data)
            
            # 按基金类型筛选
            if fund_type and fund_type != '全部':
                df = df[df['fund_type'] == fund_type]
                
            # 按风险调整后的得分排序
            df = df.sort_values('risk_adjusted_score', ascending=False)
            
            # 获取前N名基金
            top_funds = df.head(top_n)
            
            # 生成分析报告
            report = []
            for _, fund in top_funds.iterrows():
                fund_report = {
                    'fund_code': fund['fund_code'],
                    'fund_name': fund['fund_name'],
                    'fund_type': fund['fund_type'],
                    'net_value': fund['net_value'],
                    'daily_gain': fund['daily_gain'],
                    'comprehensive_score': round(fund['comprehensive_score'], 2),
                    'risk_adjusted_score': round(fund['risk_adjusted_score'], 2),
                    'gains_info': {
                        '近1周涨幅': f"{fund['gain_1w']:.2f}%",
                        '近1月涨幅': f"{fund['gain_1m']:.2f}%",
                        '近3月涨幅': f"{fund['gain_3m']:.2f}%",
                        '近6月涨幅': f"{fund['gain_6m']:.2f}%",
                        '近1年涨幅': f"{fund['gain_1y']:.2f}%"
                    },
                    'rank_in_type': fund['rank_in_type'],
                    'recommendation_reason': self.generate_recommendation_reason(fund)
                }
                report.append(fund_report)
                
            return report
            
        except Exception as e:
            logging.error(f"分析基金失败: {str(e)}")
            raise
            
    def generate_recommendation_reason(self, fund):
        """生成推荐理由"""
        reasons = []
        
        # 分析涨幅表现
        gains = {
            '近1周': fund['gain_1w'],
            '近1月': fund['gain_1m'],
            '近3月': fund['gain_3m'],
            '近6月': fund['gain_6m'],
            '近1年': fund['gain_1y']
        }
        
        # 找出表现最好的时期
        best_periods = sorted(gains.items(), key=lambda x: x[1], reverse=True)[:2]
        if best_periods[0][1] > 0:
            reasons.append(f"{best_periods[0][0]}表现最佳，涨幅达{best_periods[0][1]:.2f}%")
        if best_periods[1][1] > 0:
            reasons.append(f"{best_periods[1][0]}表现也不错，涨幅{best_periods[1][1]:.2f}%")
            
        # 分析同类排名
        if fund['rank_in_type'] <= 10:
            reasons.append(f"在同类基金中排名第{fund['rank_in_type']}位，属于优秀水平")
        elif fund['rank_in_type'] <= 50:
            reasons.append(f"在同类基金中排名第{fund['rank_in_type']}位，表现良好")
            
        # 分析风险收益
        if fund['risk_adjusted_score'] > 0:
            reasons.append(f"风险调整后收益得分{fund['risk_adjusted_score']:.2f}，投资价值较高")
            
        # 分析当前趋势
        if fund['daily_gain'] > 0:
            reasons.append(f"当日涨幅{fund['daily_gain']:.2f}%，走势向好")
        
        return "；".join(reasons) + "。"

class FundAdvisor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("基金投资推荐助手")
        self.root.geometry("1200x800")
        
        # 初始化爬虫和分析器
        self.crawler = FundCrawler()
        self.analyzer = FundAnalyzer()
        
        # 创建数据库
        self.db_path = 'fund_data.db'
        self.create_database()
        
        # 创建界面
        self.create_gui()
        
        # 添加进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.root, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
        self.progress_bar.pack_forget()  # 默认隐藏进度条

    def get_db_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)

    def create_database(self):
        """创建数据库表"""
        conn = self.get_db_connection()
        try:
            cursor = conn.cursor()
            # 先删除旧表
            cursor.execute('DROP TABLE IF EXISTS funds')
            
            # 创建新表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS funds (
                fund_code TEXT PRIMARY KEY,
                fund_name TEXT,
                fund_type TEXT,
                net_value REAL,
                daily_gain REAL,
                gain_1w REAL,    -- 近1周涨幅
                gain_1m REAL,    -- 近1月涨幅
                gain_3m REAL,    -- 近3月涨幅
                gain_6m REAL,    -- 近6月涨幅
                gain_1y REAL,    -- 近1年涨幅
                rank_in_type INTEGER,  -- 同类排名
                update_time TEXT
            )
            ''')
            conn.commit()
        finally:
            conn.close()

    def create_gui(self):
        """创建图形界面"""
        # 创建顶部工具栏
        toolbar = ttk.Frame(self.root)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="更新数据", command=self.update_fund_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="分析推荐", command=self.analyze_funds).pack(side=tk.LEFT, padx=5)
        
        # 创建筛选框
        filter_frame = ttk.LabelFrame(self.root, text="筛选条件")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(filter_frame, text="基金类型:").pack(side=tk.LEFT, padx=5)
        self.type_var = tk.StringVar()
        self.type_combo = ttk.Combobox(filter_frame, textvariable=self.type_var)
        self.type_combo['values'] = ('全部', '股票型', '混合型', '债券型', '指数型', 'QDII', 'FOF')
        self.type_combo.pack(side=tk.LEFT, padx=5)
        self.type_combo.set('全部')
        
        # 绑定选择事件
        self.type_combo.bind('<<ComboboxSelected>>', self.on_type_selected)
        
        # 创建基金列表
        list_frame = ttk.Frame(self.root)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        columns = ('基金代码', '基金名称', '基金类型', '最新净值', '日涨幅', 
                  '近1周涨幅', '近1月涨幅', '近3月涨幅', '近6月涨幅', '近1年涨幅', '同类排名')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings')
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)
            
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=scrollbar.set)
        self.tree.pack(fill=tk.BOTH, expand=True)
        
        # 绑定双击事件
        self.tree.bind('<Double-1>', self.show_fund_detail)
        
        # 创建状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var)
        status_bar.pack(fill=tk.X, padx=5, pady=5)
        
    def on_type_selected(self, event):
        """处理基金类型选择事件"""
        selected_type = self.type_var.get()
        self.refresh_fund_list(selected_type)

    def refresh_fund_list(self, fund_type='全部'):
        """刷新基金列表"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 获取新的数据库连接
        conn = self.get_db_connection()
        try:
            cursor = conn.cursor()
            if fund_type == '全部':
                cursor.execute("SELECT * FROM funds")
            else:
                cursor.execute("SELECT * FROM funds WHERE fund_type = ?", (fund_type,))
            
            # 添加数据到列表
            for row in cursor.fetchall():
                values = (
                    row[0],  # 基金代码
                    row[1],  # 基金名称
                    row[2],  # 基金类型
                    f"{row[3]:.4f}",  # 最新净值
                    f"{row[4]:.2f}%",  # 日涨幅
                    f"{row[5]:.2f}%",  # 近1周涨幅
                    f"{row[6]:.2f}%",  # 近1月涨幅
                    f"{row[7]:.2f}%",  # 近3月涨幅
                    f"{row[8]:.2f}%",  # 近6月涨幅
                    f"{row[9]:.2f}%",  # 近1年涨幅
                    f"{row[10]}"  # 同类排名
                )
                self.tree.insert('', 'end', values=values)
        finally:
            conn.close()
        
    def update_fund_data(self):
        """更新基金数据"""
        # 禁用更新按钮
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Button):
                widget.configure(state='disabled')
        
        # 显示进度条
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
        self.progress_var.set(0)
        self.status_var.set("正在获取基金列表...")
        self.root.update()
        
        threading.Thread(target=self._update_fund_data_thread, daemon=True).start()
        
    def _update_fund_data_thread(self):
        """在后台线程中更新数据"""
        try:
            # 获取基金列表
            fund_list = self.crawler.get_fund_list()
            total_funds = len(fund_list)
            
            if total_funds == 0:
                raise ValueError("未获取到基金数据")
            
            # 在线程中创建新的数据库连接
            conn = self.get_db_connection()
            try:
                cursor = conn.cursor()
                # 清空旧数据
                cursor.execute("DELETE FROM funds")
                
                # 准备批量插入的数据
                insert_data = []
                for fund in fund_list:
                    insert_data.append((
                        fund['fund_code'],
                        fund['fund_name'],
                        fund['fund_type'],
                        fund['net_value'],
                        fund['daily_gain'],
                        fund['gain_1w'],
                        fund['gain_1m'],
                        fund['gain_3m'],
                        fund['gain_6m'],
                        fund['gain_1y'],
                        fund['rank_in_type'],
                        fund['update_time']
                    ))
                
                # 批量插入数据
                cursor.executemany('''
                INSERT INTO funds VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', insert_data)
                
                # 提交事务
                conn.commit()
                
                # 更新界面
                self.root.after(0, self._update_complete, f"数据更新完成，共更新{total_funds}只基金")
                
            finally:
                conn.close()
            
        except Exception as e:
            error_msg = f"更新数据失败: {str(e)}"
            logging.error(error_msg)
            self.root.after(0, self._update_error, error_msg)
    
    def _update_progress(self, progress, status_text):
        """更新进度条和状态文本"""
        self.progress_var.set(progress)
        self.status_var.set(status_text)
        self.root.update()
    
    def _update_complete(self, status_text):
        """更新完成后的处理"""
        self.status_var.set(status_text)
        self.progress_bar.pack_forget()
        self.refresh_fund_list()
        
        # 重新启用按钮
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Button):
                widget.configure(state='normal')
                
        messagebox.showinfo("提示", status_text)
    
    def _update_error(self, error_msg):
        """更新错误后的处理"""
        self.status_var.set("更新失败")
        self.progress_bar.pack_forget()
        
        # 重新启用按钮
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Button):
                widget.configure(state='normal')
                
        messagebox.showerror("错误", error_msg)

    def analyze_funds(self):
        """分析基金并给出推荐"""
        try:
            # 获取选择的基金类型
            fund_type = self.type_var.get()
            
            # 获取新的数据库连接
            conn = self.get_db_connection()
            try:
                cursor = conn.cursor()
                if fund_type == '全部':
                    cursor.execute("SELECT * FROM funds")
                else:
                    cursor.execute("SELECT * FROM funds WHERE fund_type = ?", (fund_type,))
                
                columns = [description[0] for description in cursor.description]
                fund_data = []
                
                for row in cursor.fetchall():
                    fund_dict = dict(zip(columns, row))
                    fund_data.append(fund_dict)
            finally:
                conn.close()
            
            # 分析基金
            report = self.analyzer.analyze_funds(fund_data, fund_type)
            
            # 显示分析结果
            self.show_analysis_report(report)
            
        except Exception as e:
            logging.error(f"分析基金失败: {str(e)}")
            messagebox.showerror("错误", f"分析基金失败: {str(e)}")
            
    def show_analysis_report(self, report):
        """显示分析报告"""
        # 创建新窗口
        report_window = tk.Toplevel(self.root)
        report_window.title("基金分析报告")
        report_window.geometry("1000x800")
        
        # 创建报告内容
        report_frame = ttk.Frame(report_window)
        report_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建文本框
        text = tk.Text(report_frame, wrap=tk.WORD)
        text.pack(fill=tk.BOTH, expand=True)
        
        # 添加报告内容
        text.insert(tk.END, "=== 基金投资推荐报告 ===\n\n")
        text.insert(tk.END, f"生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for i, fund in enumerate(report, 1):
            text.insert(tk.END, f"Top {i}: {fund['fund_name']}（{fund['fund_code']}）\n")
            text.insert(tk.END, f"基金类型：{fund['fund_type']}\n")
            text.insert(tk.END, f"最新净值：{fund['net_value']:.4f}\n")
            text.insert(tk.END, f"日涨幅：{fund['daily_gain']:.2f}%\n")
            text.insert(tk.END, f"综合得分：{fund['comprehensive_score']:.2f}\n")
            text.insert(tk.END, f"风险调整得分：{fund['risk_adjusted_score']:.2f}\n")
            text.insert(tk.END, "阶段涨幅：\n")
            
            for period, gain in fund['gains_info'].items():
                text.insert(tk.END, f"  {period}：{gain}\n")
                
            text.insert(tk.END, f"同类排名：第{fund['rank_in_type']}名\n")
            text.insert(tk.END, f"推荐理由：{fund['recommendation_reason']}\n")
            text.insert(tk.END, "\n" + "="*50 + "\n\n")
            
        # 设置只读
        text.configure(state='disabled')
            
    def show_fund_detail(self, event):
        """显示基金详细信息"""
        item = self.tree.selection()[0]
        fund_code = self.tree.item(item, "values")[0]
        
        try:
            # 获取新的数据库连接
            conn = self.get_db_connection()
            try:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM funds WHERE fund_code = ?", (fund_code,))
                fund_data = cursor.fetchone()
            finally:
                conn.close()
                
            if fund_data:
                # 创建详情窗口
                detail_window = tk.Toplevel(self.root)
                detail_window.title(f"基金详情 - {fund_code}")
                detail_window.geometry("800x600")
                
                # 创建详情内容
                detail_frame = ttk.Frame(detail_window)
                detail_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                
                # 显示基金基本信息
                basic_frame = ttk.LabelFrame(detail_frame, text="基本信息")
                basic_frame.pack(fill=tk.X, pady=5)
                
                ttk.Label(basic_frame, text=f"基金名称：{fund_data[1]}").pack(anchor=tk.W)
                ttk.Label(basic_frame, text=f"基金代码：{fund_data[0]}").pack(anchor=tk.W)
                ttk.Label(basic_frame, text=f"基金类型：{fund_data[2]}").pack(anchor=tk.W)
                ttk.Label(basic_frame, text=f"最新净值：{fund_data[3]:.4f}").pack(anchor=tk.W)
                ttk.Label(basic_frame, text=f"日涨幅：{fund_data[4]:.2f}%").pack(anchor=tk.W)
                
                # 显示涨幅信息
                gain_frame = ttk.LabelFrame(detail_frame, text="阶段涨幅")
                gain_frame.pack(fill=tk.X, pady=5)
                
                ttk.Label(gain_frame, text=f"近1周涨幅：{fund_data[5]:.2f}%").pack(anchor=tk.W)
                ttk.Label(gain_frame, text=f"近1月涨幅：{fund_data[6]:.2f}%").pack(anchor=tk.W)
                ttk.Label(gain_frame, text=f"近3月涨幅：{fund_data[7]:.2f}%").pack(anchor=tk.W)
                ttk.Label(gain_frame, text=f"近6月涨幅：{fund_data[8]:.2f}%").pack(anchor=tk.W)
                ttk.Label(gain_frame, text=f"近1年涨幅：{fund_data[9]:.2f}%").pack(anchor=tk.W)
                
                # 显示排名信息
                rank_frame = ttk.LabelFrame(detail_frame, text="排名信息")
                rank_frame.pack(fill=tk.X, pady=5)
                
                ttk.Label(rank_frame, text=f"同类排名：第{fund_data[10]}名").pack(anchor=tk.W)
                ttk.Label(rank_frame, text=f"更新时间：{fund_data[11]}").pack(anchor=tk.W)
                
        except Exception as e:
            logging.error(f"显示基金详情失败: {str(e)}")
            messagebox.showerror("错误", f"显示基金详情失败: {str(e)}")
            
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = FundAdvisor()
    app.run() 