import pandas as pd
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from pathlib import Path
import os
import sys
import warnings
warnings.simplefilter('ignore', category=UserWarning)

class DataAnalyzer:
    def __init__(self):
        # 设置高DPI感知
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass
        
        self.window = tk.Tk()
        self.window.title("数据统计工具")
        self.window.geometry("800x600")
        
        # 设置图标
        try:
            # 获取图标文件路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的 exe
                application_path = sys._MEIPASS
            else:
                # 如果是直接运行的 py 文件
                application_path = os.path.dirname(os.path.abspath(__file__))
            
            icon_path = os.path.join(application_path, 'icon.ico')
            if os.path.exists(icon_path):
                self.window.iconbitmap(icon_path)
        except Exception:
            pass  # 如果设置图标失败，使用默认图标
        
        # 数据存储
        self.df = None
        self.selected_column = None
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        # 创建主框架
        self.main_frame = ttk.Frame(self.window)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建上部控制区域
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 文件选择按钮和标签
        self.upload_btn = tk.Button(self.control_frame, text="选择文件", command=self.upload_file)
        self.upload_btn.pack(side=tk.LEFT, padx=5)
        
        self.file_label = tk.Label(self.control_frame, text="未选择文件")
        self.file_label.pack(side=tk.LEFT, padx=5)
        
        # 列选择区域
        self.column_label = tk.Label(self.control_frame, text="选择要统计的列：")
        self.column_label.pack(side=tk.LEFT, padx=5)
        
        self.column_combobox = ttk.Combobox(self.control_frame, state="disabled")
        self.column_combobox.pack(side=tk.LEFT, padx=5)
        
        # 统计按钮
        self.analyze_btn = tk.Button(self.control_frame, text="开始统计", command=self.analyze_data, state="disabled")
        self.analyze_btn.pack(side=tk.LEFT, padx=5)
        
        # 导出按钮
        self.export_btn = tk.Button(self.control_frame, text="导出统计结果", command=self.export_results, state="disabled")
        self.export_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建下部结果显示区域
        self.results_frame = ttk.Frame(self.main_frame)
        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧结果区域
        self.left_frame = ttk.Frame(self.results_frame)
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # 原始统计结果标签
        self.primary_label = tk.Label(self.left_frame, text="数据出现次数统计：")
        self.primary_label.pack(pady=5)
        
        # 创建原始统计表格
        self.tree = ttk.Treeview(self.left_frame, columns=("值", "次数"), show="headings", height=15)
        self.tree.heading("值", text="值")
        self.tree.heading("次数", text="出现次数")
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加左侧滚动条
        left_scrollbar = ttk.Scrollbar(self.left_frame, orient=tk.VERTICAL, command=self.tree.yview)
        left_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=left_scrollbar.set)
        
        # 右侧结果区域
        self.right_frame = ttk.Frame(self.results_frame)
        self.right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # 二次统计结果标签
        self.secondary_label = tk.Label(self.right_frame, text="出现次数分布统计：")
        self.secondary_label.pack(pady=5)
        
        # 创建二次统计表格
        self.secondary_tree = ttk.Treeview(self.right_frame, columns=("出现次数", "数量"), show="headings", height=15)
        self.secondary_tree.heading("出现次数", text="出现次数")
        self.secondary_tree.heading("数量", text="数量")
        self.secondary_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加右侧滚动条
        right_scrollbar = ttk.Scrollbar(self.right_frame, orient=tk.VERTICAL, command=self.secondary_tree.yview)
        right_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.secondary_tree.configure(yscrollcommand=right_scrollbar.set)
        
    def upload_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[
                ("Excel文件", "*.xlsx;*.xls"),
                ("CSV文件", "*.csv"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                # 根据文件扩展名读取文件
                file_extension = Path(file_path).suffix.lower()
                if file_extension == '.csv':
                    self.df = pd.read_csv(file_path)
                else:
                    self.df = pd.read_excel(file_path)
                
                # 更新界面
                self.file_label.config(text=f"已选择文件：{Path(file_path).name}")
                self.column_combobox['values'] = list(self.df.columns)
                self.column_combobox.set("请选择要统计的列")
                self.column_combobox['state'] = 'readonly'
                self.analyze_btn['state'] = 'normal'
                
            except Exception as e:
                messagebox.showerror("错误", f"读取文件时出错：{str(e)}")
                
    def format_value(self, value):
        """格式化数值，去除不必要的小数点和零"""
        # 如果是数字字符串，尝试转换并格式化
        try:
            # 首先检查是否为数字字符串
            float_val = float(value)
            # 如果是整数，去除小数部分
            if float_val.is_integer():
                return str(int(float_val))
            return str(value)
        except (ValueError, TypeError):
            # 如果不是数字，直接返回原值
            return str(value)
        
    def analyze_data(self):
        selected_column = self.column_combobox.get()
        if selected_column == "请选择要统计的列":
            messagebox.showwarning("警告", "请先选择要统计的列！")
            return
            
        # 清空现有结果
        for item in self.tree.get_children():
            self.tree.delete(item)
        for item in self.secondary_tree.get_children():
            self.secondary_tree.delete(item)
            
        # 统计数据前先格式化
        formatted_series = self.df[selected_column].apply(self.format_value)
        value_counts = formatted_series.value_counts()
        
        # 显示原始统计结果
        for value, count in value_counts.items():
            self.tree.insert("", tk.END, values=(value, int(count)))
        
        # 进行二次统计（统计重复次数的分布）并按降序排列
        frequency_distribution = value_counts.value_counts().sort_index(ascending=False)
        
        # 显示二次统计结果
        for repeat_times, count in frequency_distribution.items():
            self.secondary_tree.insert("", tk.END, values=(f"{repeat_times}次", int(count)))
            
        self.export_btn['state'] = 'normal'
        
    def export_results(self):
        if not self.tree.get_children():
            messagebox.showwarning("警告", "没有可导出的数据！")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[
                ("Excel文件", "*.xlsx"),
                ("旧版Excel文件", "*.xls"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            # 收集原始统计结果
            primary_results = []
            for item in self.tree.get_children():
                value, count = self.tree.item(item)['values']
                primary_results.append({'值': value, '出现次数': int(count)})
            
            # 收集二次统计结果
            secondary_results = []
            for item in self.secondary_tree.get_children():
                repeat_times, count = self.secondary_tree.item(item)['values']
                secondary_results.append({'出现次数': repeat_times, '数量': int(count)})
            
            try:
                if file_path.endswith('.txt'):
                    # 如果是txt格式，将两个统计结果都写入文本文
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write("原始统计结果：\n")
                        f.write("值\t出现次数\n")
                        for result in primary_results:
                            f.write(f"{result['值']}\t{result['出现次数']}\n")
                        
                        f.write("\n出现次数分布统计：\n")
                        f.write("出现次数\t数量\n")
                        for result in secondary_results:
                            f.write(f"{result['出现次数']}\t{result['数量']}\n")
                else:
                    # 如果是Excel格式，创建两个sheet
                    with pd.ExcelWriter(file_path, engine='openpyxl' if file_path.endswith('.xlsx') else 'xlwt') as writer:
                        pd.DataFrame(primary_results).to_excel(writer, sheet_name='原始统计结果', index=False)
                        pd.DataFrame(secondary_results).to_excel(writer, sheet_name='出现次数分布', index=False)
                
                messagebox.showinfo("成功", "统计结果已成功导出！")
            except Exception as e:
                messagebox.showerror("错误", f"导出文件时出错：{str(e)}")
    
    def run(self):
        self.window.mainloop()

if __name__ == "__main__":
    app = DataAnalyzer()
    app.run() 