docker volume create n8n_data
docker run -it --rm --name n8n -p 5678:5678 -e N8N_PERMITTED_FILES="/**"  -v n8n_data:/home/<USER>/.n8n -v C:/Users/<USER>/Downloads/AI写代码/n8n/picture:/picture docker.n8n.io/n8nio/n8n


docker volume create n8n_data
docker run -d --name n8n -p 5678:5678 -e N8N_PERMITTED_FILES="/**" -e WEBHOOK_URL=https://webn8n.zjpolice.dpdns.org/ -v n8n_data:/home/<USER>/.n8n -v C:/Users/<USER>/Downloads/AI/pythonProject1/n8n/picture:/picture docker.n8n.io/n8nio/n8n


docker run --name cloudflare_tunnel -d cloudflare/cloudflared:latest tunnel --no-autoupdate run --token eyJhIjoiMGNjZjI2NzBkYzk5MmIwYjYwNGVkYTNiZDU0NDRkNTMiLCJ0IjoiYTQzY2JmNzMtNDYyOC00OTFhLTg1ODMtYzc1YTAzNTAzNDEzIiwicyI6Ik5EWmxaR0pqWkdVdFl6TTVPUzAwTkdZM0xUazJNRGt0WVdRMlltWmtOekE0TW1NeCJ9