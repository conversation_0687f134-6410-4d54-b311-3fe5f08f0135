/**
 * ListWorkflowsHandler unit tests
 */

import { describe, it, expect, jest } from '@jest/globals';
import { getListWorkflowsToolDefinition } from '../../../../src/tools/workflow/list.js';
import { mockApiResponses } from '../../../mocks/n8n-fixtures.js';

// Since this is an integration test, we'll test the definition directly
// rather than mocking the complex handler implementation
jest.mock('../../../../src/tools/workflow/base-handler.js');

describe('getListWorkflowsToolDefinition', () => {
  it('should return the correct tool definition', () => {
    // Execute
    const definition = getListWorkflowsToolDefinition();
    
    // Assert
    expect(definition.name).toBe('list_workflows');
    expect(definition.description).toBeTruthy();
    expect(definition.inputSchema).toBeDefined();
    expect(definition.inputSchema.properties).toHaveProperty('active');
    expect(definition.inputSchema.required).toEqual([]);
  });
});
