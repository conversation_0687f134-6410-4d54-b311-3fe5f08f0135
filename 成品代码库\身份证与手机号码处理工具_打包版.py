import sys
import os
import base64
import PySide2
import pandas as pd
from datetime import datetime
from PySide2.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QPushButton, QFileDialog, QComboBox, QLabel, 
                            QMessageBox, QHBoxLayout, QCheckBox, QGroupBox)
from PySide2.QtCore import Qt
from PySide2.QtGui import QIcon
import io

# 导入base64数据
from base64_data import PHONE_DATA_BASE64, AREA_DATA_BASE64, CSV_ENCODING

# 设置Qt插件路径
if hasattr(sys, 'frozen'):
    os.environ['PATH'] = sys._MEIPASS + ";" + os.environ['PATH']
dirname = os.path.dirname(PySide2.__file__)
plugin_path = os.path.join(dirname, 'plugins', 'platforms')
os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = plugin_path

# 将CSV和Excel数据编码为base64字符串
# 注意: 这里需要你手动将文件内容转换为base64字符串


class IDCardProcessor:
    def __init__(self):
        try:
            # 从base64解码Excel数据
            excel_data = base64.b64decode(AREA_DATA_BASE64)
            # 使用BytesIO读取数据
            self.area_data = pd.read_excel(io.BytesIO(excel_data))
            # 确保行政区划代码列的类型是字符串
            self.area_data['行政区划代码'] = self.area_data['行政区划代码'].astype(str)
        except Exception as e:
            print(f"加载行政区域代码数据失败: {e}")
            raise

    def validate_id_card(self, id_card):
        """校验身份证号码是否正确"""
        if len(id_card) != 18:
            return "位数不对"
        
        if not id_card[:17].isdigit():
            return "校验错误"
        
        factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        checksum = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum = 0
        for i in range(17):
            sum += int(id_card[i]) * factors[i]
        
        if checksum[sum % 11] != id_card[-1].upper():
            return "错误"
            
        return "正确"

    def get_age(self, id_card, calc_date=None, legal_age=True):
        """计算周岁年龄
        Args:
            id_card: 身份证号码
            calc_date: 计算日期，默认为当前日期
            legal_age: 是否计算法律周岁年龄，True则生日次日算满周岁，False则生日当天算满周岁
        """
        # 首先验证身份证号码
        if self.validate_id_card(id_card) == "位数不对":
            return None
        
        try:
            birth_year = int(id_card[6:10])
            birth_month = int(id_card[10:12])
            birth_day = int(id_card[12:14])
            birth_date = datetime(birth_year, birth_month, birth_day)
            
            if calc_date is None:
                calc_date = datetime.now()
            elif isinstance(calc_date, str):
                try:
                    # 尝试解析日期字符串
                    calc_date = datetime.strptime(calc_date.split()[0], '%Y-%m-%d')
                except:
                    try:
                        calc_date = datetime.strptime(calc_date, '%Y-%m-%d')
                    except:
                        # 如果解析失败，使用当前日期
                        calc_date = datetime.now()
            elif isinstance(calc_date, (int, float)):
                # 如果是数值类型，返回None
                return None
            
            # 如果计算法律周岁，则计算日期减1天
            if legal_age:
                from datetime import timedelta
                calc_date = calc_date - timedelta(days=1)
            
            age = calc_date.year - birth_year
            if (calc_date.month, calc_date.day) < (birth_month, birth_day):
                age -= 1
            
            return age
        except Exception as e:
            print(f"计算年龄时发生错误: {e}")
            return None

    def get_gender(self, id_card):
        """获取性别"""
        return "女" if int(id_card[-2]) % 2 == 0 else "男"

    def get_birth_date(self, id_card):
        """获取出生日期"""
        birth_date = f"{id_card[6:10]}-{id_card[10:12]}-{id_card[12:14]}"
        return birth_date

    def get_area(self, id_card):
        """获取户籍所在地"""
        try:
            area_code = str(id_card[:6])
            result = self.area_data[self.area_data['行政区划代码'] == area_code]
            if not result.empty:
                return result.iloc[0]['省市县']
            return f"未找到对应地区(代码:{area_code})"
        except Exception as e:
            print(f"查询户籍所在地失败: {e}")
            return "查询失败"

    def clean_id_card(self, id_card):
        """清理身份证号码数据"""
        if not isinstance(id_card, str):
            return str(id_card)
        
        id_card = id_card.strip()
        id_card = id_card.replace(' ', '')
        id_card = id_card.replace('\n', '')
        id_card = id_card.replace('\r', '')
        id_card = id_card.replace('-', '')
        id_card = id_card.replace('_', '')
        id_card = id_card.replace('.', '')
        id_card = id_card.replace('，', '')
        id_card = id_card.replace(',', '')
        id_card = id_card.replace('"', '')
        id_card = id_card.replace("'", '')
        id_card = id_card.replace('"', '')
        id_card = id_card.replace('"', '')
        
        return id_card

    def mask_id_card(self, id_card):
        """身份证号码脱敏"""
        id_card = self.clean_id_card(id_card)
        if len(id_card) != 18:
            return id_card
        return id_card[:6] + '*' * 8 + id_card[-4:]

class PhoneProcessor:
    def __init__(self):
        try:
            # 从base64解码CSV数据
            csv_data = base64.b64decode(PHONE_DATA_BASE64)
            # 使用BytesIO读取数据，使用检测到的编码
            try:
                # 尝试不同的参数组合
                try:
                    # 新版pandas写法
                    self.phone_data = pd.read_csv(
                        io.BytesIO(csv_data), 
                        encoding=CSV_ENCODING,
                        on_bad_lines='skip'
                    )
                except TypeError:
                    # 旧版pandas写法
                    self.phone_data = pd.read_csv(
                        io.BytesIO(csv_data), 
                        encoding=CSV_ENCODING,
                        error_bad_lines=False
                    )
            except:
                # 如果还是失败，尝试最基础的读取方式
                self.phone_data = pd.read_csv(
                    io.BytesIO(csv_data), 
                    encoding=CSV_ENCODING
                )
            
            # 确保号段列的类型是整数
            self.phone_data['号段'] = self.phone_data['号段'].astype(int)
        except Exception as e:
            print(f"加载手机归属地数据失败: {e}")
            # 添加更详细的错误信息
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
            raise

    def get_phone_info(self, phone):
        """获取手机号码信息"""
        try:
            if len(phone) < 7:
                return None
                
            prefix = int(phone[:7])
            result = self.phone_data[self.phone_data['号段'] == prefix]
            
            if not result.empty:
                return {
                    'location': result.iloc[0]['归属地'],
                    'operator': result.iloc[0]['运营商']
                }
            return None
        except Exception as e:
            print(f"查询手机号码信息失败: {e}")
            return None

    def clean_phone_number(self, phone):
        """清理手机号码数据"""
        if not isinstance(phone, str):
            return str(phone)
        
        phone = phone.strip()
        phone = phone.replace(' ', '')
        phone = phone.replace('\n', '')
        phone = phone.replace('\r', '')
        phone = phone.replace('-', '')
        phone = phone.replace('_', '')
        phone = phone.replace('.', '')
        phone = phone.replace('，', '')
        phone = phone.replace(',', '')
        phone = phone.replace('"', '')
        phone = phone.replace("'", '')
        phone = phone.replace('"', '')
        phone = phone.replace('"', '')
        
        return phone

    def mask_phone_number(self, phone):
        """手机号码脱敏"""
        phone = self.clean_phone_number(phone)
        if len(phone) != 11:
            return phone
        return phone[:3] + '*' * 4 + phone[-4:]

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 初始化属性
        self.date_column_combo = None
        self.fixed_date_combo = None
        self.date_type_combo = None
        self.date_widget = None
        self.age_type_combo = None
        self.column_combo = None
        self.tool_combo = None
        self.file_label = None
        self.file_btn = None
        self.process_btn = None
        self.export_btn = None
        self.checkboxes = []
        self.df = None
        self.function_group = None
        self.function_layout = None
        self.column_label = None
        
        # 设置图标
        if hasattr(sys, '_MEIPASS'):
            icon_path = os.path.join(sys._MEIPASS, '7.ico')
        else:
            icon_path = '7.ico'
        self.setWindowIcon(QIcon(icon_path))
        
        # 初始化处理器
        self.id_processor = IDCardProcessor()
        self.phone_processor = PhoneProcessor()
        
        # 初始化UI
        self.initUI()

    def initUI(self):
        self.setWindowTitle('身份证号码与手机号码处理工具')
        self.setGeometry(100, 100, 1000, 700)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                font-size: 14px;
                color: #333333;
                padding: 5px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QComboBox {
                padding: 5px;
                border: 1px solid #BBBBBB;
                border-radius: 4px;
                background-color: white;
                min-height: 25px;
            }
            QCheckBox {
                font-size: 14px;
                padding: 5px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel('身份证号码与手机号码处理工具', self)
        title_label.setStyleSheet("""
            font-size: 24px;
            color: #1565C0;
            font-weight: bold;
            padding: 20px;
            qproperty-alignment: AlignCenter;
        """)
        main_layout.addWidget(title_label)

        # 工具选择部分
        tool_group = QGroupBox("工具选择")
        tool_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
                border: 2px solid #BBBBBB;
                border-radius: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        tool_layout = QHBoxLayout()
        self.tool_combo = QComboBox(self)
        self.tool_combo.addItems(["请选择工具类型", "身份证号码工具", "手机号码工具"])
        self.tool_combo.currentTextChanged.connect(self.on_tool_changed)
        tool_layout.addWidget(QLabel("选择工具类型："))
        tool_layout.addWidget(self.tool_combo)
        tool_group.setLayout(tool_layout)
        main_layout.addWidget(tool_group)

        # 文件选择部分
        file_group = QGroupBox("文件选择")
        file_group.setStyleSheet(tool_group.styleSheet())
        file_layout = QHBoxLayout()
        self.file_btn = QPushButton('选择文件', self)
        self.file_label = QLabel('未选择文件', self)
        file_layout.addWidget(self.file_btn)
        file_layout.addWidget(self.file_label)
        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)

        # 列选择部分
        column_group = QGroupBox("数据列选择")
        column_group.setStyleSheet(tool_group.styleSheet())
        column_layout = QVBoxLayout()
        
        id_card_layout = QHBoxLayout()
        self.column_label = QLabel("数据列：")
        id_card_layout.addWidget(self.column_label)
        
        # 初始化列选择下拉框
        self.column_combo = QComboBox(self)
        id_card_layout.addWidget(self.column_combo)
        column_layout.addLayout(id_card_layout)
        
        column_group.setLayout(column_layout)
        main_layout.addWidget(column_group)

        # 功能选择部分
        self.function_group = QGroupBox("功能选择")
        self.function_group.setStyleSheet(tool_group.styleSheet())
        self.function_layout = QVBoxLayout()
        self.function_group.setLayout(self.function_layout)
        main_layout.addWidget(self.function_group)

        # 按钮部分
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        
        self.process_btn = QPushButton('处理', self)
        self.export_btn = QPushButton('导出结果', self)
        
        button_layout.addStretch()
        button_layout.addWidget(self.process_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)

        # 版权信息
        copyright_label = QLabel('版权所有：台州市公安局 解晟', self)
        copyright_label.setStyleSheet("""
            color: #000000;
            font-size: 16px;
            padding: 10px;
            qproperty-alignment: AlignCenter;
        """)
        main_layout.addWidget(copyright_label)

        # 连接信号
        self.file_btn.clicked.connect(self.select_file)
        self.process_btn.clicked.connect(self.process_data)
        self.export_btn.clicked.connect(self.export_results)

        # 初始化界面状态
        self.function_group.setVisible(False)

    def on_tool_changed(self, text):
        """处理工具类型选择变化"""
        # 清除旧的复选框
        for i in reversed(range(self.function_layout.count())): 
            widget = self.function_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        
        if text == "身份证号码工具":
            self.column_label.setText("身份证号码列：")
            functions = [
                '校验身份证号码',
                '计算周岁年龄', 
                '提取性别',
                '提取出生日期',
                '匹配户籍地',
                '身份证号码脱敏（中间变星号）'
            ]
            self.function_group.setVisible(True)
        elif text == "手机号码工具":
            self.column_label.setText("手机号码列：")
            functions = [
                '手机号码归属地匹配',
                '手机号码脱敏（中间变星号）'
            ]
            self.function_group.setVisible(True)
        else:
            self.function_group.setVisible(False)
            return

        self.checkboxes = []
        for func in functions:
            checkbox = QCheckBox(func, self)
            if func == '计算周岁年龄':
                # 为计算周岁年龄复选框添加水平布局
                age_layout = QHBoxLayout()
                age_layout.addWidget(checkbox)
                
                # 创建日期选择控件容器
                self.date_widget = QWidget()
                self.date_widget.setVisible(False)  # 初始不可见
                date_layout = QHBoxLayout(self.date_widget)
                date_layout.setContentsMargins(20, 0, 0, 0)  # 左边缩进
                
                # 添加日期选择方式的下拉框
                date_layout.addWidget(QLabel("计算日期："))
                self.date_type_combo = QComboBox(self)
                self.date_type_combo.addItems(["使用当前日期", "使用数据列日期", "使用固定日期"])
                self.date_type_combo.currentTextChanged.connect(self.on_date_type_changed)
                date_layout.addWidget(self.date_type_combo)
                
                # 添加数据列选择下拉框
                self.date_column_combo = QComboBox(self)
                self.date_column_combo.setVisible(False)
                date_layout.addWidget(self.date_column_combo)
                
                # 添加固定日期输入框
                self.fixed_date_combo = QComboBox(self)
                self.fixed_date_combo.setEditable(True)
                self.fixed_date_combo.addItems(["2024-12-01"])
                self.fixed_date_combo.setVisible(False)
                date_layout.addWidget(self.fixed_date_combo)
                
                # 添加周岁计算方式选择
                date_layout.addWidget(QLabel("计算方式："))
                self.age_type_combo = QComboBox(self)
                self.age_type_combo.addItems(["法律周岁（生日次日算满周岁）", "普通周岁（生日当天算满周岁）"])
                date_layout.addWidget(self.age_type_combo)
                
                age_layout.addWidget(self.date_widget)
                age_layout.addStretch()
                
                # 添加复选框状态改变事件
                checkbox.stateChanged.connect(lambda state: self.date_widget.setVisible(state == Qt.Checked))
                
                # 将整个布局添加到功能布局中
                widget = QWidget()
                widget.setLayout(age_layout)
                self.function_layout.addWidget(widget)
            else:
                self.function_layout.addWidget(checkbox)
            self.checkboxes.append(checkbox)

    def select_file(self):
        try:
            file_name, _ = QFileDialog.getOpenFileName(
                self,
                "选择文件",
                "",
                "Excel Files (*.xlsx *.xls);;CSV Files (*.csv)"
            )
            
            if file_name:
                try:
                    if file_name.endswith('.csv'):
                        self.df = pd.read_csv(file_name)
                    else:
                        self.df = pd.read_excel(file_name)
                    
                    self.file_label.setText(file_name.split('/')[-1])
                    
                    # 更新列选择下拉框
                    if self.column_combo:
                        self.column_combo.clear()
                        self.column_combo.addItems(self.df.columns)
                    
                    if self.date_column_combo:
                        self.date_column_combo.clear()
                        self.date_column_combo.addItems(self.df.columns)
                    
                except Exception as e:
                    QMessageBox.critical(self, '错误', f'无法读取文件：{str(e)}')
                    return
        except Exception as e:
            QMessageBox.critical(self, '错误', f'选择文件时发生错误：{str(e)}')

    def process_data(self):
        if self.df is None or self.column_combo.currentText() == '':
            QMessageBox.warning(self, '警告', '请先选择文件和数据列')
            return

        column = self.column_combo.currentText()
        data = self.df[column].astype(str)
        original_data = data.copy()

        current_tool = self.tool_combo.currentText()
        results = {}
        
        if current_tool == "身份证号码工具":
            date_type = self.date_type_combo.currentText()
            calc_dates = None
            
            if date_type == "使用数据列日期":
                date_column = self.date_column_combo.currentText()
                if date_column:
                    calc_dates = self.df[date_column]
            elif date_type == "使用固定日期":
                fixed_date = self.fixed_date_combo.currentText()
                try:
                    calc_dates = pd.Series([fixed_date] * len(self.df))
                except:
                    QMessageBox.warning(self, '警告', '固定日期格式无效，请使用YYYY-MM-DD格式')
                    return
            
            for checkbox in self.checkboxes:
                if not checkbox.isChecked():
                    continue
                    
                if checkbox.text() == '校验身份证号码':
                    results['校验身份证号码是否正确'] = original_data.apply(
                        lambda x: self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)))
                elif checkbox.text() == '计算周岁年龄':
                    # 获取周岁计算方式
                    legal_age = self.age_type_combo.currentText().startswith("法律周岁")
                    
                    if calc_dates is not None:
                        # 使用数据列日期或固定日期
                        results['周岁年龄'] = pd.DataFrame({
                            'id_card': original_data,
                            'calc_date': calc_dates
                        }).apply(
                            lambda x: self.id_processor.get_age(
                                self.id_processor.clean_id_card(x['id_card']), 
                                str(x['calc_date']) if pd.notna(x['calc_date']) else None,
                                legal_age
                            ),
                            axis=1
                        )
                    else:
                        # 使用当前日期
                        results['周岁年龄'] = original_data.apply(
                            lambda x: self.id_processor.get_age(
                                self.id_processor.clean_id_card(x),
                                None,
                                legal_age
                            )
                        )
                elif checkbox.text() == '提取性别':
                    results['性别'] = original_data.apply(
                        lambda x: self.id_processor.get_gender(self.id_processor.clean_id_card(x)) 
                        if self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)) != "位数不对" else None)
                elif checkbox.text() == '提取出生日期':
                    results['出生日期'] = original_data.apply(
                        lambda x: self.id_processor.get_birth_date(self.id_processor.clean_id_card(x)) 
                        if self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)) != "位数不对" else None)
                elif checkbox.text() == '匹配户籍地':
                    results['户籍所在地'] = original_data.apply(
                        lambda x: self.id_processor.get_area(self.id_processor.clean_id_card(x)) 
                        if self.id_processor.validate_id_card(self.id_processor.clean_id_card(x)) != "位数不对" else None)
                elif checkbox.text() == '身份证号码脱敏（中间变星号）':
                    self.df[column] = original_data.apply(lambda x: self.id_processor.mask_id_card(x))
        
        elif current_tool == "手机号码工具":
            for checkbox in self.checkboxes:
                if checkbox.isChecked():
                    if checkbox.text().startswith('手机号码归属地匹配'):
                        clean_phone_data = original_data.apply(self.phone_processor.clean_phone_number)
                        phone_info = clean_phone_data.apply(self.phone_processor.get_phone_info)
                        results['手机号码归属地'] = phone_info.apply(lambda x: x['location'] if x else None)
                        results['运营商'] = phone_info.apply(lambda x: x['operator'] if x else None)
                    elif checkbox.text() == '手机号码脱敏（中间变星号）':
                        self.df[column] = original_data.apply(lambda x: self.phone_processor.mask_phone_number(x))

        for key, value in results.items():
            self.df[key] = value

        QMessageBox.information(self, '成功', '数据处理完成！')

    def export_results(self):
        if self.df is None:
            QMessageBox.warning(self, '警告', '没有可导出的数据！')
            return

        file_name, _ = QFileDialog.getSaveFileName(
            self,
            "保存文件",
            "",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )
        
        if file_name:
            try:
                if file_name.endswith('.csv'):
                    self.df.to_csv(file_name, index=False, encoding='utf-8-sig')
                else:
                    self.df.to_excel(file_name, index=False)
                QMessageBox.information(self, '成功', '文件导出成功！')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导出文件失败：{str(e)}')

    def on_date_type_changed(self, text):
        """处理日期类型选择变化"""
        self.date_column_combo.setVisible(text == "使用数据列日期")
        self.fixed_date_combo.setVisible(text == "使用固定日期")

    def closeEvent(self, event):
        """重写关闭事件，确保正确清理资源"""
        # 清理所有组件
        if self.date_column_combo:
            self.date_column_combo.deleteLater()
        if self.fixed_date_combo:
            self.fixed_date_combo.deleteLater()
        if self.date_type_combo:
            self.date_type_combo.deleteLater()
        if self.date_widget:
            self.date_widget.deleteLater()
        if self.age_type_combo:
            self.age_type_combo.deleteLater()
        if self.column_combo:
            self.column_combo.deleteLater()
        if self.tool_combo:
            self.tool_combo.deleteLater()
        if self.function_group:
            self.function_group.deleteLater()
        
        # 调用父类的关闭事件
        super().closeEvent(event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    try:
        window = MainWindow()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        import traceback
        error_msg = f"错误时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        error_msg += f"错误信息：{str(e)}\n"
        error_msg += f"详细堆栈：\n{traceback.format_exc()}"
        
        # 获取桌面路径
        desktop = os.path.join(os.path.expanduser("~"), 'Desktop')
        log_path = os.path.join(desktop, 'error_log.txt')
        
        # 写入错误日志
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(error_msg)
        
        # 显示错误消息
        QMessageBox.critical(None, '错误', 
            f'程序发生错误：{str(e)}\n错误日志已保存到桌面：error_log.txt')
        sys.exit(1) 