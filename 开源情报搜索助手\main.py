#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
开源情报搜索助手 - 主程序
"""

import sys
import os
import time
import threading
import logging
import webbrowser
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QRadioButton, QLabel, QListWidget, QListWidgetItem,
    QTextBrowser, QProgressBar, QSplitter, QButtonGroup, QFrame,
    QMessageBox, QStatusBar, QMenu, QAction, QToolBar, QToolButton,
    QGroupBox, QScrollArea, QFileDialog, QDialog, QGridLayout, QLineEdit,
    QCheckBox, QDialogButtonBox, QSizePolicy
)
from PyQt5.QtCore import Qt, QSize, QUrl, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QFont, QDesktopServices, QColor, QPalette

# 导入自定义工具模块
from utils import get_domestic_hot_news, get_international_hot_news, HotNewsCluster, ContentFetcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='osint_assistant.log'
)
logger = logging.getLogger('OSINTAssistant')

# 样式常量
MAIN_STYLE = """
QMainWindow, QWidget {
    background-color: #f5f5f5;
    color: #333333;
}
QListWidget {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 5px;
    font-size: 12px;
    min-width: 400px;
}
QListWidget::item {
    border-bottom: 1px solid #eeeeee;
    padding: 10px 6px;
    margin: 3px 0px;
}
QListWidget::item:selected {
    background-color: #e7f3ff;
    color: #1a73e8;
}
QTextBrowser {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 10px;
    font-size: 13px;
}
QPushButton {
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #0d5bdd;
}
QPushButton:pressed {
    background-color: #0b4bbc;
}
QRadioButton {
    font-size: 13px;
    font-weight: bold;
    padding: 5px;
}
QProgressBar {
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    background-color: #f0f0f0;
    text-align: center;
}
QProgressBar::chunk {
    background-color: #4285f4;
}
QGroupBox {
    font-weight: bold;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    margin-top: 10px;
    padding-top: 15px;
}
QToolButton {
    background-color: transparent;
    border: none;
    padding: 4px;
}
QToolButton:hover {
    background-color: #e7f3ff;
    border-radius: 4px;
}
"""

class FetchWorker(QThread):
    """异步获取热点信息的工作线程"""
    
    updateProgress = pyqtSignal(int)
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    
    def __init__(self, fetch_type):
        """
        初始化获取线程
        
        Args:
            fetch_type: 获取类型，'domestic'或'international'
        """
        super().__init__()
        self.fetch_type = fetch_type
        
    def run(self):
        """执行获取任务"""
        try:
            # 模拟进度更新
            self.updateProgress.emit(10)
            
            # 获取热点数据
            if self.fetch_type == 'domestic':
                hot_items = get_domestic_hot_news()
            else:
                hot_items = get_international_hot_news()
                
            self.updateProgress.emit(50)
            
            # 使用HotNewsCluster对获取的热点进行聚类
            if hot_items:
                cluster_tool = HotNewsCluster(similarity_threshold=0.35)
                clustered_items = cluster_tool.cluster_hot_news(hot_items)
                self.updateProgress.emit(90)
                
                # 返回聚类后的热点列表
                self.finished.emit(clustered_items)
            else:
                self.error.emit("未获取到热点信息，请检查网络连接")
                
            self.updateProgress.emit(100)
            
        except Exception as e:
            logger.error(f"获取热点失败: {e}")
            self.error.emit(f"获取热点时出错: {str(e)}")

class ContentFetchWorker(QThread):
    """异步获取热点详情的工作线程"""
    
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, url):
        """
        初始化内容获取线程
        
        Args:
            url: 要获取内容的URL
        """
        super().__init__()
        self.url = url
        self.fetcher = ContentFetcher()
        
    def run(self):
        """执行获取任务"""
        try:
            # 获取详细内容
            content = self.fetcher.fetch_content(self.url)
            
            # 返回获取到的内容
            self.finished.emit(content)
            
        except Exception as e:
            logger.error(f"获取内容详情失败: {e}")
            self.error.emit(f"获取内容详情时出错: {str(e)}")

class HotItemWidget(QWidget):
    """热点项目控件"""
    
    def __init__(self, item_data, parent=None):
        super().__init__(parent)
        self.item_data = item_data
        
        # 主布局 - 增加间距
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 10, 12, 10)
        layout.setSpacing(8)
        
        # 主信息布局
        info_layout = QVBoxLayout()
        
        # 标题
        self.title_label = QLabel(item_data['title'])
        self.title_label.setWordWrap(True)  # 允许文本换行
        self.title_label.setTextFormat(Qt.RichText)  # 设置为富文本格式
        self.title_label.setStyleSheet("""
            font-weight: bold; 
            font-size: 14px; 
            padding: 4px 0px;
            line-height: 150%;
        """)
        # 确保标题能够正确换行显示，调整大小策略
        self.title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        # 设置最小高度以容纳更多文本
        self.title_label.setMinimumHeight(45)
        
        # 添加标题到信息布局
        info_layout.addWidget(self.title_label)
        
        # 聚合热点图标和标记
        is_aggregated = 'cluster_size' in item_data and item_data['cluster_size'] > 1
        
        if is_aggregated:
            # 为聚合热点添加特殊标记
            marker_layout = QHBoxLayout()
            
            # 聚合图标
            aggregate_marker = QLabel("🔥")  # 使用火焰表情作为聚合标记
            aggregate_marker.setStyleSheet("color: #FF4500; font-size: 16px;")
            marker_layout.addWidget(aggregate_marker)
            
            # 聚合标识
            aggregate_label = QLabel(f"热点聚合 [{item_data['cluster_size']}条相似内容]")
            aggregate_label.setStyleSheet("color: #FF4500; font-size: 12px; font-weight: bold;")
            marker_layout.addWidget(aggregate_label)
            
            marker_layout.addStretch()
            info_layout.addLayout(marker_layout)
            
            # 使聚合热点的背景更加突出
            self.setStyleSheet("background-color: #FFEFEF; border-radius: 5px;")
        
        # 来源和热度信息
        source_hot_layout = QHBoxLayout()
        
        # 来源
        source_label = QLabel(f"来源: {item_data['source']}")
        source_label.setStyleSheet("color: #666666; font-size: 12px;")
        source_hot_layout.addWidget(source_label)
        
        source_hot_layout.addStretch()
        
        # 热度
        if 'hot' in item_data and item_data['hot']:
            hot_label = QLabel(f"热度: {item_data['hot']}")
            hot_label.setStyleSheet("color: #E74C3C; font-size: 12px;")
            source_hot_layout.addWidget(hot_label)
        
        info_layout.addLayout(source_hot_layout)
        layout.addLayout(info_layout)
        
        # 设置悬停效果
        self.setAutoFillBackground(True)
        self.set_normal_style()
    
    def set_normal_style(self):
        """设置正常样式"""
        if 'cluster_size' in self.item_data and self.item_data['cluster_size'] > 1:
            # 聚合热点保持突出背景色
            self.setStyleSheet("background-color: #FFEFEF; border-radius: 5px;")
        else:
            self.setStyleSheet("background-color: #FFFFFF; border-radius: 5px;")
    
    def set_hover_style(self):
        """设置悬停样式"""
        if 'cluster_size' in self.item_data and self.item_data['cluster_size'] > 1:
            # 聚合热点悬停样式
            self.setStyleSheet("background-color: #FFE0E0; border-radius: 5px;")
        else:
            self.setStyleSheet("background-color: #F0F0F0; border-radius: 5px;")
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.set_hover_style()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.set_normal_style()
        super().leaveEvent(event)

class ProxySettingsDialog(QDialog):
    """代理设置对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("代理设置")
        self.setMinimumWidth(500)
        
        layout = QVBoxLayout()
        
        # 创建国内代理配置组
        domestic_group = QGroupBox("国内网站代理设置")
        domestic_layout = QGridLayout()
        
        self.domestic_enabled = QCheckBox("启用代理")
        domestic_layout.addWidget(self.domestic_enabled, 0, 0, 1, 2)
        
        domestic_layout.addWidget(QLabel("HTTP代理:"), 1, 0)
        self.domestic_http = QLineEdit()
        self.domestic_http.setPlaceholderText("例如: http://127.0.0.1:7890")
        domestic_layout.addWidget(self.domestic_http, 1, 1)
        
        domestic_layout.addWidget(QLabel("HTTPS代理:"), 2, 0)
        self.domestic_https = QLineEdit()
        self.domestic_https.setPlaceholderText("例如: http://127.0.0.1:7890")
        domestic_layout.addWidget(self.domestic_https, 2, 1)
        
        domestic_group.setLayout(domestic_layout)
        layout.addWidget(domestic_group)
        
        # 创建国际代理配置组
        international_group = QGroupBox("国际网站代理设置")
        international_layout = QGridLayout()
        
        self.international_enabled = QCheckBox("启用代理")
        international_layout.addWidget(self.international_enabled, 0, 0, 1, 2)
        
        international_layout.addWidget(QLabel("HTTP代理:"), 1, 0)
        self.international_http = QLineEdit()
        self.international_http.setPlaceholderText("例如: http://127.0.0.1:7890")
        international_layout.addWidget(self.international_http, 1, 1)
        
        international_layout.addWidget(QLabel("HTTPS代理:"), 2, 0)
        self.international_https = QLineEdit()
        self.international_https.setPlaceholderText("例如: http://127.0.0.1:7890")
        international_layout.addWidget(self.international_https, 2, 1)
        
        international_group.setLayout(international_layout)
        layout.addWidget(international_group)
        
        # 添加说明文本
        note_label = QLabel("注意：\n- 国内网站通常无需代理，如有困难连接可使用代理\n- 国际网站通常需要代理才能访问\n- 代理格式为 http://主机:端口 或 http://用户名:密码@主机:端口\n- 代理设置将自动保存，下次启动程序时会自动加载")
        note_label.setWordWrap(True)
        layout.addWidget(note_label)
        
        # 添加按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        
        # 加载当前设置
        self.load_current_settings()
    
    def load_current_settings(self):
        """加载当前代理设置"""
        from utils.spider import get_proxy_config
        config = get_proxy_config()
        
        # 设置国内代理
        domestic = config['domestic']
        self.domestic_enabled.setChecked(domestic['enabled'])
        self.domestic_http.setText(domestic['http'] or "")
        self.domestic_https.setText(domestic['https'] or "")
        
        # 设置国际代理
        international = config['international']
        self.international_enabled.setChecked(international['enabled'])
        self.international_http.setText(international['http'] or "")
        self.international_https.setText(international['https'] or "")
    
    def get_settings(self):
        """获取设置的代理配置"""
        return {
            'domestic': {
                'enabled': self.domestic_enabled.isChecked(),
                'http': self.domestic_http.text().strip() or None,
                'https': self.domestic_https.text().strip() or None
            },
            'international': {
                'enabled': self.international_enabled.isChecked(),
                'http': self.international_http.text().strip() or None,
                'https': self.international_https.text().strip() or None
            }
        }
        
    def accept(self):
        """用户点击确定按钮时的处理"""
        from utils.spider import set_proxy
        
        # 获取设置
        settings = self.get_settings()
        
        # 设置国内代理
        set_proxy(
            'domestic', 
            settings['domestic']['enabled'],
            settings['domestic']['http'],
            settings['domestic']['https']
        )
        
        # 设置国际代理
        set_proxy(
            'international', 
            settings['international']['enabled'],
            settings['international']['http'],
            settings['international']['https']
        )
        
        super().accept()

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("开源情报搜索助手")
        self.setMinimumSize(1024, 700)
        
        # 初始化界面
        self.init_ui()
        
        # 设置样式
        self.setStyleSheet(MAIN_STYLE)
        
        # 初始化变量
        self.hot_items = []
        self.fetch_worker = None
        self.content_worker = None
        
        # 默认选择国内热点
        self.domestic_radio.setChecked(True)
        
        # 显示欢迎信息
        self.display_welcome_message()
        
    def init_ui(self):
        """初始化界面"""
        # 创建中央窗口部件
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建顶部控制区
        control_layout = QHBoxLayout()
        
        # 创建选项按钮组
        option_group = QGroupBox("热点类型")
        option_layout = QHBoxLayout(option_group)
        
        # 创建选择项
        self.domestic_radio = QRadioButton("国内热点")
        self.international_radio = QRadioButton("国际热点")
        
        # 添加到按钮组
        self.option_group = QButtonGroup(self)
        self.option_group.addButton(self.domestic_radio, 1)
        self.option_group.addButton(self.international_radio, 2)
        
        # 添加到布局
        option_layout.addWidget(self.domestic_radio)
        option_layout.addWidget(self.international_radio)
        
        # 创建刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setIcon(QIcon("images/refresh.png"))
        self.refresh_button.clicked.connect(self.fetch_hot_news)
        
        # 添加到控制布局
        control_layout.addWidget(option_group)
        control_layout.addStretch()
        control_layout.addWidget(self.refresh_button)
        
        # 添加控制布局到主布局
        main_layout.addLayout(control_layout)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 创建内容分割器
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 创建左侧热点列表区域
        self.hot_list = QListWidget()
        self.hot_list.setVerticalScrollMode(QListWidget.ScrollPerPixel)
        self.hot_list.itemClicked.connect(self.on_item_clicked)
        content_splitter.addWidget(self.hot_list)
        
        # 创建右侧详情区域
        detail_widget = QWidget()
        detail_layout = QVBoxLayout(detail_widget)
        detail_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建详情内容浏览器
        self.detail_browser = QTextBrowser()
        self.detail_browser.setOpenExternalLinks(True)
        detail_layout.addWidget(self.detail_browser)
        
        # 创建详情控制区域
        detail_control_layout = QHBoxLayout()
        
        # 创建打开链接按钮
        self.open_link_button = QPushButton("在浏览器中打开")
        self.open_link_button.setIcon(QIcon("images/browser.png"))  # 需要创建图标文件
        self.open_link_button.clicked.connect(self.open_in_browser)
        self.open_link_button.setEnabled(False)
        
        # 创建复制链接按钮
        self.copy_link_button = QPushButton("复制链接")
        self.copy_link_button.setIcon(QIcon("images/copy.png"))
        self.copy_link_button.clicked.connect(self.copy_link)
        self.copy_link_button.setEnabled(False)
        
        # 添加按钮到布局
        detail_control_layout.addWidget(self.open_link_button)
        detail_control_layout.addWidget(self.copy_link_button)
        detail_control_layout.addStretch()
        
        # 添加控制布局到详情布局
        detail_layout.addLayout(detail_control_layout)
        
        # 添加详情部件到分割器
        content_splitter.addWidget(detail_widget)
        
        # 设置分割器初始比例 (40% 为热点列表, 60% 为详情区域)
        content_splitter.setSizes([400, 600])
        
        # 添加分割器到主布局
        main_layout.addWidget(content_splitter)
        
        # 创建状态栏
        self.statusBar().showMessage("就绪")
        
        # 设置窗口大小
        self.setMinimumSize(1200, 800)
        
        # 创建工具栏
        tool_bar = QToolBar("工具栏")
        self.addToolBar(tool_bar)
        
        # 创建保存按钮
        save_action = QAction(QIcon("images/save.png"), "保存", self)
        save_action.triggered.connect(self.save_content)
        tool_bar.addAction(save_action)
        
        # 创建关于按钮
        about_action = QAction(QIcon("images/about.png"), "关于", self)
        about_action.triggered.connect(self.show_about)
        tool_bar.addAction(about_action)
        
        # 创建菜单栏
        self.create_menus()
        
    def create_menus(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 设置菜单
        settings_menu = menubar.addMenu("设置")
        
        # 添加代理设置菜单项
        proxy_action = QAction("代理设置", self)
        proxy_action.triggered.connect(self.open_proxy_settings)
        settings_menu.addAction(proxy_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def display_welcome_message(self):
        """显示欢迎信息"""
        welcome_html = """
        <div style="text-align:center; margin-top:50px;">
            <h1 style="color:#1a73e8;">欢迎使用开源情报搜索助手</h1>
            <p style="font-size:14px; color:#666;">
                这是一款功能强大的热点信息聚合工具<br>
                可以帮您快速了解国内外热门话题
            </p>
            <div style="margin-top:30px;">
                <p style="font-size:14px; color:#333;">
                    <b>使用说明:</b><br>
                    1. 在上方选择"国内热点"或"国际热点"<br>
                    2. 点击"刷新"按钮获取最新热点<br>
                    3. 在左侧列表中点击感兴趣的热点<br>
                    4. 右侧将显示热点的详细内容
                </p>
            </div>
            <div style="margin-top:40px;">
                <p style="color:#888; font-size:12px;">
                    点击"刷新"按钮开始探索最新热点资讯
                </p>
            </div>
        </div>
        """
        self.detail_browser.setHtml(welcome_html)
        
    def fetch_hot_news(self):
        """获取热点新闻"""
        # 禁用刷新按钮
        self.refresh_button.setEnabled(False)
        
        # 显示进度条
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)
        
        # 清空热点列表
        self.hot_list.clear()
        self.hot_items = []
        
        # 更新状态栏
        self.statusBar().showMessage("正在获取热点信息...")
        
        # 确定获取类型
        fetch_type = 'domestic' if self.domestic_radio.isChecked() else 'international'
        
        # 创建并启动工作线程
        self.fetch_worker = FetchWorker(fetch_type)
        self.fetch_worker.updateProgress.connect(self.update_progress)
        self.fetch_worker.finished.connect(self.on_fetch_finished)
        self.fetch_worker.error.connect(self.on_fetch_error)
        self.fetch_worker.start()
        
    def update_progress(self, value):
        """更新进度条值"""
        self.progress_bar.setValue(value)
        
    def on_fetch_finished(self, hot_items):
        """获取热点完成时的处理"""
        # 保存热点数据
        self.hot_items = hot_items
        
        # 更新热点列表
        self.update_hot_list()
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 启用刷新按钮
        self.refresh_button.setEnabled(True)
        
        # 更新状态栏
        fetch_type = "国内" if self.domestic_radio.isChecked() else "国际"
        self.statusBar().showMessage(f"已获取{len(hot_items)}条{fetch_type}热点")
        
    def on_fetch_error(self, error_message):
        """获取热点出错时的处理"""
        # 显示错误信息
        QMessageBox.warning(self, "获取失败", error_message)
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 启用刷新按钮
        self.refresh_button.setEnabled(True)
        
        # 更新状态栏
        self.statusBar().showMessage("获取热点失败")
        
    def update_hot_list(self):
        """更新热点列表"""
        # 清空列表
        self.hot_list.clear()
        
        # 添加热点项
        for item_data in self.hot_items:
            # 创建自定义项组件
            item_widget = HotItemWidget(item_data)
            
            # 创建列表项
            list_item = QListWidgetItem(self.hot_list)
            # 设置足够大的尺寸以容纳内容
            size_hint = item_widget.sizeHint()
            # 确保列表项高度足够
            min_height = max(size_hint.height(), 80)
            list_item.setSizeHint(QSize(size_hint.width(), min_height))
            
            # 添加到列表
            self.hot_list.addItem(list_item)
            self.hot_list.setItemWidget(list_item, item_widget)
            
    def on_item_clicked(self, item):
        """点击热点项的处理"""
        # 获取选中项的索引
        index = self.hot_list.row(item)
        
        # 获取对应的热点数据
        if 0 <= index < len(self.hot_items):
            item_data = self.hot_items[index]
            
            # 显示加载信息
            self.detail_browser.setHtml(
                '<div style="text-align:center; margin-top:50px;">'
                '<p style="font-size:14px;">正在加载内容，请稍候...</p>'
                '</div>'
            )
            
            # 禁用链接按钮
            self.open_link_button.setEnabled(False)
            self.copy_link_button.setEnabled(False)
            
            # 获取链接
            link = item_data.get('link', '')
            
            if link:
                # 更新状态栏
                self.statusBar().showMessage(f"正在获取: {item_data['title']}")
                
                # 创建并启动内容获取线程
                self.content_worker = ContentFetchWorker(link)
                self.content_worker.finished.connect(lambda content: self.display_content(item_data, content))
                self.content_worker.error.connect(self.on_content_fetch_error)
                self.content_worker.start()
            else:
                # 无链接时显示提示
                self.display_no_link_message(item_data)
    
    def display_content(self, item_data, content):
        """显示获取到的内容"""
        # 提取数据
        title = content.get('title', item_data.get('title', ''))
        content_text = content.get('content', '')
        source_url = content.get('source_url', item_data.get('link', ''))
        images = content.get('images', [])
        
        # 构建HTML内容
        html_content = f"""
        <div style="font-family:'Microsoft YaHei', sans-serif;">
            <h2 style="color:#1a73e8; margin-bottom:15px;">{title}</h2>
            <p style="color:#666; font-size:12px; margin-bottom:15px;">
                来源: {item_data.get('source', '')}
            </p>
        """
        
        # 添加原始标题（国际热点）
        if 'original_title' in item_data:
            html_content += f"""
            <p style="color:#666; font-size:12px; margin-bottom:20px; font-style:italic;">
                原标题: {item_data['original_title']}
            </p>
            """
        
        # 添加图片区域
        if images:
            html_content += '<div style="margin:15px 0;">'
            for img_url in images:
                html_content += f'<img src="{img_url}" style="max-width:100%; margin-bottom:10px;" />'
            html_content += '</div>'
        
        # 添加内容文本
        content_text_br = content_text.replace('\n', '<br>')
        html_content += f"""
        <div style="line-height:1.6; margin-top:15px;">
            {content_text_br}
        </div>
        """
        
        # 添加原文链接
        if source_url:
            html_content += f"""
            <div style="margin-top:30px; padding-top:10px; border-top:1px solid #eee;">
                <p style="font-size:12px;">
                    原文链接: <a href="{source_url}" style="color:#1a73e8;">{source_url}</a>
                </p>
            </div>
            """
        
        html_content += "</div>"
        
        # 显示内容
        self.detail_browser.setHtml(html_content)
        
        # 保存源URL
        self.current_url = source_url
        
        # 启用链接按钮
        self.open_link_button.setEnabled(bool(source_url))
        self.copy_link_button.setEnabled(bool(source_url))
        
        # 更新状态栏
        self.statusBar().showMessage(f"已加载: {title}")
        
    def display_no_link_message(self, item_data):
        """显示无链接提示"""
        html_content = f"""
        <div style="text-align:center; margin-top:50px;">
            <h2>{item_data.get('title', '')}</h2>
            <p style="color:#666; margin-top:20px;">该热点没有可用的详情链接</p>
        </div>
        """
        self.detail_browser.setHtml(html_content)
        
        # 更新按钮状态
        self.open_link_button.setEnabled(False)
        self.copy_link_button.setEnabled(False)
        
        # 更新状态栏
        self.statusBar().showMessage("无法获取详情: 没有可用链接")
        
    def on_content_fetch_error(self, error_message):
        """获取内容出错时的处理"""
        html_content = f"""
        <div style="text-align:center; margin-top:50px;">
            <h3 style="color:#d93025;">获取内容失败</h3>
            <p style="margin-top:20px;">{error_message}</p>
        </div>
        """
        self.detail_browser.setHtml(html_content)
        
        # 更新状态栏
        self.statusBar().showMessage("内容获取失败")
        
    def open_in_browser(self):
        """在浏览器中打开链接"""
        if hasattr(self, 'current_url') and self.current_url:
            QDesktopServices.openUrl(QUrl(self.current_url))
            
    def copy_link(self):
        """复制链接到剪贴板"""
        if hasattr(self, 'current_url') and self.current_url:
            clipboard = QApplication.clipboard()
            clipboard.setText(self.current_url)
            self.statusBar().showMessage("链接已复制到剪贴板", 3000)
            
    def save_content(self):
        """保存当前内容"""
        # 获取当前内容
        html_content = self.detail_browser.toHtml()
        text_content = self.detail_browser.toPlainText()
        
        if not text_content.strip():
            QMessageBox.information(self, "保存提示", "没有可保存的内容")
            return
            
        # 打开保存对话框
        file_name, _ = QFileDialog.getSaveFileName(
            self, "保存内容", "", "HTML文件 (*.html);;文本文件 (*.txt)"
        )
        
        if not file_name:
            return
            
        try:
            with open(file_name, 'w', encoding='utf-8') as f:
                if file_name.endswith('.html'):
                    f.write(html_content)
                else:
                    f.write(text_content)
                    
            self.statusBar().showMessage(f"内容已保存到: {file_name}", 3000)
            
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存内容时出错: {str(e)}")
            
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, 
            "关于开源情报搜索助手",
            "开源情报搜索助手 v1.0\n\n"
            "一款强大的开源情报搜索工具，帮助您轻松获取国内外热点信息。\n\n"
            "支持多平台热点获取、内容聚合、相似度分析等功能。"
        )

    def open_proxy_settings(self):
        """打开代理设置对话框"""
        dialog = ProxySettingsDialog(self)
        if dialog.exec_():
            # 代理设置已在ProxySettingsDialog.accept()中保存和应用
            QMessageBox.information(self, "代理设置", "代理设置已更新并保存，将在下次搜索时生效")

def main():
    """主函数"""
    # 创建应用实例
    app = QApplication(sys.argv)
    
    # 创建窗口
    window = MainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 