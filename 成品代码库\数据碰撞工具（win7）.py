import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import re
import os
import logging
import sys
import warnings
import ctypes

warnings.filterwarnings('ignore')


# 确保能正确处理临时文件路径
if hasattr(sys, '_MEIPASS'):
    os.chdir(sys._MEIPASS)

# 设置pandas选项，避免可能的兼容性问题
pd.set_option('mode.chained_assignment', None)

def set_app_icon():
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        icon_path = os.path.join(sys._MEIPASS, '2.ico')
    else:
        icon_path = '2.ico'
    
    if os.path.exists(icon_path):
        try:
            # 设置任务栏图标
            myappid = 'tzga.datamatcher.1.0'  # 任意字符串，作为应用程序ID
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
            return icon_path
        except:
            return None
    return None

# 数据清洗函数：去除空格、标点符号等
def clean_data(text):
    if pd.isna(text):
        return ''
    # 转换为字符串
    text = str(text)
    # 提取所有数字
    cleaned_text = ''.join(filter(str.isdigit, text))
    # 去除多余的空格
    cleaned_text = cleaned_text.strip()
    return cleaned_text

# 数据匹配和计数函数
def match_data(df1, column1, df2, column2, match_mode="exact", deduplicate=False):
    logging.info(f"开始数据匹配，模式：{match_mode}，去重：{deduplicate}")
    
    try:
        # 创建数据副本，避免修改原始数据
        df1_copy = df1.copy()
        df2_copy = df2.copy()
        
        # 确保列名存在
        if column1 not in df1_copy.columns:
            raise ValueError(f"在第一个文件中未找到列：{column1}")
        if column2 not in df2_copy.columns:
            raise ValueError(f"在第二个文件中未找到列：{column2}")
        
        # 统一数据类型为字符串并处理空值
        df1_copy[column1] = df1_copy[column1].fillna('').astype(str)
        df2_copy[column2] = df2_copy[column2].fillna('').astype(str)
        
        if match_mode == "exact":
            # 精确匹配模式
            if deduplicate:
                # 去重处理
                matched_values = set(df1_copy[column1]) & set(df2_copy[column2])
                matched = pd.DataFrame({column1: list(matched_values)})
            else:
                # 不去重，保留所有匹配记录
                matched = pd.merge(
                    df1_copy[[column1]], 
                    df2_copy[[column2]], 
                    left_on=column1,
                    right_on=column2,
                    how='inner'
                )
        else:
            # 模糊匹配模式
            # 创建临时列用于清洗后的数据
            df1_temp = pd.DataFrame()
            df2_temp = pd.DataFrame()
            
            # 复制原始列并添加清洗列
            df1_temp['original'] = df1_copy[column1]
            df1_temp['cleaned'] = df1_copy[column1].apply(clean_data)
            
            df2_temp['original'] = df2_copy[column2]
            df2_temp['cleaned'] = df2_copy[column2].apply(clean_data)
            
            if deduplicate:
                # 去重模式：只保留清洗后的唯一值
                matched_values = set(df1_temp['cleaned']) & set(df2_temp['cleaned'])
                # 找出对应的原始值
                matched = df1_temp[df1_temp['cleaned'].isin(matched_values)][['original']].drop_duplicates()
                # 重命名列为原始列名
                matched = matched.rename(columns={'original': column1})
            else:
                # 不去重模式：保留所有匹配记录
                matched = pd.merge(
                    df1_temp[['original', 'cleaned']],
                    df2_temp[['original', 'cleaned']],
                    on='cleaned',
                    how='inner'
                )
                # 重命名列为原始列名
                matched = matched.rename(columns={
                    'original_x': column1,
                    'original_y': column2
                })
                # 只保留原始列
                matched = matched[[column1, column2]]
        
        # 计数
        match_count = len(matched)
        logging.info(f"匹配完成，总匹配数: {match_count}")
        
        return matched, match_count
        
    except Exception as e:
        logging.error(f"匹配过程中出错: {str(e)}")
        raise Exception(f"匹配过程中出错: {str(e)}")

# 保存结果函数
def save_results(matched_df):
    logging.info("开始保存匹配结果")
    if matched_df.empty:
        messagebox.showwarning("无匹配结果", "没有匹配到任何结果。")
        logging.warning("尝试保存空的匹配结果")
        return
    # 提供保存对话框
    file_path = filedialog.asksaveasfilename(defaultextension=".xlsx",
                                             filetypes=[("Excel files", "*.xlsx"),
                                                        ("CSV files", "*.csv")])
    if file_path:
        try:
            if file_path.endswith('.csv'):
                matched_df.to_csv(file_path, index=False, encoding='utf-8-sig')
                logging.info(f"结果已保存为 CSV 文件: {file_path}")
            else:
                matched_df.to_excel(file_path, index=False, engine='openpyxl')
                logging.info(f"结果已保存为 Excel 文件: {file_path}")
            messagebox.showinfo("保存成功", f"结果已保存到 {file_path}")
        except Exception as e:
            logging.error(f"保存文件时出错: {e}")
            messagebox.showerror("保存失败", f"保存文件时出错: {e}")

# 数据相减函数
def subtract_data(df1, column1, df2, column2, match_mode="exact", deduplicate=False):
    """
    从第一个数据集中减去第二个数据集中的数据
    """
    logging.info(f"开始数据相减，模式：{match_mode}，去重：{deduplicate}")
    
    try:
        # 创建数据副本
        df1_copy = df1.copy()
        df2_copy = df2.copy()
        
        # 确保列名存在
        if column1 not in df1_copy.columns:
            raise ValueError(f"在第一个文件中未找到列：{column1}")
        if column2 not in df2_copy.columns:
            raise ValueError(f"在第二个文件中未找到列：{column2}")
        
        # 统一数据类型为字符串并处理空值
        df1_copy[column1] = df1_copy[column1].fillna('').astype(str)
        df2_copy[column2] = df2_copy[column2].fillna('').astype(str)
        
        if match_mode == "exact":
            # 精确相减模式
            if deduplicate:
                # 去重处理
                values_to_keep = set(df1_copy[column1]) - set(df2_copy[column2])
                result = pd.DataFrame({column1: list(values_to_keep)})
            else:
                # 不去重，保留所有记录
                result = df1_copy[~df1_copy[column1].isin(df2_copy[column2])]
        else:
            # 模糊相减模式
            # 创建临时列用于清洗后的数据
            df1_temp = pd.DataFrame()
            df2_temp = pd.DataFrame()
            
            # 复制原始列并添加清洗列
            df1_temp['original'] = df1_copy[column1]
            df1_temp['cleaned'] = df1_copy[column1].apply(clean_data)
            
            df2_temp['original'] = df2_copy[column2]
            df2_temp['cleaned'] = df2_copy[column2].apply(clean_data)
            
            if deduplicate:
                # 去重模式：只保留清洗后的唯一值
                values_to_keep = set(df1_temp['cleaned']) - set(df2_temp['cleaned'])
                # 找出对应的原始值
                result = df1_temp[df1_temp['cleaned'].isin(values_to_keep)][['original']].drop_duplicates()
                # 重命名列为原始列名
                result = result.rename(columns={'original': column1})
            else:
                # 不去重模式：保留所有不匹配的记录
                result = df1_temp[~df1_temp['cleaned'].isin(df2_temp['cleaned'])][['original']]
                # 重命名列为原始列名
                result = result.rename(columns={'original': column1})
        
        # 计数
        result_count = len(result)
        logging.info(f"相减完成，剩余数量: {result_count}")
        
        return result, result_count
        
    except Exception as e:
        logging.error(f"相减过程中出错: {str(e)}")
        raise Exception(f"相减过程中出错: {str(e)}")

# 主应用类
class DataMatcherApp:
    def __init__(self, root):
        self.root = root
        self.root.title("数据匹配工具")
        self.root.geometry("750x700")
        self.root.resizable(False, False)
        
        # 设置图标
        icon_path = set_app_icon()
        if icon_path:
            self.root.iconbitmap(icon_path)
        
        # 修改全局字体和颜色
        self.style = {
            'font': ('Microsoft YaHei', 10),
            'bg_color': 'white',
            'button_bg': '#4a90e2',
            'button_fg': 'white',
            'label_fg': '#000000'
        }
        
        self.root.configure(bg=self.style['bg_color'])
        
        # 初始化变量
        self.file1_path = ""
        self.file2_path = ""
        self.df1 = None
        self.df2 = None
        self.matched_df = None
        self.match_mode = tk.StringVar(value="exact")

        # 创建界面组件
        self.create_widgets()

    def create_widgets(self):
        # 创建外层框架用于居中
        outer_frame = tk.Frame(self.root, bg=self.style['bg_color'])
        outer_frame.pack(expand=True, fill=tk.BOTH)
        
        # 创建主框架，设置固定宽度以实现居中效果
        frame = tk.Frame(outer_frame, width=650, padx=15, pady=10, bg=self.style['bg_color'])
        frame.pack(expand=True)
        frame.grid_propagate(True)
        
        # 配置grid列的权重，使内容居中
        frame.grid_columnconfigure(0, weight=1)
        frame.grid_columnconfigure(1, weight=0)

        # 修改文本输入框宽度
        entry_width = 55

        # 第一个文件上传
        label1 = tk.Label(frame, text="选择第一个文件:", font=self.style['font'], 
                bg=self.style['bg_color'], fg=self.style['label_fg'])
        label1.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0,5))

        self.file1_entry = tk.Entry(frame, width=entry_width, font=self.style['font'])
        self.file1_entry.grid(row=1, column=0, sticky='ew', padx=(0,10), pady=(0,10))

        self.file1_button = tk.Button(frame, text="浏览", width=12, 
                                    font=self.style['font'],
                                    bg=self.style['button_bg'],
                                    fg=self.style['button_fg'],
                                    relief=tk.FLAT,
                                    command=self.browse_file1)
        self.file1_button.grid(row=1, column=1, pady=(0,10))

        # 第二个文件上传
        tk.Label(frame, text="选择第二个文件:", font=self.style['font'],
                bg=self.style['bg_color'], fg=self.style['label_fg']).grid(
                row=2, column=0, columnspan=2, sticky=tk.W, pady=(0,5))

        self.file2_entry = tk.Entry(frame, width=entry_width, font=self.style['font'])
        self.file2_entry.grid(row=3, column=0, sticky='ew', padx=(0,10), pady=(0,10))

        self.file2_button = tk.Button(frame, text="浏览", width=12,
                                    font=self.style['font'],
                                    bg=self.style['button_bg'],
                                    fg=self.style['button_fg'],
                                    relief=tk.FLAT,
                                    command=self.browse_file2)
        self.file2_button.grid(row=3, column=1, pady=(0,10))

        # 修改下拉框样式
        dropdown_style = {
            'width': 52,  # 调整下拉框宽度
            'font': self.style['font'],
            'bg': 'white',
            'fg': '#333333',
            'relief': tk.FLAT,
            'highlightthickness': 1,
            'highlightbackground': '#cccccc',
            'highlightcolor': '#4a90e2',
            'activebackground': '#f5f5f5',
            'activeforeground': '#000000'
        }

        # 第一个下拉框标签
        tk.Label(frame, text="选择用于匹配的第一个文件列:", font=self.style['font'],
                bg=self.style['bg_color'], fg=self.style['label_fg']).grid(
                row=4, column=0, columnspan=2, sticky=tk.W, pady=(0,5))

        # 第一个下拉框
        self.column1_var = tk.StringVar()
        self.column1_dropdown = tk.OptionMenu(frame, self.column1_var, "")
        self.column1_dropdown.config(**dropdown_style)
        self.column1_dropdown.grid(row=5, column=0, columnspan=2, sticky='ew', pady=(0,10))

        # 第二个下拉框标签
        tk.Label(frame, text="选择用于匹配的第二个文件列:", font=self.style['font'],
                bg=self.style['bg_color'], fg=self.style['label_fg']).grid(
                row=6, column=0, columnspan=2, sticky=tk.W, pady=(0,5))

        # 第二个下拉框
        self.column2_var = tk.StringVar()
        self.column2_dropdown = tk.OptionMenu(frame, self.column2_var, "")
        self.column2_dropdown.config(**dropdown_style)
        self.column2_dropdown.grid(row=7, column=0, columnspan=2, sticky='ew', pady=(0,10))

        # 操作模式选择
        tk.Label(frame, text="选择操作模式:", font=self.style['font'],
                bg=self.style['bg_color'], fg=self.style['label_fg']).grid(
                row=8, column=0, columnspan=2, sticky=tk.W, pady=(0,5))

        # 操作模式单选按钮
        mode_frame = tk.Frame(frame, bg=self.style['bg_color'])
        mode_frame.grid(row=9, column=0, columnspan=2, sticky='ew', pady=(0,10))

        self.operation_mode = tk.StringVar(value="match")
        modes = [
            ("A∩B（数据碰撞）", "match"),
            ("A-B（第一个文件减去第二个文件）", "a_minus_b"),
            ("B-A（第二个文件减去第一个文件）", "b_minus_a")
        ]

        for i, (text, value) in enumerate(modes):
            tk.Radiobutton(mode_frame, text=text, variable=self.operation_mode,
                          value=value, font=self.style['font'],
                          bg=self.style['bg_color']).pack(anchor='w')

        # 处理模式选择
        process_frame = tk.Frame(frame, bg=self.style['bg_color'])
        process_frame.grid(row=10, column=0, columnspan=2, sticky='ew', pady=(0,10))

        tk.Label(process_frame, text="选择处理模式:", font=self.style['font'],
                bg=self.style['bg_color'], fg=self.style['label_fg']).pack(side=tk.LEFT)

        tk.Radiobutton(process_frame, text="精确处理（完全一致）",
                      variable=self.match_mode, value="exact",
                      font=self.style['font'], bg=self.style['bg_color']).pack(side=tk.LEFT, padx=20)
        tk.Radiobutton(process_frame, text="模糊处理（忽略标点空格等噪声数据）",
                      variable=self.match_mode, value="fuzzy",
                      font=self.style['font'], bg=self.style['bg_color']).pack(side=tk.LEFT)

        # 去重选项
        self.deduplicate_var = tk.BooleanVar(value=False)
        tk.Checkbutton(frame, text="去重并只显示结果列", 
                      variable=self.deduplicate_var,
                      font=self.style['font'],
                      bg=self.style['bg_color']).grid(
                      row=11, column=0, columnspan=2, sticky='w', pady=(0,10))

        # 按钮和结果区域
        button_frame = tk.Frame(frame, bg=self.style['bg_color'])
        button_frame.grid(row=12, column=0, columnspan=2, pady=(0,10))

        # 按钮样式
        button_style = {
            'font': self.style['font'],
            'bg': self.style['button_bg'],
            'fg': self.style['button_fg'],
            'relief': tk.FLAT,
            'width': 18,
            'pady': 5
        }

        # 开始按钮
        self.match_button = tk.Button(button_frame, text="开始处理",
                                    command=self.start_matching,
                                    **button_style)
        self.match_button.pack(pady=(0,5))

        # 结果标签
        self.result_label = tk.Label(button_frame, text="匹配结果将显示在弹出窗口中。",
                                   font=self.style['font'],
                                   bg=self.style['bg_color'],
                                   fg=self.style['label_fg'])
        self.result_label.pack(pady=(0,5))

        # 保存按钮
        self.save_button = tk.Button(button_frame, text="导出结果",
                                   command=self.save_results_button,
                                   **button_style)
        self.save_button.pack(pady=(0,5))
        self.save_button.config(state=tk.DISABLED)

        # 版权信息
        copyright_label = tk.Label(
            frame, 
            text="版权所有：台州市公安局 解晟",
            font=('Microsoft YaHei', 12),
            fg="#000000",
            bg=self.style['bg_color']
        )
        copyright_label.grid(row=13, column=0, columnspan=2, pady=(15,0))

        # 确保所有组件的背景色正确
        for widget in frame.winfo_children():
            if isinstance(widget, (tk.Label, tk.Radiobutton, tk.Checkbutton)):
                widget.configure(bg=self.style['bg_color'])
                if isinstance(widget, (tk.Radiobutton, tk.Checkbutton)):
                    widget.configure(activebackground=self.style['bg_color'])

    def browse_file1(self):
        file_path = filedialog.askopenfilename(filetypes=[("All files", "*.*"),
                                                         ("Excel files", "*.xlsx *.xls"),
                                                         ("CSV files", "*.csv"),
                                                         ("Text files", "*.txt")])
        if file_path:
            self.file1_path = file_path
            self.file1_entry.delete(0, tk.END)
            self.file1_entry.insert(0, file_path)
            logging.info(f"选择第一个文件: {file_path}")
            self.load_file1()

    def browse_file2(self):
        file_path = filedialog.askopenfilename(filetypes=[("All files", "*.*"),
                                                         ("Excel files", "*.xlsx *.xls"),
                                                         ("CSV files", "*.csv"),
                                                         ("Text files", "*.txt")])
        if file_path:
            self.file2_path = file_path
            self.file2_entry.delete(0, tk.END)
            self.file2_entry.insert(0, file_path)
            logging.info(f"选择第二个文件: {file_path}")
            self.load_file2()

    def load_file1(self):
        try:
            self.df1 = self.read_file(self.file1_path)
            columns = list(self.df1.columns)
            if not columns:
                raise ValueError("第一个文件中没有列。")
            
            self.column1_var.set(columns[0])
            menu = self.column1_dropdown["menu"]
            menu.delete(0, "end")
            
            # 添加菜单项时配置样式
            for col in columns:
                menu.add_command(
                    label=col,
                    command=lambda value=col: self.column1_var.set(value),
                    font=self.style['font'],
                    background='white',
                    activebackground='#e6f0fa',
                    activeforeground='#000000'
                )
            
            logging.info(f"加载第一个文件的列: {columns}")
        except Exception as e:
            logging.error(f"无法读取第一个文件: {e}")
            messagebox.showerror("文件读取错误", f"无法读取第一个文件: {e}")

    def load_file2(self):
        try:
            self.df2 = self.read_file(self.file2_path)
            columns = list(self.df2.columns)
            if not columns:
                raise ValueError("第二个文件中没有列。")
            
            self.column2_var.set(columns[0])
            menu = self.column2_dropdown["menu"]
            menu.delete(0, "end")
            
            # 添加菜单项时配置样式
            for col in columns:
                menu.add_command(
                    label=col,
                    command=lambda value=col: self.column2_var.set(value),
                    font=self.style['font'],
                    background='white',
                    activebackground='#e6f0fa',
                    activeforeground='#000000'
                )
            
            logging.info(f"加载第二个文件的列: {columns}")
        except Exception as e:
            logging.error(f"无法读取第二个文件: {e}")
            messagebox.showerror("文件读取错误", f"无法读取第二个文件: {e}")

    def read_file(self, file_path):
        ext = os.path.splitext(file_path)[1].lower()
        logging.debug(f"读取文件: {file_path}, 扩展名: {ext}")
        
        # 扩展编码列表
        encodings = [
            'utf-8', 'gbk', 'gb2312', 'gb18030', 'big5', 
            'utf-16', 'utf-16le', 'utf-16be',
            'ascii', 'latin1', 'iso-8859-1',
            'cp936', 'cp950', 'cp1252'
        ]
        
        if ext in ['.xlsx', '.xls']:
            engines = {
                '.xlsx': 'openpyxl',
                '.xls': 'xlrd'
            }
            engine = engines.get(ext, 'openpyxl')
            return pd.read_excel(file_path, engine=engine)
        elif ext == '.csv':
            # 尝试不同的编码方式读取CSV文件
            last_error = None
            for encoding in encodings:
                try:
                    # 尝试先读取文件的一小部分来检测编码
                    with open(file_path, 'rb') as f:
                        raw = f.read(1024)
                        if raw.startswith(b'\xff\xfe') or raw.startswith(b'\xfe\xff'):
                            # 检测到 UTF-16 码
                            return pd.read_csv(file_path, encoding='utf-16')
                    
                    # 尝试完整读取文件
                    df = pd.read_csv(
                        file_path, 
                        encoding=encoding,
                        dtype=str  # 将所有列都作为字符串读取
                    )
                    print(f"成功使用 {encoding} 编码读取文件")
                    return df
                except UnicodeDecodeError as e:
                    last_error = e
                    continue
                except Exception as e:
                    print(f"使用 {encoding} 编码读取文件时出错：{str(e)}")
                    last_error = e
                    continue
            
            # 如果所有编码都失败了，尝试使用二进制方式读取
            try:
                with open(file_path, 'rb') as f:
                    content = f.read()
                    # 尝试检测编码
                    import chardet
                    detected = chardet.detect(content)
                    if detected['encoding']:
                        return pd.read_csv(file_path, encoding=detected['encoding'])
            except Exception as e:
                last_error = e
            
            raise ValueError(f"无法使用已知编码方式读取文件，最后一次错误: {str(last_error)}")
        
        elif ext == '.txt':
            # 对txt文件使用相同的编码处理方式
            for encoding in encodings:
                try:
                    return pd.read_csv(file_path, delimiter='\t', encoding=encoding)
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    print(f"使用 {encoding} 编码读取文件时出错：{str(e)}")
                    continue
            raise ValueError(f"无法使用已知编码方式读取文件，请确保文件编码正确")
        else:
            raise ValueError("不支持的文件格式")

    def start_matching(self):
        logging.info("点击开始处理按钮")
        if self.df1 is None or self.df2 is None or self.df1.empty or self.df2.empty:
            messagebox.showwarning("文件未选择", "请确保已选择并加载两个文件。")
            logging.warning("文件未选择或加载")
            return
        
        col1 = self.column1_var.get()
        col2 = self.column2_var.get()
        if not col1 or not col2:
            messagebox.showwarning("列未选择", "请确保已选择用于处理的两列。")
            logging.warning("处理列未选择")
            return
        
        # 获取操作模式和处理模式
        operation_mode = self.operation_mode.get()
        match_mode = self.match_mode.get()
        deduplicate = self.deduplicate_var.get()
        
        try:
            if operation_mode == "match":
                result_df, count = match_data(self.df1, col1, self.df2, col2, 
                                            match_mode=match_mode, 
                                            deduplicate=deduplicate)
                operation_name = "匹配"
            elif operation_mode == "a_minus_b":
                result_df, count = subtract_data(self.df1, col1, self.df2, col2,
                                               match_mode=match_mode,
                                               deduplicate=deduplicate)
                operation_name = "A-B相减"
            else:  # b_minus_a
                result_df, count = subtract_data(self.df2, col2, self.df1, col1,
                                               match_mode=match_mode,
                                               deduplicate=deduplicate)
                operation_name = "B-A相减"
            
            if count > 0:
                # 显示结果
                result_window = tk.Toplevel(self.root)
                mode_text = "精确" if match_mode == "exact" else "模糊"
                dedup_text = "（去重）" if deduplicate else ""
                result_window.title(f"{operation_name}{dedup_text}（{mode_text}）结果")
                
                # 使用文本框展示
                text = tk.Text(result_window, wrap='none')
                text.pack(expand=True, fill='both')
                
                # 插入DataFrame内容
                text.insert(tk.END, result_df.to_string(index=False, max_rows=1000))
                
                # 添加滚动条
                scrollbar_y = tk.Scrollbar(result_window, command=text.yview)
                scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
                text.configure(yscrollcommand=scrollbar_y.set)
                
                scrollbar_x = tk.Scrollbar(result_window, command=text.xview, orient='horizontal')
                scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
                text.configure(xscrollcommand=scrollbar_x.set)
                
                # 显示计数
                count_text = "匹配总数" if operation_mode == "match" else "剩余数量"
                tk.Label(result_window, text=f"{count_text}: {count}").pack(pady=5)
                
                # 保存按钮可用
                self.matched_df = result_df
                self.save_button.config(state=tk.NORMAL)
            else:
                messagebox.showinfo("无结果", "没有找到任何结果。")
                self.save_button.config(state=tk.DISABLED)
                
        except Exception as e:
            logging.error(f"处理过程中出现错误: {e}")
            messagebox.showerror("处理错误", f"处理过程中出现错误: {e}")

    def save_results_button(self):
        if hasattr(self, 'matched_df') and self.matched_df is not None and not self.matched_df.empty:
            save_results(self.matched_df)
        else:
            messagebox.showwarning("无数据", "没有匹配结果可保存。")
            logging.warning("尝试保存无数据的匹配结果")

# 运行应用程序
if __name__ == "__main__":
    try:
        logging.info("应用程序启动")
        root = tk.Tk()
        app = DataMatcherApp(root)
        root.mainloop()
        logging.info("应用程序正常退出")
    except Exception as e:
        logging.critical(f"应用程序崩溃: {e}")
