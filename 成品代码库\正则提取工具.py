import sys
import os
import platform
import re
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from docx import Document
import warnings
warnings.filterwarnings('ignore')  # 忽略警告信息
from ttkthemes import ThemedTk
import tkinter.font as tkfont
from PIL import Image, ImageTk
import base64

def check_system_compatibility():
    """检查系统兼容性"""
    try:
        system = platform.system()
        if system != 'Windows':
            return False, f"当前系统为 {system}，本程序仅支持 Windows 系统"
        
        win_version = platform.win32_ver()[0]
        if win_version not in ['7', '8', '8.1', '10', '11']:
            return False, f"当前 Windows 版本 ({win_version}) 可能不受支持"
            
        return True, "系统兼容性检查通过"
    except Exception as e:
        return False, f"系统检查失败：{str(e)}"

class RegexPatterns:
    """正则表达式模式类"""
    def __init__(self):
        # 加载地名数据
        self.city_names = self.load_location_names('地市州名称.xlsx')
        self.district_names = self.load_location_names('县市区名称.xlsx')
        
        # 构建地名正则模式
        city_pattern = '|'.join(self.city_names)
        district_pattern = '|'.join(self.district_names)
        
        self.patterns = {
            # 手机号码：11位数字，以1开头，第二位3-9
            '手机号码': r'(?<!\d)1[3-9]\d{9}(?!\d)',
            
            # 身份证号码：18位，最后一位可能是X
            '身份证号码': r'(?<!\d)[1-9][\s\-]*\d[\s\-]*\d[\s\-]*\d[\s\-]*\d[\s\-]*\d(?:19|20)[\s\-]*\d[\s\-]*\d(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dXx](?!\d)',
            
            # 邮政编码/警号：6位数字
            '邮政编码/警号': r'(?<!\d)\d[\s\-]*\d[\s\-]*\d[\s\-]*\d[\s\-]*\d[\s\-]*\d(?!\d)',
            
            # QQ号码：5-11位数字，第一位不能为0
            'QQ号码': r'(?<!\d)[1-9][\s\-]*[0-9][\s\-]*[0-9][\s\-]*[0-9](?:[\s\-]*[0-9]){1,7}(?!\d)',
            
            # 邮箱地址：更严格的匹配
            '邮箱地址': r'(?:[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
            
            # 网址：更严格的匹配
            '网址': r'https?://(?:[\w-](?:[\w-]{0,61}[\w-])?\.)+[a-zA-Z]{2,}(?:/[^\s]*)?',
            
            # 日期：标准格式和中文格式
            '日期': r'(?:(?:19|20)\d{2}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)|(?:\d{1,2}[-/月]\d{1,2}[日](?:19|20)\d{2}年?)',
            
            # 时间：支持中文格式和标准格式
            '时间': r'(?:' + '|'.join([
                # 标准日期时间格式 (2023-01-01 10:23:15)
                r'(?:(?:19|20)\d{2}[-/年]\d{1,2}[-/月]\d{1,2}[日]?\s*(?:[01]?\d|2[0-3])(?::[0-5]\d){1,2})',
                # 文日期时间格式 (2023年01月01日 10时23分15秒)
                r'(?:(?:19|20)\d{2}[-/年]\d{1,2}[-/月]\d{1,2}[日]?\s*(?:[01]?\d|2[0-3])时(?:[0-5]\d分)?(?:[0-5]\d秒)?)',
                # 标准时间格式 (10:23:15 或 10:23)
                r'(?:[01]?\d|2[0-3])(?::[0-5]\d){1,2}',
                # 中文时间格式 (10时23分15秒 或 10时23分 或 10时)
                r'(?:[01]?\d|2[0-3])时(?:[0-5]\d分)?(?:[0-5]\d秒)?'
            ]) + r')',
            
            # 省份：完整的省份名称匹配
            '省份': r'(?:北京|天津|上海|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆)(?:省|自治区|市)?',
            
            # 地址：使用加载的地名数据进行匹配
            '地址': r'(?:'
                   # 省级地址
                   r'(?:(?:北京|天津|上海|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆)(?:省|自治区|市)?)'
                   r'(?:[^，。；\s()（）]*?(?:' + city_pattern + r'|市|区|县|旗|镇|街道|路|号|室|村|幢)[^，。；\s()（）]*)*|'
                   # 直接以市级地名开头的地址
                   r'(?:(?:' + city_pattern + r')(?:[^，。；\s()（）]*?(?:' + district_pattern + r'|区|县|旗|镇|街道|路|号|室|村|幢)[^，。；\s()（）]*)*)|'
                   # 直接以区县级地名开头的地址
                   r'(?:(?:' + district_pattern + r')(?:[^，。；\s()（）]*?(?:镇|街道|路|号|室|村|幢)[^，。；\s()（）]*)*)'
                   r')',

            # 车牌号：支持新能源车牌、特殊车牌等
            '车牌号': r'(?<![A-Z0-9])[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳](?![A-Z0-9])'
        }
        
        # 添加后处理函数
        self.post_processors = {
            '地址': self._process_address,
            '手机号码': self._process_numbers,
            '身份证号码': self._process_numbers,
            '邮政编码/警号': self._process_numbers,
            'QQ号码': self._process_numbers,
            '车牌号': self._process_plate_number  # 添加车牌号的后处理函数
        }
    
    def _process_numbers(self, text):
        """处理数字格式，去除空格和符号"""
        # 保留数字和大写X
        return ''.join(c for c in text if c.isdigit() or c in 'Xx')
    
    def _process_address(self, address):
        """处理地址中的括号"""
        # 处理中文括号
        while '（' in address and '）' in address:
            start = address.find('（')
            end = address.find('）')
            if start < end:
                address = address[:start] + address[end+1:]
            else:
                break
                
        # 处理英文括号
        while '(' in address and ')' in address:
            start = address.find('(')
            end = address.find(')')
            if start < end:
                address = address[:start] + address[end+1:]
            else:
                break
        
        return address.strip()
    
    def _process_plate_number(self,text):
        """处理车牌号格式"""
        # 移除空格和破折号等分隔符
        return ''.join(text.split())

    def load_location_names(self, filename):
        """从Excel文件加载地名数据"""
        try:
            # 获取资源文件路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe
                base_path = sys._MEIPASS
            else:
                # 如果是python脚本
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            file_path = os.path.join(base_path, filename)
            
            if os.path.exists(file_path):
                df = pd.read_excel(file_path)
                if not df.empty and len(df.columns) > 0:
                    # 获取第一列的所有非空值，并去除可能的空格
                    names = [str(name).strip() for name in df.iloc[:, 0].dropna()]
                    # 按长度降序排序，确保先匹配较长的地名
                    names.sort(key=len, reverse=True)
                    # 转义特殊字符
                    names = [re.escape(name) for name in names]
                    return names
            return []
        except Exception as e:
            print(f"加载地名数据失败：{str(e)}")
            return []

class FileHandler:
    """文件处理类"""
    @staticmethod
    def read_excel_file(file_path, sheet_name=None):
        """专门处理Excel文件的方法"""
        try:
            # 尝试用openpyxl读取
            import openpyxl
            wb = openpyxl.load_workbook(file_path)
            
            # 如果没有指定sheet，使用活动sheet
            if sheet_name is None or sheet_name not in wb.sheetnames:
                ws = wb.active
            else:
                ws = wb[sheet_name]
            
            data = []
            headers = []
            for i, row in enumerate(ws.rows):
                if i == 0:  # 第一行作为表头
                    headers = [str(cell.value) if cell.value is not None else f"Column{i+1}" 
                             for i, cell in enumerate(row)]
                else:
                    data.append([cell.value for cell in row])
            df = pd.DataFrame(data, columns=headers)
            return df
        except Exception as e1:
            try:
                # 如果openpyxl失败，尝试使用xlrd
                import xlrd
                wb = xlrd.open_workbook(file_path)
                
                # 如果没有指定sheet，使用第一个sheet
                if sheet_name is None:
                    ws = wb.sheet_by_index(0)
                else:
                    try:
                        ws = wb.sheet_by_name(sheet_name)
                    except xlrd.XLRDError:
                        ws = wb.sheet_by_index(0)
                        
                headers = [str(ws.cell_value(0, i)) if ws.cell_value(0, i) 
                          else f"Column{i+1}" for i in range(ws.ncols)]
                data = []
                for row_idx in range(1, ws.nrows):
                    row = [ws.cell_value(row_idx, col_idx) 
                          for col_idx in range(ws.ncols)]
                    data.append(row)
                df = pd.DataFrame(data, columns=headers)
                return df
            except Exception as e2:
                raise Exception(f"无法读取Excel文件。\nopenpyxl错误：{str(e1)}\nxlrd错误：{str(e2)}")

    @staticmethod
    def read_file(file_path):
        ext = os.path.splitext(file_path)[1].lower()
        try:
            if ext in ['.xlsx', '.xls']:
                return FileHandler.read_excel_file(file_path)
            elif ext == '.csv':
                return pd.read_csv(file_path, encoding='utf-8')
            elif ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif ext in ['.doc', '.docx']:
                try:
                    from docx import Document
                except ImportError:
                    raise ImportError("请先安装 python-docx 包：pip install python-docx")
                doc = Document(file_path)
                return '\n'.join([paragraph.text for paragraph in doc.paragraphs])
            else:
                raise ValueError(f"不支持的文件格式：{ext}")
        except Exception as e:
            raise Exception(f"读取文件失败：{str(e)}")

    @staticmethod
    def save_results(results, output_path, mode='full'):
        """保存提取结果"""
        try:
            ext = os.path.splitext(output_path)[1].lower()
            if isinstance(results, pd.DataFrame):  #按列逐行提取模式
                if ext == '.txt':
                    # 为txt格式添加更好的格式化
                    with open(output_path, 'w', encoding='utf-8') as f:
                        # 写入原始数据
                        f.write("=== 原始数据 ===\n")
                        original_columns = [col for col in results.columns if not col.startswith('提取结果_')]
                        for col in original_columns:
                            f.write(f"\n【{col}】\n")
                            for item in results[col]:
                                if pd.notna(item) and str(item).strip():
                                    f.write(f"{item}\n")
                        
                        # 写入提取结果
                        f.write("\n=== 提取结果 ===\n")
                        result_columns = [col for col in results.columns if col.startswith('提取结果_')]
                        for col in result_columns:
                            pattern_name = col.replace('提取结果_', '')
                            f.write(f"\n【{pattern_name}】\n")
                            for item in results[col]:
                                if pd.notna(item) and str(item).strip():
                                    f.write(f"{item}\n")
                elif ext == '.csv':
                    results.to_csv(output_path, index=False, encoding='utf-8-sig')
                else:  # Excel
                    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                        results.to_excel(writer, index=False)
                        
                        # 优化Excel格式
                        workbook = writer.book
                        worksheet = writer.sheets['Sheet1']
                        
                        # 调整列宽
                        for column in worksheet.columns:
                            max_length = 0
                            column = [cell for cell in column]
                            for cell in column:
                                try:
                                    if len(str(cell.value)) > max_length:
                                        max_length = len(str(cell.value))
                                except:
                                    pass
                            adjusted_width = (max_length + 2)
                            worksheet.column_dimensions[column[0].column_letter].width = min(adjusted_width, 50)
            else:  # 全量提取模式
                # 确保所有列表长度一致
                max_length = max(len(v) for v in results.values()) if results else 0
                normalized_results = {k: v + [''] * (max_length - len(v)) for k, v in results.items()}
                
                if ext == '.txt':
                    with open(output_path, 'w', encoding='utf-8') as f:
                        for col in normalized_results:
                            f.write(f"=== {col} ===\n")
                            for item in normalized_results[col]:
                                if item:  # 只写入非空值
                                    f.write(f"{item}\n")
                            f.write("\n")
                elif ext == '.csv':
                    df = pd.DataFrame(normalized_results)
                    df.to_csv(output_path, index=False, encoding='utf-8-sig')
                else:  # Excel
                    df = pd.DataFrame(normalized_results)
                    if df.empty:  # 如果结果为空，添加一个空行
                        df = pd.DataFrame([''], columns=['无匹配结果'])
                    
                    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                        df.to_excel(writer, index=False)
                        
                        # 优化Excel格式
                        workbook = writer.book
                        worksheet = writer.sheets['Sheet1']
                        
                        # 调整列宽
                        for column in worksheet.columns:
                            max_length = 0
                            column = [cell for cell in column]
                            for cell in column:
                                try:
                                    if len(str(cell.value)) > max_length:
                                        max_length = len(str(cell.value))
                                except:
                                    pass
                            adjusted_width = (max_length + 2)
                            worksheet.column_dimensions[column[0].column_letter].width = min(adjusted_width, 50)
        except Exception as e:
            raise Exception(f"保存文件失败：{str(e)}")

class RegexExtractor:
    """正则提取器类"""
    def __init__(self):
        self.patterns = RegexPatterns()  # 不再直接访问 patterns 属性
        self.file_handler = FileHandler()

    def extract_full(self, file_path, selected_patterns, remove_duplicates=True):
        """全量提取模式"""
        content = self.file_handler.read_file(file_path)
        results = {name: [] for name in selected_patterns}
        
        if isinstance(content, pd.DataFrame):
            for column in content.columns:
                for pattern_name in selected_patterns:
                    matches = []
                    for cell in content[column].astype(str):
                        matches.extend(re.findall(self.patterns.patterns[pattern_name], cell))
                    
                    # 应用后处理
                    if pattern_name in self.patterns.post_processors:
                        matches = [self.patterns.post_processors[pattern_name](m) for m in matches]
                        # 去除空字符串
                        matches = [m for m in matches if m]
                    
                    if remove_duplicates:
                        results[pattern_name].extend(list(dict.fromkeys(matches)))
                    else:
                        results[pattern_name].extend(matches)
        else:
            for pattern_name in selected_patterns:
                matches = re.findall(self.patterns.patterns[pattern_name], str(content))
                
                # 应用后处理
                if pattern_name in self.patterns.post_processors:
                    matches = [self.patterns.post_processors[pattern_name](m) for m in matches]
                    # 去除空字符串
                    matches = [m for m in matches if m]
                
                if remove_duplicates:
                    results[pattern_name] = list(dict.fromkeys(matches))
                else:
                    results[pattern_name] = matches
        
        return results

    def extract_by_row(self, file_path, column_name, selected_patterns, remove_duplicates=True):
        """按列逐行提取模式"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在：{file_path}")
            
            try:
                df = self.file_handler.read_file(file_path)
                if not isinstance(df, pd.DataFrame):
                    raise ValueError("文件格式不支持按列逐行提取模式")
            except Exception as read_error:
                raise Exception(f"文件读取失败：{str(read_error)}")
            
            if column_name not in df.columns:
                raise ValueError(f"找不到指定的列：{column_name}")
            
            for pattern_name in selected_patterns:
                def extract_pattern(cell):
                    matches = re.findall(self.patterns.patterns[pattern_name], str(cell))
                    if matches:
                        # 应用后处理
                        if pattern_name in self.patterns.post_processors:
                            matches = [self.patterns.post_processors[pattern_name](m) for m in matches]
                            # 去除空字符串
                            matches = [m for m in matches if m]
                            
                        if remove_duplicates:
                            matches = list(dict.fromkeys(matches))
                        return '、'.join(matches)
                    return ''
                
                new_column_name = f"提取结果_{pattern_name}"
                df[new_column_name] = df[column_name].apply(extract_pattern)
            
            return df
        except Exception as e:
            raise Exception(f"处理文件失败：{str(e)}")

    def extract_by_column(self, file_path, column_name, selected_patterns, remove_duplicates=True):
        """按列全量提取模式"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在：{file_path}")
            
            try:
                df = self.file_handler.read_file(file_path)
                if not isinstance(df, pd.DataFrame):
                    raise ValueError("文件格式不支持按列提取模式")
            except Exception as read_error:
                raise Exception(f"文件读取失败：{str(read_error)}")
            
            if column_name not in df.columns:
                raise ValueError(f"找不到指定的列：{column_name}")
            
            # 将选中列的所有内容合并成一个字符串
            content = '\n'.join(df[column_name].astype(str).values)
            
            # 使用全量提取的逻辑处理合并后的内容
            results = {name: [] for name in selected_patterns}
            
            for pattern_name in selected_patterns:
                matches = re.findall(self.patterns.patterns[pattern_name], content)
                
                # 应用后处理
                if pattern_name in self.patterns.post_processors:
                    matches = [self.patterns.post_processors[pattern_name](m) for m in matches]
                    # 去除空字符串
                    matches = [m for m in matches if m]
                
                if remove_duplicates:
                    results[pattern_name] = list(dict.fromkeys(matches))
                else:
                    results[pattern_name] = matches
            
            return results
            
        except Exception as e:
            raise Exception(f"处理文件失败：{str(e)}")

class GUI:
    """图形界面类"""
    def __init__(self):
        # 检查系统兼容性
        is_compatible, message = check_system_compatibility()
        if not is_compatible:
            tk.messagebox.showerror("系统兼容性错误", message)
            sys.exit(1)
            
        # 使用ThemedTk替代普通的Tk
        self.root = ThemedTk(theme="arc")
        self.root.title("正则提取工具")
        
        # 设置程序DPI感知
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass
        
        # 设置窗口大小和位置
        window_width = 800
        window_height = 700
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 设置字体和样式
        self.default_font = tkfont.nametofont("TkDefaultFont")
        self.default_font.configure(size=10)
        self.title_font = tkfont.Font(family="Microsoft YaHei UI", size=11, weight="bold")
        
        # 设置全局样式
        style = ttk.Style()
        # 设置按钮样式
        style.configure('TButton', padding=5, font=('Microsoft YaHei UI', 10))
        # 设置标签框样式
        style.configure('TLabelframe', padding=8)
        style.configure('TLabelframe.Label', font=self.title_font)
        # 设置大按钮样式
        style.configure('Big.TButton', padding=8, font=('Microsoft YaHei UI', 11))
        # 设置单选按钮样式
        style.configure('TRadiobutton', font=('Microsoft YaHei UI', 10))
        # 设置复选框样式
        style.configure('TCheckbutton', font=('Microsoft YaHei UI', 10))
        # 设置标签样式
        style.configure('TLabel', font=('Microsoft YaHei UI', 10))
        # 设置下拉框样式
        style.configure('TCombobox', font=('Microsoft YaHei UI', 10))
        
        # 设置错误处理
        self.root.report_callback_exception = self.show_error
        
        self.extractor = RegexExtractor()
        self.setup_gui()
        
        # 设置窗口图标
        try:
            # 获取图标文件路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe
                application_path = sys._MEIPASS
            else:
                # 如果是python脚本
                application_path = os.path.dirname(os.path.abspath(__file__))
            
            icon_path = os.path.join(application_path, '6.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass  # 如果设置图标失败，继续运行程序
        
    def show_error(self, exc_type, exc_value, exc_traceback):
        """统一错误处理"""
        error_message = str(exc_value)
        tk.messagebox.showerror("错误", error_message)
        
    def setup_gui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建一个容器frame来实现居中效果
        center_container = ttk.Frame(main_frame)
        center_container.pack(expand=True, fill="both", padx=15)

        # 文件操作框架
        self.file_frame = ttk.LabelFrame(center_container, text="文件操作")
        self.file_frame.pack(pady=(5,3),fill="x")

        # 文件操作按钮布局
        file_buttons_frame = ttk.Frame(self.file_frame)
        file_buttons_frame.pack(pady=5, padx=5, fill="x")

        # 左右按钮布局
        button_container = ttk.Frame(file_buttons_frame)
        button_container.pack(expand=True, fill="x")

        # 左侧按钮
        left_frame = ttk.Frame(button_container)
        left_frame.pack(side=tk.LEFT, expand=True, fill="x", padx=2)
        ttk.Button(left_frame, text="选择输入文件", 
                  command=self.select_input_file).pack(fill="x", ipady=2)

        # 右侧按钮
        right_frame = ttk.Frame(button_container)
        right_frame.pack(side=tk.LEFT, expand=True, fill="x", padx=2)
        ttk.Button(right_frame, text="选择输出位置", 
                  command=self.select_output_file).pack(fill="x", ipady=2)

        # 导出格式选择
        format_frame = ttk.Frame(self.file_frame)
        format_frame.pack(pady=3, padx=5, fill="x")

        format_label = ttk.Label(format_frame, text="导出格式：")
        format_label.pack(side=tk.LEFT, padx=5)

        self.output_format_var = tk.StringVar(value='.xlsx')
        for text, value in [("Excel", '.xlsx'), ("CSV", '.csv'), ("TXT", '.txt')]:
            rb = ttk.Radiobutton(format_frame, text=text, variable=self.output_format_var,
                               value=value)
            rb.pack(side=tk.LEFT, padx=8)

        # 去重选项
        self.remove_duplicates_var = tk.BooleanVar(value=True)
        duplicate_check = ttk.Checkbutton(self.file_frame, text="去除重复结果", 
                                        variable=self.remove_duplicates_var)
        duplicate_check.pack(pady=2, padx=5, anchor="w")

        # 模式选择和处理列框架容器
        mode_column_container = ttk.Frame(center_container)
        mode_column_container.pack(pady=3, fill="x")

        # 模式选择框架
        mode_frame = ttk.LabelFrame(mode_column_container, text="提取模式")
        mode_frame.pack(side=tk.LEFT, fill="x", expand=True, padx=(0, 2))

        modes_container = ttk.Frame(mode_frame)
        modes_container.pack(pady=5, padx=5, fill="x")

        self.mode_var = tk.StringVar(value="full")
        for text, value in [("全量提取", "full"), 
                           ("按列提取（逐行展示）", "row"),
                           ("按列提取（全量展示）", "column")]:
            rb = ttk.Radiobutton(modes_container, text=text, variable=self.mode_var,
                               value=value, command=self.toggle_mode)
            rb.pack(side=tk.LEFT, padx=15)

        # 列选择框架
        self.column_frame = ttk.LabelFrame(mode_column_container, text="处理列")
        # 初始状态不显示列选择框架，因为默认是全量提取模式
        # self.column_frame.pack(side=tk.LEFT, fill="x", expand=True, padx=(2, 0))

        self.column_var = tk.StringVar()
        self.column_combo = ttk.Combobox(self.column_frame, textvariable=self.column_var,
                                       state='readonly')
        self.column_combo.pack(pady=5, padx=5, fill="x")

        # 提取项选择框架
        self.pattern_frame = ttk.LabelFrame(center_container, text="选择要提取的内容")
        self.pattern_frame.pack(pady=3, fill="both", expand=True)

        # 创建网格布局
        pattern_container = ttk.Frame(self.pattern_frame)
        pattern_container.pack(pady=5, padx=5, fill="both", expand=True)

        self.pattern_vars = {}
        patterns_per_row = 4
        for i, pattern_name in enumerate(self.extractor.patterns.patterns.keys()):
            var = tk.BooleanVar()
            row = i // patterns_per_row
            col = i % patterns_per_row
            check = ttk.Checkbutton(pattern_container, text=pattern_name, 
                                  variable=var)
            check.grid(row=row, column=col, padx=8, pady=2, sticky="w")
            self.pattern_vars[pattern_name] = var

        # 开始按钮
        start_button = ttk.Button(center_container, text="开始提取", 
                                command=self.start_extraction,
                                style='Big.TButton')
        start_button.pack(pady=(5,8), fill="x")

        # 使用说明
        help_frame = ttk.LabelFrame(center_container, text="使用说明")
        help_frame.pack(pady=(3,8), fill="x")

        help_text = """1. 选择输入文件和输出位置
2. 选择提取模式
3. 选择要提取的内容
4. 点击"开始提取"按钮"""

        help_label = ttk.Label(help_frame, text=help_text, justify=tk.LEFT)
        help_label.pack(pady=3, padx=5)

        # 版权信息
        copyright_frame = ttk.Frame(center_container)
        copyright_frame.pack(pady=(10,12), fill="x")

        copyright_text = "版权所有 © 台州市公安局 解晟\nAll rights reserved."
        copyright_label = ttk.Label(
            copyright_frame, 
            text=copyright_text,
            justify=tk.CENTER,
            font=('Microsoft YaHei UI', 12),
            foreground='black'
        )
        copyright_label.pack(pady=8)

        # 在文件操作框架中添加sheet选择
        sheet_frame = ttk.Frame(self.file_frame)
        sheet_frame.pack(pady=3, padx=5, fill="x")
        
        sheet_label = ttk.Label(sheet_frame, text="工作表：")
        sheet_label.pack(side=tk.LEFT, padx=5)
        
        self.sheet_var = tk.StringVar()
        self.sheet_combo = ttk.Combobox(sheet_frame, textvariable=self.sheet_var,
                                      state='readonly', width=30)
        self.sheet_combo.pack(side=tk.LEFT, padx=5, fill="x", expand=True)
        
        # 添加sheet选择变更事件处理
        self.sheet_combo.bind('<<ComboboxSelected>>', self.on_sheet_selected)

    def toggle_mode(self):
        """切换提取模式时的处理"""
        if self.mode_var.get() in ["row", "column"]:
            self.column_frame.pack(side=tk.LEFT, fill="x", expand=True, padx=(2, 0))
        else:
            self.column_frame.pack_forget()

    def select_input_file(self):
        file_types = [
            ('所有支持的文件', '*.xlsx;*.xls;*.csv;*.txt;*.doc;*.docx'),
            ('Excel文件', '*.xlsx;*.xls'),
            ('CSV文件', '*.csv'),
            ('文本文件', '*.txt'),
            ('Word文件', '*.doc;*.docx')
        ]
        self.input_file = filedialog.askopenfilename(filetypes=file_types)
        if self.input_file:
            # 更新sheet选择下拉框
            sheets = self.get_excel_sheets(self.input_file)
            if sheets:
                self.sheet_combo['values'] = sheets
                self.sheet_combo.set(sheets[0])
                self.sheet_combo.pack(side=tk.LEFT, padx=5, fill="x", expand=True)
            else:
                self.sheet_combo.set('')
                self.sheet_combo.pack_forget()
            
            # 更新列选择下拉框
            try:
                if self.input_file.endswith(('.xlsx', '.xls')):
                    df = self.extractor.file_handler.read_excel_file(
                        self.input_file, 
                        self.sheet_var.get()
                    )
                else:
                    df = pd.read_csv(self.input_file) if self.input_file.endswith('.csv') \
                        else pd.DataFrame()
                
                self.column_combo['values'] = df.columns.tolist()
                if df.columns.tolist():
                    self.column_combo.set(df.columns[0])
            except Exception as e:
                # 如果是不支持的文件格式（如txt、doc），则清空列选择
                self.column_combo['values'] = []
                self.column_combo.set('')

    def select_output_file(self):
        # 根据选择的格式设置文件类型
        format_ext = self.output_format_var.get()
        if format_ext == '.xlsx':
            file_types = [('Excel文件', '*.xlsx')]
        elif format_ext == '.csv':
            file_types = [('CSV文件', '*.csv')]
        else:
            file_types = [('文本文件', '*.txt')]
        
        self.output_file = filedialog.asksaveasfilename(
            filetypes=file_types,
            defaultextension=format_ext
        )
        
        # 确保文件有正确的后缀名
        if self.output_file:
            ext = os.path.splitext(self.output_file)[1].lower()
            if not ext:
                self.output_file += format_ext
            elif ext != format_ext:
                self.output_file = os.path.splitext(self.output_file)[0] + format_ext

    def start_extraction(self):
        try:
            selected_patterns = [name for name, var in self.pattern_vars.items() 
                               if var.get()]
            
            if not selected_patterns:
                messagebox.showerror("错误", "请至少选择一个要提取的内容")
                return

            if not hasattr(self, 'input_file') or not hasattr(self, 'output_file'):
                messagebox.showerror("错误", "请选择输入和输出文件")
                return

            # 获取是否去重的设置
            remove_duplicates = self.remove_duplicates_var.get()

            mode = self.mode_var.get()
            if mode == "full":
                results = self.extractor.extract_full(
                    self.input_file, 
                    selected_patterns,
                    remove_duplicates
                )
                self.extractor.file_handler.save_results(
                    results, 
                    self.output_file, 
                    'full'
                )
            elif mode == "row":
                if not self.column_var.get():
                    messagebox.showerror("错误", "请选择要处理的列")
                    return
                results = self.extractor.extract_by_row(
                    self.input_file, 
                    self.column_var.get(), 
                    selected_patterns,
                    remove_duplicates
                )
                self.extractor.file_handler.save_results(
                    results, 
                    self.output_file, 
                    'row'
                )
            else:  # column mode
                if not self.column_var.get():
                    messagebox.showerror("错误", "请选择要处理的列")
                    return
                results = self.extractor.extract_by_column(
                    self.input_file,
                    self.column_var.get(),
                    selected_patterns,
                    remove_duplicates
                )
                self.extractor.file_handler.save_results(
                    results,
                    self.output_file,
                    'full'  # 使用全量模式的保存格式
                )

            messagebox.showinfo("成功", "提取完成！")
        except Exception as e:
            messagebox.showerror("错误", f"提取过程中出现错误：{str(e)}")

    def run(self):
        self.root.mainloop()

    def get_excel_sheets(self, file_path):
        """获取Excel文件中的所有工作表名称"""
        try:
            if file_path.endswith(('.xlsx', '.xls')):
                try:
                    # 尝试使用openpyxl
                    import openpyxl
                    wb = openpyxl.load_workbook(file_path, read_only=True)
                    return wb.sheetnames
                except Exception:
                    # 如果openpyxl失败，尝试使用xlrd
                    import xlrd
                    wb = xlrd.open_workbook(file_path)
                    return wb.sheet_names()
            return []
        except Exception:
            return []

    def on_sheet_selected(self, event=None):
        """当选择不同的工作表时更新列选择下拉框"""
        try:
            if hasattr(self, 'input_file') and self.input_file.endswith(('.xlsx', '.xls')):
                df = self.extractor.file_handler.read_excel_file(
                    self.input_file, 
                    self.sheet_var.get()
                )
                self.column_combo['values'] = df.columns.tolist()
                if df.columns.tolist():
                    self.column_combo.set(df.columns[0])
        except Exception as e:
            messagebox.showerror("错误", f"读取工作表失败：{str(e)}")

if __name__ == "__main__":
    gui = GUI()
    gui.run() 