import os
import sys
import shutil
from PyInstaller.__main__ import run

# 定义打包配置
def build_exe():
    print("开始打包每日分析研判助手...")
    
    # 检查ico文件是否存在
    icon_path = os.path.join(os.getcwd(), "ico", "9.ico")
    if not os.path.exists(icon_path):
        icon_path = os.path.join(os.getcwd(), "9.ico")
        if not os.path.exists(icon_path):
            print("警告: 未找到9.ico文件，将使用默认图标")
            icon_path = None
    
    # 创建临时目录存放打包文件
    if not os.path.exists("build_temp"):
        os.makedirs("build_temp")
    
    # 复制主程序到临时目录
    shutil.copy("测试.py", os.path.join("build_temp", "daily_analysis.py"))
    
    # 构建PyInstaller命令
    pyinstaller_args = [
        os.path.join("build_temp", "daily_analysis.py"),  # 主程序
        "--name=每日分析研判助手",                        # 程序名称
        "--onefile",                                     # 打包成单个文件
        "--noconsole",                                   # 不显示控制台
        "--clean",                                       # 清理临时文件
        "--noupx",                                       # 不使用UPX压缩
        "--win-private-assemblies",                      # 打包私有程序集
        "--noconfirm",                                   # 不确认覆盖
        "--hidden-import=pandas",                        # 隐式导入
        "--hidden-import=docx",
        "--hidden-import=PyQt5",
        "--hidden-import=re",
        "--hidden-import=datetime",
    ]
    
    # 添加图标
    if icon_path:
        pyinstaller_args.append(f"--icon={icon_path}")
    
    # 添加兼容性选项
    pyinstaller_args.extend([
        "--win-no-prefer-redirects",
        "--disable-windowed-traceback"
    ])
    
    # 运行PyInstaller
    print("正在执行打包命令...")
    run(pyinstaller_args)
    
    # 清理临时目录
    print("清理临时文件...")
    try:
        shutil.rmtree("build_temp")
    except Exception as e:
        print(f"清理临时文件时出错: {e}")
    
    # 输出打包结果
    dist_path = os.path.join(os.getcwd(), "dist", "每日分析研判助手.exe")
    if os.path.exists(dist_path):
        print(f"\n打包成功! 可执行文件位置: {dist_path}")
        print("注意: 程序不会请求管理员权限，已优化Win7系统兼容性。")
    else:
        print("\n打包可能出现问题，请检查上述输出信息。")

if __name__ == "__main__":
    # 检查PyInstaller是否已安装
    try:
        import PyInstaller
    except ImportError:
        print("错误: 未安装PyInstaller。请先运行 'pip install pyinstaller' 安装。")
        sys.exit(1)
    
    # 执行打包
    build_exe() 